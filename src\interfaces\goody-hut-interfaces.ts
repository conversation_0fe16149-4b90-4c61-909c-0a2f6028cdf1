/**
 * TypeScript Interface Definitions for GoodyHutHelper Automation
 * Frida IL2CPP Bridge Integration for Dominations Game
 */

import "frida-il2cpp-bridge";

// ============================================================================
// Core GoodyHutHelper Method Interfaces
// ============================================================================

/**
 * GoodyHutHelper IL2CPP method references
 */
export interface GoodyHutHelperMethods {
    CanCollect: Il2Cpp.Method | null;
    FinishCollect: Il2Cpp.Method | null;
    Config: Il2Cpp.Method | null;
    SellRuins: Il2Cpp.Method | null;
    Update: Il2Cpp.Method | null;
    GetRewardType: Il2Cpp.Method | null;
    GetRewardAmount: Il2Cpp.Method | null;
    GetCollectTime: Il2Cpp.Method | null;
}

/**
 * GoodyHutHelper configuration object structure
 */
export interface GoodyHutHelperConfig {
    cleanUp: boolean;
    collectTime: number;
    rewardType: number;
    rewardAmount: number;
    [key: string]: any; // Allow for additional config properties
}

// ============================================================================
// Batch Processing System Interfaces
// ============================================================================

/**
 * Individual instance tracking data
 */
export interface InstanceTracker {
    ptr: NativePointer;
    discoveryTime: number;
    retryCount: number;
    lastFailTime?: number;
    entityId?: string;
}

/**
 * Batch processing configuration
 */
export interface BatchConfig {
    batchSize: number;
    batchInterval: number;
    retryLimit: number;
    discoveryTimeout: number;
    maxWaitTime: number;
}

/**
 * Batch processing statistics
 */
export interface BatchStats {
    totalDiscoveryScans: number;
    discoveredInstances: number;
    processedInstances: number;
    failedInstances: number;
    batchNumber: number;
    isProcessingBatch: boolean;
    currentPhase: 1 | 2; // 1 = Discovery, 2 = Collection
    lastDiscoveryTime: number;
    discoveryCompleteTime: number;
}

/**
 * Batch item for processing queue
 */
export interface BatchItem {
    id: string;
    data: InstanceTracker;
    source: 'discovered' | 'retry';
}

// ============================================================================
// Automation State Management
// ============================================================================

/**
 * Overall automation statistics
 */
export interface AutomationStats {
    totalInstances: number;
    validInstances: number;
    invalidInstances: number;
    upgradeableInstances: number;
    collectionsPerformed: number;
    startTime: number;
    lastUpdateTime: number;
}

/**
 * Phase tracking for two-phase processing
 */
export interface PhaseTracker {
    currentPhase: 1 | 2;
    phaseStartTime: number;
    phaseEndTime?: number;
    phaseDuration?: number;
    discoveryComplete: boolean;
}

// ============================================================================
// IL2CPP Module and Method Discovery
// ============================================================================

/**
 * IL2CPP module information
 */
export interface IL2CPPModuleInfo {
    module: Module | null;
    name: string;
    base: NativePointer;
    size: number;
    isValid: boolean;
    loadTime: number;
}

/**
 * Method address mapping for IL2CPP methods
 */
export interface MethodAddresses {
    CanCollect: NativePointer;
    FinishCollect: NativePointer;
    Update: NativePointer;
    Config: NativePointer;
    SellRuins: NativePointer;
    GetRewardType: NativePointer;
    GetRewardAmount: NativePointer;
}

// ============================================================================
// Error Handling and Anti-debugging
// ============================================================================

/**
 * Error handling configuration
 */
export interface ErrorHandlingConfig {
    maxRetries: number;
    retryDelay: number;
    ignoreAntiDebugErrors: boolean;
    logErrorDetails: boolean;
    continueOnError: boolean;
}

/**
 * Anti-debugging error types
 */
export type AntiDebugError =
    | 'breakpoint_triggered'
    | 'access_violation'
    | 'illegal_instruction'
    | 'abort_called'
    | 'unknown_error';

/**
 * Error tracking information
 */
export interface ErrorTracker {
    errorType: AntiDebugError;
    errorMessage: string;
    occurredAt: number;
    instanceId?: string;
    methodName?: string;
    retryCount: number;
}

// ============================================================================
// EntityController Interfaces
// ============================================================================

/**
 * EntityController method references
 */
export interface EntityControllerMethods {
    IsSelected: Il2Cpp.Method | null;
    CanUpgrade: Il2Cpp.Method | null;
    GetLevel: Il2Cpp.Method | null;
    GetMaxLevel: Il2Cpp.Method | null;
    GetMaxUpgradeLevel: Il2Cpp.Method | null;
    InstantUpgrade: Il2Cpp.Method | null;
    Select: Il2Cpp.Method | null;
    Unselect: Il2Cpp.Method | null;
    GetUniqueId: Il2Cpp.Method | null;
    IsUpgrading: Il2Cpp.Method | null;
    GetUpgradeTime: Il2Cpp.Method | null;
    GetUpgradeCost: Il2Cpp.Method | null;
}

/**
 * EntityController upgrade tracking
 */
export interface EntityUpgradeTracker {
    entityId: string;
    levelBefore: number;
    upgradeStartTime: number;
    upgradeCallCount: number;
    lastLevelCheck: number;
    expectedLevel: number;
}

/**
 * EntityController automation statistics
 */
export interface EntityControllerStats {
    totalInstances: number;
    selectedInstances: number;
    upgradeableInstances: number;
    upgradesPerformed: number;
    startTime: number;
    lastUpdateTime: number;
}

/**
 * EntityController upgrade validation result
 */
export interface UpgradeValidationResult {
    canUpgrade: boolean;
    isUpgrading: boolean;
    currentLevel: number;
    maxLevel: number;
    reason: string;
}

/**
 * EntityController upgrade result
 */
export interface EntityUpgradeResult {
    success: boolean;
    newLevel: number;
    waitTime: number;
    upgradeCount: number;
}

/**
 * EntityController configuration
 */
export interface EntityControllerConfig {
    maxRetries: number;
    retryDelay: number;
    upgradeTimeout: number;
    batchSize: number;
    batchDelay: number;
    enableLogging: boolean;
    logLevel: 'minimal' | 'normal' | 'verbose';
}

// ============================================================================
// RPC Export Function Signatures
// ============================================================================

/**
 * RPC export function signatures for manual control
 */
export interface RPCExports {
    getBatchStatus: () => void;
    clearProcessedInstances: () => void;
    clearFailedInstances: () => void;
    forceBatchProcess: () => void;
    forceCompleteDiscovery: () => void;
    resetToDiscovery: () => void;
    setModule: (moduleName: string) => boolean;
    listLargeModules: () => Array<{name: string, size: number, sizeMB: string, base: string}>;
    getStats: () => BatchStats & AutomationStats;
    manualCollect: (address: string) => boolean;
}

// ============================================================================
// Configuration and Initialization
// ============================================================================

/**
 * Main configuration object for GoodyHutHelper automation
 */
export interface GoodyHutAutomationConfig {
    batch: BatchConfig;
    errorHandling: ErrorHandlingConfig;
    module: {
        name: string;
        maxWaitTime: number;
        checkInterval: number;
    };
    collection: {
        realTimeEnabled: boolean;
        batchEnabled: boolean;
        cleanupEnabled: boolean;
        maxCollections: number;
    };
    logging: {
        debugLevel: 'minimal' | 'normal' | 'verbose';
        logBatchDetails: boolean;
        logErrorDetails: boolean;
    };
    entityController?: EntityControllerConfig;
}

/**
 * Initialization result
 */
export interface InitializationResult {
    success: boolean;
    module: IL2CPPModuleInfo;
    methods: GoodyHutHelperMethods;
    addresses: MethodAddresses;
    errorMessage?: string;
}

// ============================================================================
// Memory and Pointer Validation
// ============================================================================

/**
 * Memory validation result
 */
export interface MemoryValidationResult {
    isValid: boolean;
    canRead: boolean;
    canWrite: boolean;
    errorMessage?: string;
}

/**
 * Instance validation result
 */
export interface InstanceValidationResult {
    isValid: boolean;
    hasValidPointer: boolean;
    hasValidMethods: boolean;
    canCollect: boolean;
    reason: string;
}

// ============================================================================
// Collection Processing Results
// ============================================================================

/**
 * Collection attempt result
 */
export interface CollectionResult {
    success: boolean;
    instanceId: string;
    levelBefore?: number;
    levelAfter?: number;
    rewardType?: number;
    rewardAmount?: number;
    processingTime: number;
    errorMessage?: string;
    cleanupSet: boolean;
}

/**
 * Batch processing result
 */
export interface BatchProcessingResult {
    batchNumber: number;
    totalProcessed: number;
    successCount: number;
    failureCount: number;
    processingTime: number;
    results: CollectionResult[];
}