📦
116812 /src/goody-hut-agent.js
✄
var h=function(t,e,r,s){var n=arguments.length,o=n<3?e:s===null?s=Object.getOwnPropertyDescriptor(e,r):s,i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(t,e,r,s);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(n<3?i(o):n>3?i(e,r,o):i(e,r))||o);return n>3&&o&&Object.defineProperty(e,r,o),o},u;(function(t){t.application={get dataPath(){return e("get_persistentDataPath")},get identifier(){return e("get_identifier")??e("get_bundleIdentifier")??Process.mainModule.name},get version(){return e("get_version")??j(t.module).toString(16)}},T(t,"unityVersion",()=>{try{let s=t.$config.unityVersion??e("get_unityVersion");if(s!=null)return s}catch{}let r="69 6c 32 63 70 70";for(let s of t.module.enumerateRanges("r--").concat(Process.getRangeByAddress(t.module.base)))for(let{address:n}of Memory.scanSync(s.base,s.size,r)){for(;n.readU8()!=0;)n=n.sub(1);let o=E.find(n.add(1).readCString());if(o!=null)return o}m("couldn't determine the Unity version, please specify it manually")},l),T(t,"unityVersionIsBelow201830",()=>E.lt(t.unityVersion,"2018.3.0"),l),T(t,"unityVersionIsBelow202120",()=>E.lt(t.unityVersion,"2021.2.0"),l);function e(r){let s=t.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+r)),n=new NativeFunction(s,"pointer",[]);return n.isNull()?null:new t.String(n()).asNullable()?.content??null}})(u||(u={}));var u;(function(t){function e(r,s){let n={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},o=typeof r=="boolean"?"System.Boolean":typeof r=="number"?n[s??"int32"]:r instanceof Int64?"System.Int64":r instanceof UInt64?"System.UInt64":r instanceof NativePointer?n[s??"intptr"]:m(`Cannot create boxed primitive using value of type '${typeof r}'`),i=t.corlib.class(o??m(`Unknown primitive type name '${s}'`)).alloc();return(i.tryField("m_value")??i.tryField("_pointer")??m(`Could not find primitive field in class '${o}'`)).value=r,i}t.boxed=e})(u||(u={}));var u;(function(t){t.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(u||(u={}));var u;(function(t){function e(i,a){i=i??`${t.application.identifier}_${t.application.version}.cs`,a=a??t.application.dataPath??Process.getCurrentDir(),n(a);let c=`${a}/${i}`,d=new File(c,"w");for(let g of t.domain.assemblies){P(`dumping ${g.name}...`);for(let f of g.image.classes)d.write(`${f}

`)}d.flush(),d.close(),O(`dump saved to ${c}`),o()}t.dump=e;function r(i,a=!1){i=i??`${t.application.dataPath??Process.getCurrentDir()}/${t.application.identifier}_${t.application.version}`,!a&&s(i)&&m(`directory ${i} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let c of t.domain.assemblies){P(`dumping ${c.name}...`);let d=`${i}/${c.name.replaceAll(".","/")}.cs`;n(d.substring(0,d.lastIndexOf("/")));let g=new File(d,"w");for(let f of c.image.classes)g.write(`${f}

`);g.flush(),g.close()}O(`dump saved to ${i}`),o()}t.dumpTree=r;function s(i){return t.corlib.class("System.IO.Directory").method("Exists").invoke(t.string(i))}function n(i){t.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(t.string(i))}function o(){x("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(u||(u={}));var u;(function(t){function e(r="current"){let s=t.exports.threadGetCurrent();return Interceptor.attach(t.module.getExportByName("__cxa_throw"),function(n){r=="current"&&!t.exports.threadGetCurrent().equals(s)||P(new t.Object(n[0].readPointer()))})}t.installExceptionListener=e})(u||(u={}));var u;(function(t){t.exports={get alloc(){return e("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return e("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return e("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return e("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return e("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return e("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return e("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return e("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return e("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return e("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return e("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return e("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return e("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return e("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return e("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return e("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return e("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return e("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return e("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return e("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return e("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return e("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return e("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return e("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return e("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return e("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return e("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return e("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return e("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return e("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return e("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return e("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return e("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return e("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return e("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return e("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return e("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return e("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return e("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return e("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return e("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return e("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return e("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return e("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return e("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return e("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return e("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return e("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return e("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return e("il2cpp_free","void",["pointer"])},get gcCollect(){return e("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return e("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return e("il2cpp_gc_disable","void",[])},get gcEnable(){return e("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return e("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return e("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return e("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return e("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return e("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return e("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return e("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return e("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return e("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return e("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return e("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return e("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return e("il2cpp_stop_gc_world","void",[])},get getCorlib(){return e("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return e("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return e("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return e("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return e("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return e("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return e("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return e("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return e("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return e("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return e("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return e("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return e("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return e("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return e("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return e("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return e("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return e("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return e("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return e("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return e("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return e("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return e("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return e("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return e("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return e("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return e("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return e("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return e("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return e("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return e("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return e("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return e("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return e("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return e("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return e("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return e("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return e("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return e("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return e("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return e("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return e("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return e("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return e("il2cpp_string_length","int32",["pointer"])},get stringNew(){return e("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return e("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return e("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return e("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return e("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return e("il2cpp_thread_current","pointer",[])},get threadIsVm(){return e("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return e("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return e("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return e("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return e("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return e("il2cpp_type_get_type","int",["pointer"])}},U(t.exports,l),T(t,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),l);function e(r,s,n){let o=t.$config.exports?.[r]?.()??t.module.findExportByName(r)??t.memorySnapshotExports[r],i=new NativeFunction(o??NULL,s,n);return i.isNull()?new Proxy(i,{get(a,c){let d=a[c];return typeof d=="function"?d.bind(a):d},apply(){o==null?m(`couldn't resolve export ${r}`):o.isNull()&&m(`export ${r} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):i}})(u||(u={}));var u;(function(t){function e(s){return n=>n instanceof t.Class?s.isAssignableFrom(n):s.isAssignableFrom(n.class)}t.is=e;function r(s){return n=>n instanceof t.Class?n.equals(s):n.class.equals(s)}t.isExactly=r})(u||(u={}));var u;(function(t){t.gc={get heapSize(){return t.exports.gcGetHeapSize()},get isEnabled(){return!t.exports.gcIsDisabled()},get isIncremental(){return!!t.exports.gcIsIncremental()},get maxTimeSlice(){return t.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return t.exports.gcGetUsedSize()},set isEnabled(e){e?t.exports.gcEnable():t.exports.gcDisable()},set maxTimeSlice(e){t.exports.gcSetMaxTimeSlice(e)},choose(e){let r=[],s=(o,i)=>{for(let a=0;a<i;a++)r.push(new t.Object(o.add(a*Process.pointerSize).readPointer()))},n=new NativeCallback(s,"void",["pointer","int","pointer"]);if(t.unityVersionIsBelow202120){let o=new NativeCallback(()=>{},"void",[]),i=t.exports.livenessCalculationBegin(e,0,n,NULL,o,o);t.exports.livenessCalculationFromStatics(i),t.exports.livenessCalculationEnd(i)}else{let o=(c,d)=>!c.isNull()&&d.compare(0)==0?(t.free(c),NULL):t.alloc(d),i=new NativeCallback(o,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let a=t.exports.livenessAllocateStruct(e,0,n,NULL,i);t.exports.livenessCalculationFromStatics(a),t.exports.livenessFinalize(a),this.startWorld(),t.exports.livenessFreeStruct(a)}return r},collect(e){t.exports.gcCollect(e<0?0:e>2?2:e)},collectALittle(){t.exports.gcCollectALittle()},startWorld(){return t.exports.gcStartWorld()},startIncrementalCollection(){return t.exports.gcStartIncrementalCollection()},stopWorld(){return t.exports.gcStopWorld()}}})(u||(u={}));var z;(function(t){T(t,"apiLevel",()=>{let r=e("ro.build.version.sdk");return r?parseInt(r):null},l);function e(r){let s=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(s){let n=new NativeFunction(s,"void",["pointer","pointer"]),o=Memory.alloc(92).writePointer(NULL);return n(Memory.allocUtf8String(r),o),o.readCString()??void 0}}})(z||(z={}));function m(t){let e=new Error(t);throw e.name="Il2CppError",e.stack=e.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),e}function x(t){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${t}`)}function O(t){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${t}`)}function P(t){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${t}`)}function U(t,e,r=Object.getOwnPropertyDescriptors(t)){for(let s in r)r[s]=e(t,s,r[s]);return Object.defineProperties(t,r),t}function T(t,e,r,s){globalThis.Object.defineProperty(t,e,s?.(t,e,{get:r,configurable:!0})??{get:r,configurable:!0})}function C(t){let e=3735928559,r=1103547991;for(let s=0,n;s<t.length;s++)n=t.charCodeAt(s),e=Math.imul(e^n,2654435761),r=Math.imul(r^n,1597334677);return e=Math.imul(e^e>>>16,2246822507),e^=Math.imul(r^r>>>13,3266489909),r=Math.imul(r^r>>>16,2246822507),r^=Math.imul(e^e>>>13,3266489909),4294967296*(2097151&r)+(e>>>0)}function j(t){return C(t.enumerateExports().sort((e,r)=>e.name.localeCompare(r.name)).map(e=>e.name+e.address.sub(t.base)).join(""))}function l(t,e,r){let s=r.get;if(!s)throw new Error("@lazy can only be applied to getter accessors");return r.get=function(){let n=s.call(this);return Object.defineProperty(this,e,{value:n,configurable:r.configurable,enumerable:r.enumerable,writable:!1}),n},r}var b=class{handle;constructor(e){e instanceof NativePointer?this.handle=e:this.handle=e.handle}equals(e){return this.handle.equals(e.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function I(t){return Object.keys(t).reduce((e,r)=>(e[e[r]]=r,e),t)}NativePointer.prototype.offsetOf=function(t,e){e??=512;for(let r=0;e>0?r<e:r<-e;r++)if(t(e>0?this.add(r):this.sub(r)))return r;return null};function w(t){let e=[],r=Memory.alloc(Process.pointerSize),s=t(r);for(;!s.isNull();)e.push(s),s=t(r);return e}function B(t){let e=Memory.alloc(Process.pointerSize),r=t(e);if(r.isNull())return[];let s=new Array(e.readInt());for(let n=0;n<s.length;n++)s[n]=r.add(n*Process.pointerSize).readPointer();return s}function $(t){return new Proxy(t,{cache:new Map,construct(e,r){let s=r[0].toUInt32();return this.cache.has(s)||this.cache.set(s,new e(r[0])),this.cache.get(s)}})}var E;(function(t){let e=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function r(i){return i?.match(e)?.[0]}t.find=r;function s(i,a){return o(i,a)>=0}t.gte=s;function n(i,a){return o(i,a)<0}t.lt=n;function o(i,a){let c=i.match(e),d=a.match(e);for(let g=1;g<=3;g++){let f=Number(c?.[g]??-1),p=Number(d?.[g]??-1);if(f>p)return 1;if(f<p)return-1}return 0}})(E||(E={}));var u;(function(t){function e(a=Process.pointerSize){return t.exports.alloc(a)}t.alloc=e;function r(a){return t.exports.free(a)}t.free=r;function s(a,c){switch(c.enumValue){case t.Type.Enum.BOOLEAN:return!!a.readS8();case t.Type.Enum.BYTE:return a.readS8();case t.Type.Enum.UBYTE:return a.readU8();case t.Type.Enum.SHORT:return a.readS16();case t.Type.Enum.USHORT:return a.readU16();case t.Type.Enum.INT:return a.readS32();case t.Type.Enum.UINT:return a.readU32();case t.Type.Enum.CHAR:return a.readU16();case t.Type.Enum.LONG:return a.readS64();case t.Type.Enum.ULONG:return a.readU64();case t.Type.Enum.FLOAT:return a.readFloat();case t.Type.Enum.DOUBLE:return a.readDouble();case t.Type.Enum.NINT:case t.Type.Enum.NUINT:return a.readPointer();case t.Type.Enum.POINTER:return new t.Pointer(a.readPointer(),c.class.baseType);case t.Type.Enum.VALUE_TYPE:return new t.ValueType(a,c);case t.Type.Enum.OBJECT:case t.Type.Enum.CLASS:return new t.Object(a.readPointer());case t.Type.Enum.GENERIC_INSTANCE:return c.class.isValueType?new t.ValueType(a,c):new t.Object(a.readPointer());case t.Type.Enum.STRING:return new t.String(a.readPointer());case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return new t.Array(a.readPointer())}m(`couldn't read the value from ${a} using an unhandled or unknown type ${c.name} (${c.enumValue}), please file an issue`)}t.read=s;function n(a,c,d){switch(d.enumValue){case t.Type.Enum.BOOLEAN:return a.writeS8(+c);case t.Type.Enum.BYTE:return a.writeS8(c);case t.Type.Enum.UBYTE:return a.writeU8(c);case t.Type.Enum.SHORT:return a.writeS16(c);case t.Type.Enum.USHORT:return a.writeU16(c);case t.Type.Enum.INT:return a.writeS32(c);case t.Type.Enum.UINT:return a.writeU32(c);case t.Type.Enum.CHAR:return a.writeU16(c);case t.Type.Enum.LONG:return a.writeS64(c);case t.Type.Enum.ULONG:return a.writeU64(c);case t.Type.Enum.FLOAT:return a.writeFloat(c);case t.Type.Enum.DOUBLE:return a.writeDouble(c);case t.Type.Enum.NINT:case t.Type.Enum.NUINT:case t.Type.Enum.POINTER:case t.Type.Enum.STRING:case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return a.writePointer(c);case t.Type.Enum.VALUE_TYPE:return Memory.copy(a,c,d.class.valueTypeSize),a;case t.Type.Enum.OBJECT:case t.Type.Enum.CLASS:case t.Type.Enum.GENERIC_INSTANCE:return c instanceof t.ValueType?(Memory.copy(a,c,d.class.valueTypeSize),a):a.writePointer(c)}m(`couldn't write value ${c} to ${a} using an unhandled or unknown type ${d.name} (${d.enumValue}), please file an issue`)}t.write=n;function o(a,c){if(globalThis.Array.isArray(a)){let d=Memory.alloc(c.class.valueTypeSize),g=c.class.fields.filter(f=>!f.isStatic);for(let f=0;f<g.length;f++){let p=o(a[f],g[f].type);n(d.add(g[f].offset).sub(t.Object.headerSize),p,g[f].type)}return new t.ValueType(d,c)}else if(a instanceof NativePointer){if(c.isByReference)return new t.Reference(a,c);switch(c.enumValue){case t.Type.Enum.POINTER:return new t.Pointer(a,c.class.baseType);case t.Type.Enum.STRING:return new t.String(a);case t.Type.Enum.CLASS:case t.Type.Enum.GENERIC_INSTANCE:case t.Type.Enum.OBJECT:return new t.Object(a);case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return new t.Array(a);default:return a}}else return c.enumValue==t.Type.Enum.BOOLEAN?!!a:c.enumValue==t.Type.Enum.VALUE_TYPE&&c.class.isEnum?o([a],c):a}t.fromFridaValue=o;function i(a){if(typeof a=="boolean")return+a;if(a instanceof t.ValueType){if(a.type.class.isEnum)return a.field("value__").value;{let c=a.type.class.fields.filter(d=>!d.isStatic).map(d=>i(d.bind(a).value));return c.length==0?[0]:c}}else return a}t.toFridaValue=i})(u||(u={}));var u;(function(t){T(t,"module",()=>r()??m("Could not find IL2CPP module"));async function e(n=!1){let o=r()??await new Promise(i=>{let[a,c]=s(),d=setTimeout(()=>{x(`after 10 seconds, IL2CPP module '${a}' has not been loaded yet, is the app running?`)},1e4),g=Process.attachModuleObserver({onAdded(f){(f.name==a||c&&f.name==c)&&(clearTimeout(d),setImmediate(()=>{i(f),g.detach()}))}})});return Reflect.defineProperty(t,"module",{value:o}),t.exports.getCorlib().isNull()?await new Promise(i=>{let a=Interceptor.attach(t.exports.initialize,{onLeave(){a.detach(),n?i(!0):setImmediate(()=>i(!1))}})}):!1}t.initialize=e;function r(){let[n,o]=s();return Process.findModuleByName(n)??Process.findModuleByName(o??n)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function s(){if(t.$config.moduleName)return[t.$config.moduleName];switch(Process.platform){case"linux":return[z.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}m(`${Process.platform} is not supported yet`)}})(u||(u={}));var u;(function(t){async function e(r,s="bind"){let n=null;try{let o=await t.initialize(s=="main");if(s=="main"&&!o)return e(()=>t.mainThread.schedule(r),"free");t.currentThread==null&&(n=t.domain.attach()),s=="bind"&&n!=null&&Script.bindWeak(globalThis,()=>n?.detach());let i=r();return i instanceof Promise?await i:i}catch(o){return Script.nextTick(i=>{throw i},o),Promise.reject(o)}finally{s=="free"&&n!=null&&n.detach()}}t.perform=e})(u||(u={}));var u;(function(t){class e{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let o=`
${this.#e.buffer.join(`
`)}
`;if(this.#u)P(o);else{let i=C(o);this.#e.history.has(i)||(this.#e.history.add(i),P(o))}this.#e.buffer.length=0}}};#h=t.mainThread.id;#u=!1;#d;#c=[];#l;#t;#s;#n;#r;#o;#i;#a;constructor(o){this.#d=o}thread(o){return this.#h=o.id,this}verbose(o){return this.#u=o,this}domain(){return this.#l=t.domain,this}assemblies(...o){return this.#t=o,this}classes(...o){return this.#s=o,this}methods(...o){return this.#n=o,this}filterAssemblies(o){return this.#r=o,this}filterClasses(o){return this.#o=o,this}filterMethods(o){return this.#i=o,this}filterParameters(o){return this.#a=o,this}and(){let o=p=>{if(this.#a==null){this.#c.push(p);return}for(let y of p.parameters)if(this.#a(y)){this.#c.push(p);break}},i=p=>{for(let y of p)o(y)},a=p=>{if(this.#i==null){i(p.methods);return}for(let y of p.methods)this.#i(y)&&o(y)},c=p=>{for(let y of p)a(y)},d=p=>{if(this.#o==null){c(p.image.classes);return}for(let y of p.image.classes)this.#o(y)&&a(y)},g=p=>{for(let y of p)d(y)},f=p=>{if(this.#r==null){g(p.assemblies);return}for(let y of p.assemblies)this.#r(y)&&d(y)};return this.#n?i(this.#n):this.#s?c(this.#s):this.#t?g(this.#t):this.#l&&f(this.#l),this.#t=void 0,this.#s=void 0,this.#n=void 0,this.#r=void 0,this.#o=void 0,this.#i=void 0,this.#a=void 0,this}attach(){for(let o of this.#c)if(!o.virtualAddress.isNull())try{this.#d(o,this.#e,this.#h)}catch(i){switch(i.message){case/unable to intercept function at \w+; please file a bug/.exec(i.message)?.input:case"already replaced this function":break;default:throw i}}}}t.Tracer=e;function r(n=!1){let o=()=>(a,c,d)=>{let g=a.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(a.virtualAddress,{onEnter(){this.threadId==d&&c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==d&&(c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m`),c.flush())}})},i=()=>(a,c,d)=>{let g=a.relativeVirtualAddress.toString(16).padStart(8,"0"),f=+!a.isStatic|+t.unityVersionIsBelow201830,p=function(...v){if(this.threadId==d){let H=a.isStatic?void 0:new t.Parameter("this",-1,a.class.type),L=H?[H].concat(a.parameters):a.parameters;c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m(${L.map(R=>`\x1B[32m${R.name}\x1B[0m = \x1B[31m${t.fromFridaValue(v[R.position+f],R.type)}\x1B[0m`).join(", ")})`)}let G=a.nativeFunction(...v);return this.threadId==d&&(c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m${G==null?"":` = \x1B[36m${t.fromFridaValue(G,a.returnType)}`}\x1B[0m`),c.flush()),G};a.revert();let y=new NativeCallback(p,a.returnType.fridaAlias,a.fridaSignature);Interceptor.replace(a.virtualAddress,y)};return new t.Tracer(n?i():o())}t.trace=r;function s(n){let o=t.domain.assemblies.flatMap(c=>c.image.classes.flatMap(d=>d.methods.filter(g=>!g.virtualAddress.isNull()))).sort((c,d)=>c.virtualAddress.compare(d.virtualAddress)),i=c=>{let d=0,g=o.length-1;for(;d<=g;){let f=Math.floor((d+g)/2),p=o[f].virtualAddress.compare(c);if(p==0)return o[f];p>0?g=f-1:d=f+1}return o[g]},a=()=>(c,d,g)=>{Interceptor.attach(c.virtualAddress,function(){if(this.threadId==g){let f=globalThis.Thread.backtrace(this.context,n);f.unshift(c.virtualAddress);for(let p of f)if(p.compare(t.module.base)>0&&p.compare(t.module.base.add(t.module.size))<0){let y=i(p);if(y){let v=p.sub(y.virtualAddress);v.compare(4095)<0&&d.buffer.push(`\x1B[2m0x${y.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${v.toString(16).padStart(3,"0")}\x1B[0m ${y.class.type.name}::\x1B[1m${y.name}\x1B[0m`)}}d.flush()}})};return new t.Tracer(a())}t.backtrace=s})(u||(u={}));var u;(function(t){class e extends b{static get headerSize(){return t.corlib.class("System.Array").instanceSize}get elements(){let o=t.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(i=>i.readS16()==118)??m("couldn't find the elements offset in the native array struct");return T(t.Array.prototype,"elements",function(){return new t.Pointer(this.handle.add(o),this.elementType)},l),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return t.exports.arrayGetLength(this)}get object(){return new t.Object(this)}get(n){return(n<0||n>=this.length)&&m(`cannot get element at index ${n} as the array length is ${this.length}`),this.elements.get(n)}set(n,o){(n<0||n>=this.length)&&m(`cannot set element at index ${n} as the array length is ${this.length}`),this.elements.set(n,o)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let n=0;n<this.length;n++)yield this.elements.get(n)}}h([l],e.prototype,"elementSize",null),h([l],e.prototype,"elementType",null),h([l],e.prototype,"length",null),h([l],e.prototype,"object",null),h([l],e,"headerSize",null),t.Array=e;function r(s,n){let o=typeof n=="number"?n:n.length,i=new t.Array(t.exports.arrayNew(s,o));return globalThis.Array.isArray(n)&&i.elements.write(n),i}t.array=r})(u||(u={}));var u;(function(t){let e=class extends b{get image(){if(t.exports.assemblyGetImage.isNull()){let s=this.object.tryMethod("GetType",1)?.invoke(t.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??m(`couldn't find the runtime module object of assembly ${this.name}`);return new t.Image(s.field("_impl").value)}return new t.Image(t.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let s of t.domain.object.method("GetAssemblies",1).invoke(!1))if(s.field("_mono_assembly").value.equals(this))return s;m("couldn't find the object of the native assembly struct")}};h([l],e.prototype,"name",null),h([l],e.prototype,"object",null),e=h([$],e),t.Assembly=e})(u||(u={}));var u;(function(t){let e=class extends b{get actualInstanceSize(){let s=t.corlib.class("System.String"),n=s.handle.offsetOf(o=>o.readInt()==s.instanceSize-2)??m("couldn't find the actual instance size offset in the native class struct");return T(t.Class.prototype,"actualInstanceSize",function(){return this.handle.add(n).readS32()},l),this.actualInstanceSize}get arrayClass(){return new t.Class(t.exports.classGetArrayClass(this,1))}get arrayElementSize(){return t.exports.classGetArrayElementSize(this)}get assemblyName(){return t.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new t.Class(t.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new t.Type(t.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new t.Class(t.exports.classGetElementClass(this)).asNullable()}get fields(){return w(s=>t.exports.classGetFields(this,s)).map(s=>new t.Field(s))}get flags(){return t.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let s=this.image.tryClass(this.fullName)?.asNullable();return s?.equals(this)?null:s??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let s=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(s).map(n=>new t.Class(t.exports.classFromObject(n)))}get hasReferences(){return!!t.exports.classHasReferences(this)}get hasStaticConstructor(){let s=this.tryMethod(".cctor");return s!=null&&!s.virtualAddress.isNull()}get image(){return new t.Image(t.exports.classGetImage(this))}get instanceSize(){return t.exports.classGetInstanceSize(this)}get isAbstract(){return!!t.exports.classIsAbstract(this)}get isBlittable(){return!!t.exports.classIsBlittable(this)}get isEnum(){return!!t.exports.classIsEnum(this)}get isGeneric(){return!!t.exports.classIsGeneric(this)}get isInflated(){return!!t.exports.classIsInflated(this)}get isInterface(){return!!t.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!t.exports.classIsValueType(this)}get interfaces(){return w(s=>t.exports.classGetInterfaces(this,s)).map(s=>new t.Class(s))}get methods(){return w(s=>t.exports.classGetMethods(this,s)).map(s=>new t.Method(s))}get name(){return t.exports.classGetName(this).readUtf8String()}get namespace(){return t.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return w(s=>t.exports.classGetNestedClasses(this,s)).map(s=>new t.Class(s))}get parent(){return new t.Class(t.exports.classGetParent(this)).asNullable()}get pointerClass(){return new t.Class(t.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let s=0,n=this.name;for(let o=this.name.length-1;o>0;o--){let i=n[o];if(i=="]")s++;else{if(i=="["||s==0)break;if(i==",")s++;else break}}return s}get staticFieldsData(){return t.exports.classGetStaticFieldData(this)}get valueTypeSize(){return t.exports.classGetValueTypeSize(this,NULL)}get type(){return new t.Type(t.exports.classGetType(this))}alloc(){return new t.Object(t.exports.objectNew(this))}field(s){return this.tryField(s)??m(`couldn't find field ${s} in class ${this.type.name}`)}*hierarchy(s){let n=s?.includeCurrent??!0?this:this.parent;for(;n;)yield n,n=n.parent}inflate(...s){this.isGeneric||m(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=s.length&&m(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${s.length}`);let n=s.map(a=>a.type.object),o=t.array(t.corlib.class("System.Type"),n),i=this.type.object.method("MakeGenericType",1).invoke(o);return new t.Class(t.exports.classFromObject(i))}initialize(){return t.exports.classInitialize(this),this}isAssignableFrom(s){return!!t.exports.classIsAssignableFrom(this,s)}isSubclassOf(s,n){return!!t.exports.classIsSubclassOf(this,s,+n)}method(s,n=-1){return this.tryMethod(s,n)??m(`couldn't find method ${s} in class ${this.type.name}`)}nested(s){return this.tryNested(s)??m(`couldn't find nested class ${s} in class ${this.type.name}`)}new(){let s=this.alloc(),n=Memory.alloc(Process.pointerSize);t.exports.objectInitialize(s,n);let o=n.readPointer();return o.isNull()||m(new t.Object(o).toString()),s}tryField(s){return new t.Field(t.exports.classGetFieldFromName(this,Memory.allocUtf8String(s))).asNullable()}tryMethod(s,n=-1){return new t.Method(t.exports.classGetMethodFromName(this,Memory.allocUtf8String(s),n)).asNullable()}tryNested(s){return this.nestedClasses.find(n=>n.name==s)}toString(){let s=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${s?` : ${s.map(n=>n?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(s){let n=new NativeCallback(o=>s(new t.Class(o)),"void",["pointer","pointer"]);return t.exports.classForEach(n,NULL)}};h([l],e.prototype,"arrayClass",null),h([l],e.prototype,"arrayElementSize",null),h([l],e.prototype,"assemblyName",null),h([l],e.prototype,"declaringClass",null),h([l],e.prototype,"baseType",null),h([l],e.prototype,"elementClass",null),h([l],e.prototype,"fields",null),h([l],e.prototype,"flags",null),h([l],e.prototype,"fullName",null),h([l],e.prototype,"generics",null),h([l],e.prototype,"hasReferences",null),h([l],e.prototype,"hasStaticConstructor",null),h([l],e.prototype,"image",null),h([l],e.prototype,"instanceSize",null),h([l],e.prototype,"isAbstract",null),h([l],e.prototype,"isBlittable",null),h([l],e.prototype,"isEnum",null),h([l],e.prototype,"isGeneric",null),h([l],e.prototype,"isInflated",null),h([l],e.prototype,"isInterface",null),h([l],e.prototype,"isValueType",null),h([l],e.prototype,"interfaces",null),h([l],e.prototype,"methods",null),h([l],e.prototype,"name",null),h([l],e.prototype,"namespace",null),h([l],e.prototype,"nestedClasses",null),h([l],e.prototype,"parent",null),h([l],e.prototype,"pointerClass",null),h([l],e.prototype,"rank",null),h([l],e.prototype,"staticFieldsData",null),h([l],e.prototype,"valueTypeSize",null),h([l],e.prototype,"type",null),e=h([$],e),t.Class=e})(u||(u={}));var u;(function(t){function e(r,s){let n=t.corlib.class("System.Delegate"),o=t.corlib.class("System.MulticastDelegate");n.isAssignableFrom(r)||m(`cannot create a delegate for ${r.type.name} as it's a non-delegate class`),(r.equals(n)||r.equals(o))&&m(`cannot create a delegate for neither ${n.type.name} nor ${o.type.name}, use a subclass instead`);let i=r.alloc(),a=i.handle.toString(),c=i.tryMethod("Invoke")??m(`cannot create a delegate for ${r.type.name}, there is no Invoke method`);i.method(".ctor").invoke(i,c.handle);let d=c.wrap(s);return i.field("method_ptr").value=d,i.field("invoke_impl").value=d,t._callbacksToKeepAlive[a]=d,i}t.delegate=e,t._callbacksToKeepAlive={}})(u||(u={}));var u;(function(t){let e=class extends b{get assemblies(){let s=B(n=>t.exports.domainGetAssemblies(this,n));if(s.length==0){let n=this.object.method("GetAssemblies").overload().invoke();s=globalThis.Array.from(n).map(o=>o.field("_mono_assembly").value)}return s.map(n=>new t.Assembly(n))}get object(){return t.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(s){return this.tryAssembly(s)??m(`couldn't find assembly ${s}`)}attach(){return new t.Thread(t.exports.threadAttach(this))}tryAssembly(s){return new t.Assembly(t.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(s))).asNullable()}};h([l],e.prototype,"assemblies",null),h([l],e.prototype,"object",null),e=h([$],e),t.Domain=e,T(t,"domain",()=>new t.Domain(t.exports.domainGet()),l)})(u||(u={}));var u;(function(t){class e extends b{get class(){return new t.Class(t.exports.fieldGetClass(this))}get flags(){return t.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let s=t.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return T(t.Field.prototype,"isThreadStatic",function(){return this.offset==s},l),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return t.exports.fieldGetName(this).readUtf8String()}get offset(){return t.exports.fieldGetOffset(this)}get type(){return new t.Type(t.exports.fieldGetType(this))}get value(){this.isStatic||m(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let s=Memory.alloc(Process.pointerSize);return t.exports.fieldGetStaticValue(this.handle,s),t.read(s,this.type)}set value(s){this.isStatic||m(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&m(`cannot write the value of field ${this.name} as it's thread static or literal`);let n=s instanceof t.Object&&this.type.class.isValueType?s.unbox():s instanceof b?s.handle:s instanceof NativePointer?s:t.write(Memory.alloc(this.type.class.valueTypeSize),s,this.type);t.exports.fieldSetStaticValue(this.handle,n)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?t.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(s){this.isStatic&&m(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let n=this.offset-(s instanceof t.ValueType?t.Object.headerSize:0);return new Proxy(this,{get(o,i){return i=="value"?t.read(s.handle.add(n),o.type):Reflect.get(o,i)},set(o,i,a){return i=="value"?(t.write(s.handle.add(n),a,o.type),!0):Reflect.set(o,i,a)}})}}h([l],e.prototype,"class",null),h([l],e.prototype,"flags",null),h([l],e.prototype,"isLiteral",null),h([l],e.prototype,"isStatic",null),h([l],e.prototype,"isThreadStatic",null),h([l],e.prototype,"modifier",null),h([l],e.prototype,"name",null),h([l],e.prototype,"offset",null),h([l],e.prototype,"type",null),t.Field=e})(u||(u={}));var u;(function(t){class e{handle;constructor(s){this.handle=s}get target(){return new t.Object(t.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return t.exports.gcHandleFree(this.handle)}}t.GCHandle=e})(u||(u={}));var u;(function(t){let e=class extends b{get assembly(){return new t.Assembly(t.exports.imageGetAssembly(this))}get classCount(){return t.unityVersionIsBelow201830?this.classes.length:t.exports.imageGetClassCount(this)}get classes(){if(t.unityVersionIsBelow201830){let s=this.assembly.object.method("GetTypes").invoke(!1),n=globalThis.Array.from(s,i=>new t.Class(t.exports.classFromObject(i))),o=this.tryClass("<Module>");return o&&n.unshift(o),n}else return globalThis.Array.from(globalThis.Array(this.classCount),(s,n)=>new t.Class(t.exports.imageGetClass(this,n)))}get name(){return t.exports.imageGetName(this).readUtf8String()}class(s){return this.tryClass(s)??m(`couldn't find class ${s} in assembly ${this.name}`)}tryClass(s){let n=s.lastIndexOf("."),o=Memory.allocUtf8String(n==-1?"":s.slice(0,n)),i=Memory.allocUtf8String(s.slice(n+1));return new t.Class(t.exports.classFromName(this,o,i)).asNullable()}};h([l],e.prototype,"assembly",null),h([l],e.prototype,"classCount",null),h([l],e.prototype,"classes",null),h([l],e.prototype,"name",null),e=h([$],e),t.Image=e,T(t,"corlib",()=>new t.Image(t.exports.getCorlib()),l)})(u||(u={}));var u;(function(t){class e extends b{static capture(){return new t.MemorySnapshot}constructor(n=t.exports.memorySnapshotCapture()){super(n)}get classes(){return w(n=>t.exports.memorySnapshotGetClasses(this,n)).map(n=>new t.Class(n))}get objects(){return B(n=>t.exports.memorySnapshotGetObjects(this,n)).filter(n=>!n.isNull()).map(n=>new t.Object(n))}free(){t.exports.memorySnapshotFree(this)}}h([l],e.prototype,"classes",null),h([l],e.prototype,"objects",null),t.MemorySnapshot=e;function r(s){let n=t.MemorySnapshot.capture(),o=s(n);return n.free(),o}t.memorySnapshot=r})(u||(u={}));var u;(function(t){class e extends b{get class(){return new t.Class(t.exports.methodGetClass(this))}get flags(){return t.exports.methodGetFlags(this,NULL)}get implementationFlags(){let n=Memory.alloc(Process.pointerSize);return t.exports.methodGetFlags(this,n),n.readU32()}get fridaSignature(){let n=[];for(let o of this.parameters)n.push(o.type.fridaAlias);return(!this.isStatic||t.unityVersionIsBelow201830)&&n.unshift("pointer"),this.isInflated&&n.push("pointer"),n}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let n=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(n).map(o=>new t.Class(t.exports.classFromObject(o)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!t.exports.methodIsGeneric(this)}get isInflated(){return!!t.exports.methodIsInflated(this)}get isStatic(){return!t.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return t.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new t.Object(t.exports.methodGetObject(this,NULL))}get parameterCount(){return t.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(n,o)=>{let i=t.exports.methodGetParameterName(this,o).readUtf8String(),a=t.exports.methodGetParameterType(this,o);return new t.Parameter(i,o,new t.Type(a))})}get relativeVirtualAddress(){return this.virtualAddress.sub(t.module.base)}get returnType(){return new t.Type(t.exports.methodGetReturnType(this))}get virtualAddress(){let n=t.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,o=n.field("method_ptr").value,a=n.field("method").value.offsetOf(c=>c.readPointer().equals(o))??m("couldn't find the virtual address offset in the native method struct");return T(t.Method.prototype,"virtualAddress",function(){return this.handle.add(a).readPointer()},l),t.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(n){try{Interceptor.replace(this.virtualAddress,this.wrap(n))}catch(o){switch(o.message){case"access violation accessing 0x0":m(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:x(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":x(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw o}}}inflate(...n){if(!this.isGeneric||this.generics.length!=n.length){for(let c of this.overloads())if(c.isGeneric&&c.generics.length==n.length)return c.inflate(...n);m(`could not find inflatable signature of method ${this.name} with ${n.length} generic parameter(s)`)}let o=n.map(c=>c.type.object),i=t.array(t.corlib.class("System.Type"),o),a=this.object.method("MakeGenericMethod",1).invoke(i);return new t.Method(a.field("mhandle").value)}invoke(...n){return this.isStatic||m(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...n)}invokeRaw(n,...o){let i=o.map(t.toFridaValue);(!this.isStatic||t.unityVersionIsBelow201830)&&i.unshift(n),this.isInflated&&i.push(this.handle);try{let a=this.nativeFunction(...i);return t.fromFridaValue(a,this.returnType)}catch(a){switch(a==null&&m("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),a.message){case"bad argument count":m(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${o.length}`);case"expected a pointer":case"expected number":case"expected array with fields":m(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw a}}overload(...n){return this.tryOverload(...n)??m(`couldn't find overloaded method ${this.name}(${n.map(i=>i instanceof t.Class?i.type.name:i)})`)}*overloads(){for(let n of this.class.hierarchy())for(let o of n.methods)this.name==o.name&&(yield o)}parameter(n){return this.tryParameter(n)??m(`couldn't find parameter ${n} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...n){let o=n.length*1,i=n.length*2,a;e:for(let c of this.overloads()){if(c.parameterCount!=n.length)continue;let d=0,g=0;for(let f of c.parameters){let p=n[g];if(p instanceof t.Class)if(f.type.is(p.type))d+=2;else if(f.type.class.isAssignableFrom(p))d+=1;else continue e;else if(f.type.name==p)d+=2;else continue e;g++}if(!(d<o)){if(d==i)return c;if(a==null||d>a[0])a=[d,c];else if(d==a[0]){let f=0;for(let p of a[1].parameters){if(p.type.class.isAssignableFrom(c.parameters[f].type.class)){a=[d,c];continue e}f++}}}}return a?.[1]}tryParameter(n){return this.parameters.find(o=>o.name==n)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(n=>n.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(n){return this.isStatic&&m(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(o,i,a){switch(i){case"invoke":let c=n instanceof t.ValueType?o.class.isValueType?n.handle.sub(r()?t.Object.headerSize:0):m(`cannot invoke method ${o.class.type.name}::${o.name} against a value type, you must box it first`):o.class.isValueType?n.handle.add(r()?0:t.Object.headerSize):n.handle;return o.invokeRaw.bind(o,c);case"overloads":return function*(){for(let g of o[i]())g.isStatic||(yield g)};case"inflate":case"overload":case"tryOverload":let d=Reflect.get(o,i).bind(a);return function(...g){return d(...g)?.bind(n)}}return Reflect.get(o,i)}})}wrap(n){let o=+!this.isStatic|+t.unityVersionIsBelow201830;return new NativeCallback((...i)=>{let a=this.isStatic?this.class:this.class.isValueType?new t.ValueType(i[0].add(r()?t.Object.headerSize:0),this.class.type):new t.Object(i[0]),c=this.parameters.map((g,f)=>t.fromFridaValue(i[f+o],g.type)),d=n.call(a,...c);return t.toFridaValue(d)},this.returnType.fridaAlias,this.fridaSignature)}}h([l],e.prototype,"class",null),h([l],e.prototype,"flags",null),h([l],e.prototype,"implementationFlags",null),h([l],e.prototype,"fridaSignature",null),h([l],e.prototype,"generics",null),h([l],e.prototype,"isExternal",null),h([l],e.prototype,"isGeneric",null),h([l],e.prototype,"isInflated",null),h([l],e.prototype,"isStatic",null),h([l],e.prototype,"isSynchronized",null),h([l],e.prototype,"modifier",null),h([l],e.prototype,"name",null),h([l],e.prototype,"nativeFunction",null),h([l],e.prototype,"object",null),h([l],e.prototype,"parameterCount",null),h([l],e.prototype,"parameters",null),h([l],e.prototype,"relativeVirtualAddress",null),h([l],e.prototype,"returnType",null),t.Method=e;let r=()=>{let s=t.corlib.class("System.Int64").alloc();s.field("m_value").value=3735928559;let n=s.method("Equals",1).overload(s.class).invokeRaw(s,3735928559);return(r=()=>n)()}})(u||(u={}));var u;(function(t){class e extends b{static get headerSize(){return t.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&m(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(s,n,o){return n=="class"?Reflect.get(s,n).parent:n=="base"?Reflect.getOwnPropertyDescriptor(t.Object.prototype,n).get.bind(o)():Reflect.get(s,n)}})}get class(){return new t.Class(t.exports.objectGetClass(this))}get monitor(){return new t.Object.Monitor(this)}get size(){return t.exports.objectGetSize(this)}field(s){return this.tryField(s)??m(`couldn't find non-static field ${s} in hierarchy of class ${this.class.type.name}`)}method(s,n=-1){return this.tryMethod(s,n)??m(`couldn't find non-static method ${s} in hierarchy of class ${this.class.type.name}`)}ref(s){return new t.GCHandle(t.exports.gcHandleNew(this,+s))}virtualMethod(s){return new t.Method(t.exports.objectGetVirtualMethod(this,s)).bind(this)}tryField(s){let n=this.class.tryField(s);if(n?.isStatic){for(let o of this.class.hierarchy({includeCurrent:!1}))for(let i of o.fields)if(i.name==s&&!i.isStatic)return i.bind(this);return}return n?.bind(this)}tryMethod(s,n=-1){let o=this.class.tryMethod(s,n);if(o?.isStatic){for(let i of this.class.hierarchy())for(let a of i.methods)if(a.name==s&&!a.isStatic&&(n<0||a.parameterCount==n))return a.bind(this);return}return o?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new t.ValueType(t.exports.objectUnbox(this),this.class.type):m(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(s){return new t.GCHandle(t.exports.gcHandleNewWeakRef(this,+s))}}h([l],e.prototype,"class",null),h([l],e.prototype,"size",null),h([l],e,"headerSize",null),t.Object=e,function(r){class s{handle;constructor(o){this.handle=o}enter(){return t.exports.monitorEnter(this.handle)}exit(){return t.exports.monitorExit(this.handle)}pulse(){return t.exports.monitorPulse(this.handle)}pulseAll(){return t.exports.monitorPulseAll(this.handle)}tryEnter(o){return!!t.exports.monitorTryEnter(this.handle,o)}tryWait(o){return!!t.exports.monitorTryWait(this.handle,o)}wait(){return t.exports.monitorWait(this.handle)}}r.Monitor=s}(e=t.Object||(t.Object={}))})(u||(u={}));var u;(function(t){class e{name;position;type;constructor(s,n,o){this.name=s,this.position=n,this.type=o}toString(){return`${this.type.name} ${this.name}`}}t.Parameter=e})(u||(u={}));var u;(function(t){class e extends b{type;constructor(s,n){super(s),this.type=n}get(s){return t.read(this.handle.add(s*this.type.class.arrayElementSize),this.type)}read(s,n=0){let o=new globalThis.Array(s);for(let i=0;i<s;i++)o[i]=this.get(i+n);return o}set(s,n){t.write(this.handle.add(s*this.type.class.arrayElementSize),n,this.type)}toString(){return this.handle.toString()}write(s,n=0){for(let o=0;o<s.length;o++)this.set(o+n,s[o])}}t.Pointer=e})(u||(u={}));var u;(function(t){class e extends b{type;constructor(n,o){super(n),this.type=o}get value(){return t.read(this.handle,this.type)}set value(n){t.write(this.handle,n,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}t.Reference=e;function r(s,n){let o=Memory.alloc(Process.pointerSize);switch(typeof s){case"boolean":return new t.Reference(o.writeS8(+s),t.corlib.class("System.Boolean").type);case"number":switch(n?.enumValue){case t.Type.Enum.UBYTE:return new t.Reference(o.writeU8(s),n);case t.Type.Enum.BYTE:return new t.Reference(o.writeS8(s),n);case t.Type.Enum.CHAR:case t.Type.Enum.USHORT:return new t.Reference(o.writeU16(s),n);case t.Type.Enum.SHORT:return new t.Reference(o.writeS16(s),n);case t.Type.Enum.UINT:return new t.Reference(o.writeU32(s),n);case t.Type.Enum.INT:return new t.Reference(o.writeS32(s),n);case t.Type.Enum.ULONG:return new t.Reference(o.writeU64(s),n);case t.Type.Enum.LONG:return new t.Reference(o.writeS64(s),n);case t.Type.Enum.FLOAT:return new t.Reference(o.writeFloat(s),n);case t.Type.Enum.DOUBLE:return new t.Reference(o.writeDouble(s),n)}case"object":if(s instanceof t.ValueType||s instanceof t.Pointer)return new t.Reference(s.handle,s.type);if(s instanceof t.Object)return new t.Reference(o.writePointer(s),s.class.type);if(s instanceof t.String||s instanceof t.Array)return new t.Reference(o.writePointer(s),s.object.class.type);if(s instanceof NativePointer)switch(n?.enumValue){case t.Type.Enum.NUINT:case t.Type.Enum.NINT:return new t.Reference(o.writePointer(s),n)}else{if(s instanceof Int64)return new t.Reference(o.writeS64(s),t.corlib.class("System.Int64").type);if(s instanceof UInt64)return new t.Reference(o.writeU64(s),t.corlib.class("System.UInt64").type)}default:m(`couldn't create a reference to ${s} using an unhandled type ${n?.name}`)}}t.reference=r})(u||(u={}));var u;(function(t){class e extends b{get content(){return t.exports.stringGetChars(this).readUtf16String(this.length)}set content(n){let o=t.string("vfsfitvnm").handle.offsetOf(i=>i.readInt()==9)??m("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(t.String.prototype,"content",{set(i){t.exports.stringGetChars(this).writeUtf16String(i??""),this.handle.add(o).writeS32(i?.length??0)}}),this.content=n}get length(){return t.exports.stringGetLength(this)}get object(){return new t.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}t.String=e;function r(s){return new t.String(t.exports.stringNew(Memory.allocUtf8String(s??"")))}t.string=r})(u||(u={}));var u;(function(t){class e extends b{get id(){let s=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let n=Process.getCurrentThreadId(),i=ptr(s.apply(t.currentThread)).offsetOf(c=>c.readS32()==n,1024)??m("couldn't find the offset for determining the kernel id of a posix thread"),a=s;s=function(){return ptr(a.apply(this)).add(i).readS32()}}return T(t.Thread.prototype,"id",s,l),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!t.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new t.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let n=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(n.tryField("_syncContext")?.value??n.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(t.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return t.exports.threadDetach(this)}schedule(s){let n=this.synchronizationContext?.tryMethod("Post");return n==null?Process.runOnThread(this.id,s):new Promise(o=>{let i=t.delegate(t.corlib.class("System.Threading.SendOrPostCallback"),()=>{let a=s();setImmediate(()=>o(a))});Script.bindWeak(globalThis,()=>{i.field("method_ptr").value=i.field("invoke_impl").value=t.exports.domainGet}),n.invoke(i,NULL)})}tryLocalValue(s){for(let n=0;n<16;n++){let o=this.staticData.add(n*Process.pointerSize).readPointer();if(!o.isNull()){let i=new t.Object(o.readPointer()).asNullable();if(i?.class?.isSubclassOf(s,!1))return i}}}}h([l],e.prototype,"internal",null),h([l],e.prototype,"isFinalizer",null),h([l],e.prototype,"managedId",null),h([l],e.prototype,"object",null),h([l],e.prototype,"staticData",null),h([l],e.prototype,"synchronizationContext",null),t.Thread=e,T(t,"attachedThreads",()=>{if(t.exports.threadGetAttachedThreads.isNull()){let r=t.currentThread?.handle??m("Current thread is not attached to IL2CPP"),s=r.toMatchPattern(),n=[];for(let o of Process.enumerateRanges("rw-"))if(o.file==null){let i=Memory.scanSync(o.base,o.size,s);if(i.length==1){for(;;){let a=i[0].address.sub(i[0].size*n.length).readPointer();if(a.isNull()||!a.readPointer().equals(r.readPointer()))break;n.unshift(new t.Thread(a))}break}}return n}return B(t.exports.threadGetAttachedThreads).map(r=>new t.Thread(r))}),T(t,"currentThread",()=>new t.Thread(t.exports.threadGetCurrent()).asNullable()),T(t,"mainThread",()=>t.attachedThreads[0])})(u||(u={}));var u;(function(t){let e=class extends b{static get Enum(){let s=(o,i=a=>a)=>i(t.corlib.class(o)).type.enumValue,n={VOID:s("System.Void"),BOOLEAN:s("System.Boolean"),CHAR:s("System.Char"),BYTE:s("System.SByte"),UBYTE:s("System.Byte"),SHORT:s("System.Int16"),USHORT:s("System.UInt16"),INT:s("System.Int32"),UINT:s("System.UInt32"),LONG:s("System.Int64"),ULONG:s("System.UInt64"),NINT:s("System.IntPtr"),NUINT:s("System.UIntPtr"),FLOAT:s("System.Single"),DOUBLE:s("System.Double"),POINTER:s("System.IntPtr",o=>o.field("m_value")),VALUE_TYPE:s("System.Decimal"),OBJECT:s("System.Object"),STRING:s("System.String"),CLASS:s("System.Array"),ARRAY:s("System.Void",o=>o.arrayClass),NARRAY:s("System.Void",o=>new t.Class(t.exports.classGetArrayClass(o,2))),GENERIC_INSTANCE:s("System.Int32",o=>o.interfaces.find(i=>i.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:n}),I({...n,VAR:s("System.Action`1",o=>o.generics[0]),MVAR:s("System.Array",o=>o.method("AsReadOnly",1).generics[0])})}get class(){return new t.Class(t.exports.typeGetClass(this))}get fridaAlias(){function s(n){let o=n.class.fields.filter(i=>!i.isStatic);return o.length==0?["char"]:o.map(i=>i.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case t.Type.Enum.VOID:return"void";case t.Type.Enum.BOOLEAN:return"bool";case t.Type.Enum.CHAR:return"uchar";case t.Type.Enum.BYTE:return"int8";case t.Type.Enum.UBYTE:return"uint8";case t.Type.Enum.SHORT:return"int16";case t.Type.Enum.USHORT:return"uint16";case t.Type.Enum.INT:return"int32";case t.Type.Enum.UINT:return"uint32";case t.Type.Enum.LONG:return"int64";case t.Type.Enum.ULONG:return"uint64";case t.Type.Enum.FLOAT:return"float";case t.Type.Enum.DOUBLE:return"double";case t.Type.Enum.NINT:case t.Type.Enum.NUINT:case t.Type.Enum.POINTER:case t.Type.Enum.STRING:case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return"pointer";case t.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:s(this);case t.Type.Enum.CLASS:case t.Type.Enum.OBJECT:case t.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?s(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case t.Type.Enum.BOOLEAN:case t.Type.Enum.CHAR:case t.Type.Enum.BYTE:case t.Type.Enum.UBYTE:case t.Type.Enum.SHORT:case t.Type.Enum.USHORT:case t.Type.Enum.INT:case t.Type.Enum.UINT:case t.Type.Enum.LONG:case t.Type.Enum.ULONG:case t.Type.Enum.FLOAT:case t.Type.Enum.DOUBLE:case t.Type.Enum.NINT:case t.Type.Enum.NUINT:return!0;default:return!1}}get name(){let s=t.exports.typeGetName(this);try{return s.readUtf8String()}finally{t.free(s)}}get object(){return new t.Object(t.exports.typeGetObject(this))}get enumValue(){return t.exports.typeGetTypeEnum(this)}is(s){return t.exports.typeEquals.isNull()?this.object.method("Equals").invoke(s.object):!!t.exports.typeEquals(this,s)}toString(){return this.name}};h([l],e.prototype,"class",null),h([l],e.prototype,"fridaAlias",null),h([l],e.prototype,"isByReference",null),h([l],e.prototype,"isPrimitive",null),h([l],e.prototype,"name",null),h([l],e.prototype,"object",null),h([l],e.prototype,"enumValue",null),h([l],e,"Enum",null),e=h([$],e),t.Type=e})(u||(u={}));var u;(function(t){class e extends b{type;constructor(s,n){super(s),this.type=n}box(){return new t.Object(t.exports.valueTypeBox(this.type.class,this))}field(s){return this.tryField(s)??m(`couldn't find non-static field ${s} in hierarchy of class ${this.type.name}`)}method(s,n=-1){return this.tryMethod(s,n)??m(`couldn't find non-static method ${s} in hierarchy of class ${this.type.name}`)}tryField(s){let n=this.type.class.tryField(s);if(n?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.fields)if(i.name==s&&!i.isStatic)return i.bind(this);return}return n?.bind(this)}tryMethod(s,n=-1){let o=this.type.class.tryMethod(s,n);if(o?.isStatic){for(let i of this.type.class.hierarchy())for(let a of i.methods)if(a.name==s&&!a.isStatic&&(n<0||a.parameterCount==n))return a.bind(this);return}return o?.bind(this)}toString(){let s=this.method("ToString",0);return this.isNull()?"null":s.class.isValueType?s.invoke().content??"null":this.box().toString()??"null"}}t.ValueType=e})(u||(u={}));globalThis.Il2Cpp=u;var A=class{config;errorCounts=new Map;retryHistory=new Map;lastErrorTime=new Map;constructor(e){this.config={maxRetries:5,retryDelay:1e3,enableRetryBackoff:!0,maxBackoffDelay:1e4,ignoreKnownErrors:!0,logErrorDetails:!0,...e}}classifyError(e){let s=(e instanceof Error?e.message:String(e)).toLowerCase();return s.includes("breakpoint triggered")||s.includes("breakpoint")?"breakpoint_triggered":s.includes("access violation")||s.includes("segmentation fault")||s.includes("sigsegv")?"access_violation":s.includes("abort was called")||s.includes("abort()")||s.includes("sigabrt")?"abort_called":(s.includes("invalid pointer")||s.includes("null pointer")||s.includes("bad pointer")||s.includes("timeout")||s.includes("timed out"),"unknown_error")}shouldIgnoreError(e){return this.config.ignoreKnownErrors?["breakpoint_triggered","access_violation","abort_called","illegal_instruction"].includes(e):!1}calculateRetryDelay(e){if(!this.config.enableRetryBackoff)return this.config.retryDelay;let r=this.config.retryDelay*Math.pow(2,e-1);return Math.min(r,this.config.maxBackoffDelay)}shouldRetry(e,r){if((this.errorCounts.get(e)||0)>=this.config.maxRetries||r==="unknown_error"&&this.config.ignoreKnownErrors)return!1;let n=this.lastErrorTime.get(e);return!(n&&Date.now()-n<100)}recordRetryAttempt(e,r,s){let n=this.errorCounts.get(e)||0,o=s?0:n+1;this.errorCounts.set(e,o),this.lastErrorTime.set(e,Date.now());let i=this.retryHistory.get(e)||[],a={attemptNumber:n+1,timestamp:Date.now(),errorType:r,success:s,delay:this.calculateRetryDelay(n+1)};i.push(a),i.length>10&&i.shift(),this.retryHistory.set(e,i)}async executeWithProtection(e,r,s){let n=null;for(let o=1;o<=this.config.maxRetries+1;o++)try{let i=await r();return this.recordRetryAttempt(e,"unknown_error",!0),o>1&&this.config.logErrorDetails&&console.log(`[+] ${s||e}: Succeeded on attempt ${o}`),i}catch(i){n=i;let a=this.classifyError(i);if(this.recordRetryAttempt(e,a,!1),this.config.logErrorDetails){let c=i instanceof Error?i.message:String(i);console.log(`[!] ${s||e}: Attempt ${o} failed - ${a}: ${c}`)}if(this.shouldIgnoreError(a))return this.config.logErrorDetails&&console.log(`[~] ${s||e}: Ignoring known anti-debug error (${a})`),null;if(o<=this.config.maxRetries&&this.shouldRetry(e,a)){let c=this.calculateRetryDelay(o);this.config.logErrorDetails&&console.log(`[*] ${s||e}: Retrying in ${c}ms (attempt ${o+1}/${this.config.maxRetries+1})`),await new Promise(d=>setTimeout(d,c));continue}break}if(this.config.logErrorDetails){let o=this.classifyError(n);console.log(`[\u274C] ${s||e}: All attempts failed - final error: ${o}`)}return null}validateInstancePointer(e,r){let s=`validate_${e.toString()}`;try{if(e.isNull())return!1;let n=e.readU32();return!0}catch(n){let o=this.classifyError(n);return this.shouldIgnoreError(o)?(this.config.logErrorDetails&&console.log(`[~] ${r||"validateInstance"}: Ignoring validation error (${o}) for ${e}`),!1):(this.config.logErrorDetails&&console.log(`[!] ${r||"validateInstance"}: Validation failed for ${e} - ${o}`),!1)}}getRetryStats(e){return{retryCount:this.errorCounts.get(e)||0,history:this.retryHistory.get(e)||[]}}clearRetryHistory(e){this.errorCounts.delete(e),this.retryHistory.delete(e),this.lastErrorTime.delete(e)}getProtectionStats(){let e=this.errorCounts.size,r=Array.from(this.errorCounts.values()).reduce((n,o)=>n+o,0),s=new Map;for(let n of this.retryHistory.values())for(let o of n)if(!o.success){let i=s.get(o.errorType)||0;s.set(o.errorType,i+1)}return{totalOperations:e,totalRetries:r,errorTypeDistribution:s}}};var k=class{methods;addresses;moduleInfo;hooks=new Map;errorTrackers=new Map;antiDebugProtection;onCanCollectCallback;onFinishCollectCallback;onUpdateCallback;processedInstances=new Set;realTimeCollectionEnabled=!0;constructor(e,r,s){this.methods=e,this.addresses=r,this.moduleInfo=s,this.antiDebugProtection=new A({maxRetries:5,retryDelay:1e3,enableRetryBackoff:!0,ignoreKnownErrors:!0,logErrorDetails:!0}),console.log("\u{1F3A3} MethodHookManager initialized with anti-debugging protection")}setupAllHooks(){console.log("\u{1F3AF} Setting up all method hooks...");try{this.setupCanCollectHook(),this.setupFinishCollectHook(),this.setupUpdateHook(),this.setupRewardHooks(),console.log("\u2705 All method hooks setup complete")}catch(e){console.log(`\u274C Failed to setup hooks: ${e}`)}}setupCanCollectHook(){try{console.log("\u{1F3A3} Setting up CanCollect hook...");let e=this.methods.CanCollect?.handle||this.addresses.CanCollect;if(!e||e.isNull()){console.log("\u26A0\uFE0F CanCollect hook target not available");return}let r=Interceptor.attach(e,{onEnter:s=>{let n=s[0];console.log(`[*] CanCollect() called on GoodyHutHelper: ${n}`),this.thisPtr=n},onLeave:s=>{try{let n=s.toInt32()===1,o=this.thisPtr;console.log(`[*] CanCollect() returned: ${n}`),n&&(console.log("[+] Ruins can be collected! Auto-triggering..."),this.onCanCollectCallback&&this.onCanCollectCallback(o,n),this.triggerAutoCollection(o))}catch(n){this.handleHookError("CanCollect",n,this.thisPtr)}}});this.hooks.set("CanCollect",r),console.log("\u2705 CanCollect hook installed")}catch(e){console.log(`\u274C Failed to setup CanCollect hook: ${e}`)}}setupFinishCollectHook(){try{console.log("\u{1F3A3} Setting up FinishCollect hook...");let e=this.methods.FinishCollect?.handle||this.addresses.FinishCollect;if(!e||e.isNull()){console.log("\u26A0\uFE0F FinishCollect hook target not available");return}let r=Interceptor.attach(e,{onEnter:s=>{let n=s[0];console.log(`[*] FinishCollect() called on GoodyHutHelper: ${n}`),this.thisPtr=n,this.startTime=Date.now()},onLeave:s=>{try{let n=this.thisPtr,o=Date.now()-this.startTime;console.log(`[+] FinishCollect() completed in ${o}ms`),this.onFinishCollectCallback&&this.onFinishCollectCallback(n,!0),this.updateEntityStateForCleanup(n)}catch(n){this.handleHookError("FinishCollect",n,this.thisPtr)}}});this.hooks.set("FinishCollect",r),console.log("\u2705 FinishCollect hook installed")}catch(e){console.log(`\u274C Failed to setup FinishCollect hook: ${e}`)}}setupUpdateHook(){try{console.log("\u{1F3A3} Setting up Update hook for real-time discovery...");let e=this.methods.Update?.handle||this.addresses.Update;if(!e||e.isNull()){console.log("\u26A0\uFE0F Update hook target not available");return}let r=0,s=Interceptor.attach(e,{onEnter:n=>{if(r++,r%15===0){let o=n[0];this.onUpdateCallback&&this.onUpdateCallback(o),this.checkInstanceForRealTimeCollection(o)}}});this.hooks.set("Update",s),console.log("\u2705 Update hook installed for real-time discovery")}catch(e){console.log(`\u274C Failed to setup Update hook: ${e}`)}}setupRewardHooks(){try{if(console.log("\u{1F3A3} Setting up reward monitoring hooks..."),this.methods.GetRewardType?.handle||this.addresses.GetRewardType){let e=this.methods.GetRewardType?.handle||this.addresses.GetRewardType,r=Interceptor.attach(e,{onLeave:s=>{let n=s.toInt32();console.log(`[*] Reward Type: ${n}`)}});this.hooks.set("GetRewardType",r)}if(this.methods.GetRewardAmount?.handle||this.addresses.GetRewardAmount){let e=this.methods.GetRewardAmount?.handle||this.addresses.GetRewardAmount,r=Interceptor.attach(e,{onLeave:s=>{let n=s.toInt32();console.log(`[*] Reward Amount: ${n}`)}});this.hooks.set("GetRewardAmount",r)}console.log("\u2705 Reward monitoring hooks installed")}catch(e){console.log(`\u274C Failed to setup reward hooks: ${e}`)}}triggerAutoCollection(e){try{console.log(`[+] Triggering auto-collection for instance: ${e}`),this.setCleanupFlag(e),this.callFinishCollect(e)}catch(r){this.handleHookError("triggerAutoCollection",r,e)}}setCleanupFlag(e){try{let r=this.methods.Config?.handle||this.addresses.Config;if(!r||r.isNull())return console.log("\u26A0\uFE0F Config method not available for cleanup flag"),!1;let n=new NativeFunction(r,"pointer",["pointer"])(e);if(n&&!n.isNull()&&!n.equals(ptr(0)))try{return n.add(48).writeU8(1),console.log(`[+] Set cleanUp flag for instance: ${e}`),!0}catch(o){return console.log(`[-] Error setting cleanUp flag: ${o}`),!1}else return console.log(`[-] Config() returned null/invalid pointer for: ${e}`),!1}catch(r){return console.log(`[-] Error in setCleanupFlag: ${r}`),!1}}callFinishCollect(e){try{let r=this.methods.FinishCollect?.handle||this.addresses.FinishCollect;return!r||r.isNull()?(console.log("\u26A0\uFE0F FinishCollect method not available"),!1):(new NativeFunction(r,"void",["pointer"])(e),console.log(`[+] FinishCollect() executed successfully on: ${e}`),!0)}catch(r){return console.log(`[-] Error executing FinishCollect(): ${r}`),!1}}checkInstanceForRealTimeCollection(e){try{if(!this.realTimeCollectionEnabled)return;let r=e.toString();if(this.processedInstances.has(r))return;let s=this.methods.CanCollect?.handle||this.addresses.CanCollect;if(!s||s.isNull())return;new NativeFunction(s,"int",["pointer"])(e)===1&&(console.log(`[+] REAL-TIME: Found collectible ruins at: ${e} - Processing immediately`),this.triggerRealTimeCollection(e)?(this.processedInstances.add(r),console.log(`[+] REAL-TIME SUCCESS: Collected ruins at ${e} (Total processed: ${this.processedInstances.size})`)):console.log(`[-] REAL-TIME FAILED: Error processing ${r}`))}catch{}}triggerRealTimeCollection(e){try{console.log(`[+] Triggering real-time collection for instance: ${e}`);let r=this.setCleanupFlag(e),s=this.callFinishCollect(e);return r&&s}catch(r){return this.handleHookError("triggerRealTimeCollection",r,e),!1}}updateEntityStateForCleanup(e){try{console.log(`[*] Updating entity state for cleanup: ${e}`),setTimeout(()=>{try{console.log("[*] Attempting automatic ruin selling...");let r=this.methods.SellRuins?.handle||this.addresses.SellRuins;r&&!r.isNull()&&(new NativeFunction(r,"void",["pointer"])(e),console.log("[+] SellRuins() executed for automatic cleanup"))}catch(r){console.log(`[-] Error in automatic ruin selling: ${r}`)}},1e3)}catch(r){console.log(`[-] Error updating state for cleanup: ${r}`)}}handleHookError(e,r,s){let n=s?s.toString():"unknown",o=`${e}_${n}`,i=this.antiDebugProtection.classifyError(r);this.antiDebugProtection.recordRetryAttempt(o,i,!1);let a={errorType:i,errorMessage:r instanceof Error?r.message:String(r),occurredAt:Date.now(),instanceId:n,methodName:e,retryCount:this.antiDebugProtection.getRetryStats(o).retryCount};this.errorTrackers.set(`${e}_${n}_${Date.now()}`,a),this.antiDebugProtection.shouldIgnoreError(i)?console.log(`\u26A0\uFE0F ${e}: Known anti-debug error (${i}) - continuing operation`):console.log(`\u274C ${e}: Error requiring attention - ${i}: ${a.errorMessage}`)}async executeWithProtection(e,r,s){return await this.antiDebugProtection.executeWithProtection(e,r,s)}validateInstance(e,r){return this.antiDebugProtection.validateInstancePointer(e,r)}getProtectionStats(){return this.antiDebugProtection.getProtectionStats()}onCanCollect(e){this.onCanCollectCallback=e}onFinishCollect(e){this.onFinishCollectCallback=e}onUpdate(e){this.onUpdateCallback=e}setRealTimeCollectionEnabled(e){this.realTimeCollectionEnabled=e,console.log(`[*] Real-time collection ${e?"ENABLED":"DISABLED"}`)}getRealTimeStats(){return{processed:this.processedInstances.size,enabled:this.realTimeCollectionEnabled}}clearProcessedInstances(){this.processedInstances.clear(),console.log("[+] Cleared real-time processed instances tracking")}removeAllHooks(){console.log("\u{1F9F9} Removing all method hooks..."),this.hooks.forEach((e,r)=>{try{e.detach(),console.log(`\u2705 Removed ${r} hook`)}catch(s){console.log(`\u26A0\uFE0F Error removing ${r} hook: ${s}`)}}),this.hooks.clear(),this.errorTrackers.clear(),console.log("\u2705 All hooks removed and cleaned up")}getHookStats(){return{activeHooks:this.hooks.size,errorCount:this.errorTrackers.size,errors:Array.from(this.errorTrackers.values())}}};var N=class{config;stats;phaseTracker;methods;addresses;discoveredInstances=new Map;processedInstances=new Set;failedInstances=new Map;isProcessingBatch=!1;batchNumber=0;batchProcessingInterval=null;statusMonitoringInterval=null;cleanupInterval=null;onBatchCompleteCallback;onPhaseChangeCallback;onInstanceProcessedCallback;constructor(e,r,s){this.config=e,this.methods=r,this.addresses=s,this.stats={totalDiscoveryScans:0,discoveredInstances:0,processedInstances:0,failedInstances:0,batchNumber:0,isProcessingBatch:!1,currentPhase:1,lastDiscoveryTime:Date.now(),discoveryCompleteTime:0},this.phaseTracker={currentPhase:1,phaseStartTime:Date.now(),discoveryComplete:!1},console.log("\u{1F4E6} BatchProcessor initialized with configuration:"),console.log(`   - Batch size: ${this.config.batchSize}`),console.log(`   - Batch interval: ${this.config.batchInterval}ms`),console.log(`   - Retry limit: ${this.config.retryLimit}`),console.log(`   - Discovery timeout: ${this.config.discoveryTimeout}ms`)}async startBatchProcessing(){console.log("\u{1F680} Starting two-phase batch processing system..."),this.startDiscoveryPhase(),this.setupBatchProcessingTimer(),this.setupStatusMonitoring(),this.setupCleanupTimer(),console.log("\u2705 Batch processing system started")}stopBatchProcessing(){console.log("\u{1F6D1} Stopping batch processing system..."),this.batchProcessingInterval&&(clearInterval(this.batchProcessingInterval),this.batchProcessingInterval=null),this.statusMonitoringInterval&&(clearInterval(this.statusMonitoringInterval),this.statusMonitoringInterval=null),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.isProcessingBatch=!1,console.log("\u2705 Batch processing system stopped")}addDiscoveredInstance(e,r){let s=e.toString();if(!this.processedInstances.has(s)&&!this.discoveredInstances.has(s)){let n={ptr:e,discoveryTime:Date.now(),retryCount:0,entityId:r||s};this.discoveredInstances.set(s,n),this.stats.discoveredInstances=this.discoveredInstances.size,this.stats.lastDiscoveryTime=Date.now(),this.config.batchSize===1&&console.log(`[+] REAL-TIME: Added instance ${s} for immediate processing`)}}forceCompleteDiscovery(){this.phaseTracker.currentPhase===1?(console.log("[*] Forcing discovery phase completion..."),this.completeDiscoveryPhase()):console.log("[*] Discovery phase already completed")}forceBatchProcess(){this.phaseTracker.currentPhase===2?(console.log("[*] Forcing immediate batch processing..."),this.processCollectionBatch()):console.log("[*] Cannot force batch processing - still in discovery phase")}resetToDiscovery(){console.log("[*] Resetting to discovery phase..."),this.phaseTracker.currentPhase=1,this.phaseTracker.phaseStartTime=Date.now(),this.phaseTracker.discoveryComplete=!1,this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.batchNumber=0,this.isProcessingBatch=!1,this.stats.lastDiscoveryTime=Date.now(),this.stats.totalDiscoveryScans=0,this.onPhaseChangeCallback&&this.onPhaseChangeCallback(1),console.log("[+] Reset complete - discovery phase restarted")}startDiscoveryPhase(){console.log("[+] ===== PHASE 1: DISCOVERY SCAN STARTED ====="),console.log("[*] Scanning for ALL collectible GoodyHutHelper instances..."),console.log(`[*] Discovery will complete after ${this.config.discoveryTimeout/1e3} seconds with no new findings`),this.phaseTracker.currentPhase=1,this.phaseTracker.phaseStartTime=Date.now(),this.phaseTracker.discoveryComplete=!1,this.stats.currentPhase=1,this.stats.lastDiscoveryTime=Date.now()}completeDiscoveryPhase(){this.phaseTracker.currentPhase=2,this.phaseTracker.phaseEndTime=Date.now(),this.phaseTracker.phaseDuration=this.phaseTracker.phaseEndTime-this.phaseTracker.phaseStartTime,this.phaseTracker.discoveryComplete=!0,this.stats.currentPhase=2,this.stats.discoveryCompleteTime=this.phaseTracker.phaseEndTime,console.log("[+] ===== PHASE 1: DISCOVERY SCAN COMPLETED ====="),console.log(`[+] Total collectible instances discovered: ${this.discoveredInstances.size}`),console.log(`[+] Total discovery scans performed: ${this.stats.totalDiscoveryScans}`),console.log(`[+] Discovery phase duration: ${(this.phaseTracker.phaseDuration/1e3).toFixed(1)} seconds`),this.onPhaseChangeCallback&&this.onPhaseChangeCallback(2),this.discoveredInstances.size===0?console.log("[*] No collectible ruins found. Monitoring will continue for new instances."):(console.log("[+] ===== PHASE 2: BATCH COLLECTION STARTING ====="),console.log(`[*] Beginning batch processing of ${this.discoveredInstances.size} instances`),console.log(`[*] Batch size: ${this.config.batchSize}, Interval: ${this.config.batchInterval/1e3} seconds`),setTimeout(()=>{this.processCollectionBatch()},1e3))}async processCollectionBatch(){if(this.phaseTracker.currentPhase!==2)return;if(this.isProcessingBatch){console.log("[*] PHASE 2: Batch processing already in progress, skipping...");return}this.isProcessingBatch=!0,this.batchNumber++,this.stats.batchNumber=this.batchNumber,this.stats.isProcessingBatch=!0;let e=Date.now(),r=this.prepareBatchInstances();if(r.length===0){this.handleEmptyBatch();return}console.log(`[+] PHASE 2: Starting Batch #${this.batchNumber} - Processing ${r.length} instances`),console.log(`[*] Batch composition: ${r.filter(a=>a.source==="discovered").length} discovered, ${r.filter(a=>a.source==="retry").length} retries`),console.log(`[*] Remaining after this batch: ${this.discoveredInstances.size+this.failedInstances.size-r.length} instances`);let s=await this.processBatchInstances(r),o=Date.now()-e,i={batchNumber:this.batchNumber,totalProcessed:r.length,successCount:s.filter(a=>a.success).length,failureCount:s.filter(a=>!a.success).length,processingTime:o,results:s};console.log(`[+] Batch #${this.batchNumber} COMPLETED - Successes: ${i.successCount}, Failures: ${i.failureCount}`),console.log(`[*] Status - Processed: ${this.processedInstances.size}, Pending: ${this.discoveredInstances.size}, Failed: ${this.failedInstances.size}`),this.onBatchCompleteCallback&&this.onBatchCompleteCallback(i),this.isProcessingBatch=!1,this.stats.isProcessingBatch=!1}prepareBatchInstances(){let e=[],r=Array.from(this.discoveredInstances.entries());for(let i=0;i<Math.min(this.config.batchSize,r.length);i++){let[a,c]=r[i];e.push({id:a,data:c,source:"discovered"}),this.discoveredInstances.delete(a)}let s=Date.now(),n=Array.from(this.failedInstances.entries()),o=this.config.batchSize-e.length;for(let i=0;i<Math.min(o,n.length);i++){let[a,c]=n[i];c.lastFailTime&&s-c.lastFailTime>1e4&&c.retryCount<this.config.retryLimit&&(e.push({id:a,data:c,source:"retry"}),this.failedInstances.delete(a))}return e}handleEmptyBatch(){this.discoveredInstances.size+this.failedInstances.size===0?(console.log("[+] ===== PHASE 2: BATCH COLLECTION COMPLETED ====="),console.log("[+] All discovered instances have been processed!"),console.log("[+] Final Statistics:"),console.log(`    - Total processed: ${this.processedInstances.size}`),console.log(`    - Total failed (abandoned): ${Array.from(this.failedInstances.values()).filter(r=>r.retryCount>=this.config.retryLimit).length}`),console.log(`    - Total batches: ${this.batchNumber}`)):(console.log(`[*] PHASE 2: Batch #${this.batchNumber} - No instances ready to process`),console.log(`[*] Remaining: ${this.discoveredInstances.size} discovered, ${this.failedInstances.size} failed`)),this.isProcessingBatch=!1,this.stats.isProcessingBatch=!1}async processBatchInstances(e){let r=[];for(let s=0;s<e.length;s++){let n=e[s],o=await this.processInstance(n,s+1,e.length);r.push(o),s<e.length-1&&await new Promise(i=>setTimeout(i,200))}return r}async processInstance(e,r,s){let n=Date.now(),o=e.id,a=e.data.ptr;console.log(`[*] Batch #${this.batchNumber} [${r}/${s}] Processing: ${o}`);let c={success:!1,instanceId:o,processingTime:0,cleanupSet:!1};try{if(!a||a.isNull()||a.equals(ptr(0)))return c.errorMessage="Invalid pointer",c;if(!await this.checkCanCollect(a))return c.errorMessage="Instance no longer collectible",c.success=!0,c;let g=await this.setCleanupFlag(a);c.cleanupSet=g,await this.executeFinishCollect(a)?(c.success=!0,this.processedInstances.add(o),this.stats.processedInstances=this.processedInstances.size,console.log(`[+] Batch #${this.batchNumber} [${r}/${s}] SUCCESS: ${o}`),this.onInstanceProcessedCallback&&this.onInstanceProcessedCallback(c)):(c.errorMessage="FinishCollect failed",this.handleFailedInstance(e))}catch(d){let g=d instanceof Error?d.message:String(d);c.errorMessage=g,console.log(`[-] Batch #${this.batchNumber} [${r}/${s}] FAILED: ${o} - ${g}`),this.handleFailedInstance(e)}return c.processingTime=Date.now()-n,c}handleFailedInstance(e){let r=e.data.retryCount+1;if(r<this.config.retryLimit){let s={...e.data,retryCount:r,lastFailTime:Date.now()};this.failedInstances.set(e.id,s),this.stats.failedInstances=this.failedInstances.size,console.log(`[*] Added ${e.id} to retry queue (attempt ${r}/${this.config.retryLimit})`)}else console.log(`[-] Instance ${e.id} exceeded retry limit, abandoning`)}async checkCanCollect(e){try{let r=this.methods.CanCollect?.handle||this.addresses.CanCollect;return!r||r.isNull()?!1:new NativeFunction(r,"int",["pointer"])(e)===1}catch{return!1}}async setCleanupFlag(e){try{let r=this.methods.Config?.handle||this.addresses.Config;if(!r||r.isNull())return!1;let n=new NativeFunction(r,"pointer",["pointer"])(e);return n&&!n.isNull()&&!n.equals(ptr(0))?(n.add(48).writeU8(1),!0):!1}catch{return!1}}async executeFinishCollect(e){try{let r=this.methods.FinishCollect?.handle||this.addresses.FinishCollect;return!r||r.isNull()?!1:(new NativeFunction(r,"void",["pointer"])(e),!0)}catch{return!1}}setupBatchProcessingTimer(){this.batchProcessingInterval=setInterval(()=>{this.phaseTracker.currentPhase===2&&this.processCollectionBatch()},this.config.batchInterval)}setupStatusMonitoring(){this.statusMonitoringInterval=setInterval(()=>{this.printBatchStatus()},3e4)}setupCleanupTimer(){this.cleanupInterval=setInterval(()=>{this.cleanupProcessedInstances()},3e5)}printBatchStatus(){console.log("=== BATCH PROCESSING STATUS ==="),console.log(`Processing Mode: ${this.config.batchSize===1?"REAL-TIME":"BATCH"}`),console.log(`Current Phase: ${this.phaseTracker.currentPhase===1?"DISCOVERY":"COLLECTION"}`),console.log("Progress:"),console.log(`  - Successfully processed: ${this.processedInstances.size}`),console.log(`  - Discovered instances: ${this.discoveredInstances.size}`),console.log(`  - Failed instances: ${this.failedInstances.size}`),console.log(`  - Total scans performed: ${this.stats.totalDiscoveryScans}`),console.log(`  - Batches completed: ${this.batchNumber}`),this.failedInstances.size>0&&(console.log("Failed instances details:"),this.failedInstances.forEach((e,r)=>{console.log(`  ${r} - Retries: ${e.retryCount}/${this.config.retryLimit}`)})),this.processedInstances.size>=this.config.maxWaitTime&&console.log("[+] TARGET ACHIEVED: Processing target reached!"),console.log("====================================")}cleanupProcessedInstances(){if(this.processedInstances.size>1e3){console.log("[*] Cleaning up old processed instances...");let e=Array.from(this.processedInstances);this.processedInstances.clear();for(let r=e.length-500;r<e.length;r++)r>=0&&this.processedInstances.add(e[r]);console.log(`[+] Cleaned up processed instances, kept ${this.processedInstances.size}`)}}onBatchComplete(e){this.onBatchCompleteCallback=e}onPhaseChange(e){this.onPhaseChangeCallback=e}onInstanceProcessed(e){this.onInstanceProcessedCallback=e}getStats(){return{...this.stats}}getPhaseInfo(){return{...this.phaseTracker}}getQueueSizes(){return{discovered:this.discoveredInstances.size,processed:this.processedInstances.size,failed:this.failedInstances.size}}clearAllQueues(){this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.stats.discoveredInstances=0,this.stats.processedInstances=0,this.stats.failedInstances=0,console.log("[+] All processing queues cleared")}};var D=class{config;defaultConfig;constructor(e){this.defaultConfig=this.createDefaultConfig(),this.config=this.mergeConfigs(this.defaultConfig,e||{})}createDefaultConfig(){return{batch:{batchSize:1,batchInterval:3e4,retryLimit:3,discoveryTimeout:1e4,maxWaitTime:6e4},collection:{realTimeEnabled:!0,batchEnabled:!0,cleanupEnabled:!0,maxCollections:1e3},logging:{debugLevel:"normal",logBatchDetails:!0,logErrorDetails:!0},errorHandling:{maxRetries:3,retryDelay:1e3,ignoreAntiDebugErrors:!0,logErrorDetails:!0,continueOnError:!0},module:{name:"libil2cpp.so",maxWaitTime:3e4,checkInterval:500}}}mergeConfigs(e,r){return{batch:{...e.batch,...r.batch},collection:{...e.collection,...r.collection},logging:{...e.logging,...r.logging},errorHandling:{...e.errorHandling,...r.errorHandling},module:{...e.module,...r.module}}}getConfig(){return{...this.config}}getBatchConfig(){return{...this.config.batch}}getCollectionConfig(){return{...this.config.collection}}getLoggingConfig(){return{...this.config.logging}}getModuleConfig(){return{...this.config.module}}getErrorHandlingConfig(){return{...this.config.errorHandling}}updateBatchConfig(e){this.config.batch={...this.config.batch,...e},console.log("[*] Batch configuration updated:",e)}updateCollectionConfig(e){this.config.collection={...this.config.collection,...e},console.log("[*] Collection configuration updated:",e)}updateLoggingConfig(e){this.config.logging={...this.config.logging,...e},console.log("[*] Logging configuration updated:",e)}updateModuleConfig(e){this.config.module={...this.config.module,...e},console.log("[*] Module configuration updated:",e)}updateErrorHandlingConfig(e){this.config.errorHandling={...this.config.errorHandling,...e},console.log("[*] Error handling configuration updated:",e)}resetToDefaults(){this.config={...this.defaultConfig},console.log("[+] Configuration reset to defaults")}validateConfig(){let e=[];return this.config.batch.batchSize<1&&e.push("Batch size must be at least 1"),this.config.batch.batchInterval<1e3&&e.push("Batch interval must be at least 1000ms"),this.config.batch.retryLimit<0&&e.push("Retry limit cannot be negative"),this.config.batch.discoveryTimeout<1e3&&e.push("Discovery timeout must be at least 1000ms"),this.config.collection.maxCollections<1&&e.push("Max collections must be at least 1"),this.config.errorHandling.maxRetries<0&&e.push("Max retries cannot be negative"),this.config.errorHandling.retryDelay<0&&e.push("Retry delay cannot be negative"),{valid:e.length===0,errors:e}}getConfigSummary(){let e=this.config;return["=== CONFIGURATION SUMMARY ===","Batch Processing:",`  - Batch Size: ${e.batch.batchSize} (${e.batch.batchSize===1?"Real-time":"Batch"} mode)`,`  - Batch Interval: ${e.batch.batchInterval/1e3}s`,`  - Retry Limit: ${e.batch.retryLimit}`,`  - Discovery Timeout: ${e.batch.discoveryTimeout/1e3}s`,"Collection:",`  - Real-time: ${e.collection.realTimeEnabled?"ENABLED":"DISABLED"}`,`  - Batch: ${e.collection.batchEnabled?"ENABLED":"DISABLED"}`,`  - Cleanup: ${e.collection.cleanupEnabled?"ENABLED":"DISABLED"}`,`  - Max Collections: ${e.collection.maxCollections}`,"Logging:",`  - Debug Level: ${e.logging.debugLevel}`,`  - Batch Details: ${e.logging.logBatchDetails?"ON":"OFF"}`,`  - Error Details: ${e.logging.logErrorDetails?"ON":"OFF"}`,"Error Handling:",`  - Max Retries: ${e.errorHandling.maxRetries}`,`  - Retry Delay: ${e.errorHandling.retryDelay}ms`,`  - Ignore Anti-Debug: ${e.errorHandling.ignoreAntiDebugErrors?"ON":"OFF"}`,`  - Continue on Error: ${e.errorHandling.continueOnError?"ON":"OFF"}`,"Module:",`  - Name: ${e.module.name}`,`  - Max Wait Time: ${e.module.maxWaitTime/1e3}s`,`  - Check Interval: ${e.module.checkInterval}ms`,"=============================="].join(`
`)}};var M=class{automationStats;phaseTracker;batchStats;discoveredInstances=new Map;processedInstances=new Set;failedInstances=new Map;errorTrackers=new Map;constructor(){this.automationStats=this.createInitialStats(),this.phaseTracker=this.createInitialPhaseTracker(),this.batchStats=this.createInitialBatchStats()}createInitialStats(){return{totalInstances:0,validInstances:0,invalidInstances:0,upgradeableInstances:0,collectionsPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()}}createInitialPhaseTracker(){return{currentPhase:1,phaseStartTime:Date.now(),phaseEndTime:0,phaseDuration:0,discoveryComplete:!1}}createInitialBatchStats(){return{currentPhase:1,batchNumber:0,isProcessingBatch:!1,discoveredInstances:0,processedInstances:0,failedInstances:0,totalDiscoveryScans:0,lastDiscoveryTime:Date.now(),discoveryCompleteTime:0}}getAutomationStats(){return{...this.automationStats}}recordCollection(){this.automationStats.collectionsPerformed++,this.automationStats.lastUpdateTime=Date.now()}recordDiscoveryScan(){this.batchStats.totalDiscoveryScans++,this.batchStats.lastDiscoveryTime=Date.now(),this.automationStats.lastUpdateTime=Date.now()}updateInstanceCounts(e,r,s,n){this.automationStats.totalInstances=e,this.automationStats.validInstances=r,this.automationStats.invalidInstances=s,this.automationStats.upgradeableInstances=n,this.automationStats.lastUpdateTime=Date.now()}resetAutomationStats(){this.automationStats=this.createInitialStats(),console.log("[+] Automation statistics reset")}getPhaseTracker(){return{...this.phaseTracker}}startDiscoveryPhase(){this.phaseTracker.currentPhase=1,this.phaseTracker.phaseStartTime=Date.now(),this.phaseTracker.discoveryComplete=!1,this.batchStats.currentPhase=1,console.log("[+] Discovery phase started")}completeDiscoveryPhase(){this.phaseTracker.currentPhase=2,this.phaseTracker.phaseEndTime=Date.now(),this.phaseTracker.phaseDuration=this.phaseTracker.phaseEndTime-this.phaseTracker.phaseStartTime,this.phaseTracker.discoveryComplete=!0,this.batchStats.currentPhase=2,this.batchStats.discoveryCompleteTime=this.phaseTracker.phaseEndTime,console.log(`[+] Discovery phase completed in ${(this.phaseTracker.phaseDuration/1e3).toFixed(1)}s`)}resetToDiscoveryPhase(){this.phaseTracker=this.createInitialPhaseTracker(),this.batchStats=this.createInitialBatchStats(),this.clearAllInstances(),console.log("[+] Reset to discovery phase")}getBatchStats(){return this.batchStats.discoveredInstances=this.discoveredInstances.size,this.batchStats.processedInstances=this.processedInstances.size,this.batchStats.failedInstances=this.failedInstances.size,{...this.batchStats}}startBatchProcessing(e){this.batchStats.batchNumber=e,this.batchStats.isProcessingBatch=!0,console.log(`[*] Started batch processing #${e}`)}completeBatchProcessing(){this.batchStats.isProcessingBatch=!1,console.log(`[+] Completed batch processing #${this.batchStats.batchNumber}`)}addDiscoveredInstance(e,r){if(!this.discoveredInstances.has(e)&&!this.processedInstances.has(e)){let s={ptr:r,discoveryTime:Date.now(),retryCount:0,entityId:e};this.discoveredInstances.set(e,s)}}markInstanceProcessed(e){this.processedInstances.add(e),this.discoveredInstances.delete(e),this.failedInstances.delete(e)}markInstanceFailed(e,r,s){let n=this.discoveredInstances.get(e)||this.failedInstances.get(e),o=n?n.retryCount+1:1,i={ptr:r,discoveryTime:n?.discoveryTime||Date.now(),retryCount:o,entityId:e,lastFailTime:Date.now()};this.failedInstances.set(e,i),this.discoveredInstances.delete(e)}getDiscoveredInstances(){return new Map(this.discoveredInstances)}getProcessedInstances(){return new Set(this.processedInstances)}getFailedInstances(){return new Map(this.failedInstances)}clearAllInstances(){this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),console.log("[+] All instance tracking cleared")}clearProcessedInstances(){this.processedInstances.clear(),console.log("[+] Processed instances cleared")}clearFailedInstances(){this.failedInstances.clear(),console.log("[+] Failed instances cleared")}recordError(e,r,s){let n=`${e}_${Date.now()}`,o={errorType:"unknown",errorMessage:r instanceof Error?r.message:String(r),occurredAt:Date.now(),instanceId:s?.toString(),methodName:e,retryCount:1};if(this.errorTrackers.set(n,o),this.errorTrackers.size>100){let i=Array.from(this.errorTrackers.keys())[0];this.errorTrackers.delete(i)}}getErrorStats(){let e=Array.from(this.errorTrackers.values()).filter(r=>Date.now()-r.occurredAt<3e5).sort((r,s)=>s.occurredAt-r.occurredAt);return{totalErrors:this.errorTrackers.size,recentErrors:e}}clearErrorTracking(){this.errorTrackers.clear(),console.log("[+] Error tracking cleared")}getStateSummary(){let e=this.getAutomationStats(),r=this.getPhaseTracker(),s=this.getBatchStats(),n=this.getErrorStats(),o=Date.now()-e.startTime;return["=== AUTOMATION STATE SUMMARY ===",`Uptime: ${`${Math.floor(o/6e4)}m ${Math.floor(o%6e4/1e3)}s`}`,`Current Phase: ${r.currentPhase} (${r.currentPhase===1?"Discovery":"Collection"})`,`Collections Performed: ${e.collectionsPerformed}`,`Total Instances: ${e.totalInstances}`,`Valid Instances: ${e.validInstances}`,`Invalid Instances: ${e.invalidInstances}`,"","Instance Counts:",`  - Discovered: ${this.discoveredInstances.size}`,`  - Processed: ${this.processedInstances.size}`,`  - Failed: ${this.failedInstances.size}`,"","Batch Processing:",`  - Current Batch: #${s.batchNumber}`,`  - Processing: ${s.isProcessingBatch?"YES":"NO"}`,"","Errors:",`  - Total: ${n.totalErrors}`,`  - Recent (5min): ${n.recentErrors.length}`,"================================="].join(`
`)}};var F=class{assemblyImage=null;goodyHutHelperClass=null;methods={CanCollect:null,FinishCollect:null,Config:null,SellRuins:null,Update:null,GetRewardType:null,GetRewardAmount:null,GetCollectTime:null};moduleInfo={module:null,name:"",base:ptr(0),size:0,isValid:!1,loadTime:0};methodAddresses={CanCollect:ptr(0),FinishCollect:ptr(0),Update:ptr(0),Config:ptr(0),SellRuins:ptr(0),GetRewardType:ptr(0),GetRewardAmount:ptr(0)};config;stats={totalInstances:0,validInstances:0,invalidInstances:0,upgradeableInstances:0,collectionsPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()};batchStats={totalDiscoveryScans:0,discoveredInstances:0,processedInstances:0,failedInstances:0,batchNumber:0,isProcessingBatch:!1,currentPhase:1,lastDiscoveryTime:0,discoveryCompleteTime:0};phaseTracker={currentPhase:1,phaseStartTime:Date.now(),discoveryComplete:!1};discoveredInstances=new Map;processedInstances=new Set;failedInstances=new Map;errorTrackers=new Map;hookManager=null;batchProcessor=null;configManager;stateManager;moduleCheckInterval=null;batchProcessingInterval=null;statusMonitoringInterval=null;cleanupInterval=null;constructor(e){console.log("\u{1F680} Starting GoodyHutHelper TypeScript Implementation..."),this.configManager=new D(e),this.stateManager=new M,this.config=this.configManager.getConfig(),console.log("\u2705 GoodyHutHelper instance created with configuration:"),console.log(`   - Batch size: ${this.config.batch.batchSize}`),console.log(`   - Real-time collection: ${this.config.collection.realTimeEnabled}`),console.log(`   - Debug level: ${this.config.logging.debugLevel}`)}createDefaultConfig(){return{batch:{batchSize:1,batchInterval:3e4,retryLimit:3,discoveryTimeout:1e4,maxWaitTime:6e4},errorHandling:{maxRetries:3,retryDelay:1e3,ignoreAntiDebugErrors:!0,logErrorDetails:!0,continueOnError:!0},module:{name:"libil2cpp.so",maxWaitTime:6e4,checkInterval:500},collection:{realTimeEnabled:!0,batchEnabled:!0,cleanupEnabled:!0,maxCollections:1e3},logging:{debugLevel:"normal",logBatchDetails:!0,logErrorDetails:!0}}}async initialize(){try{return console.log("\u{1F50D} Initializing GoodyHutHelper automation system..."),await this.detectIL2CPPModule()?(console.log("\u{1F517} IL2CPP bridge ready..."),await this.findGoodyHutHelperClass()?(this.setupMethodReferences(),this.calculateMethodAddresses(),this.setupMethodHooks(),this.initializeBatchProcessing(),this.setupRPCExports(),console.log("\u2705 GoodyHutHelper initialization complete!"),{success:!0,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses}):{success:!1,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses,errorMessage:"Failed to find GoodyHutHelper class"}):{success:!1,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses,errorMessage:"Failed to detect IL2CPP module"}}catch(e){let r=e instanceof Error?e.message:String(e);return console.log(`\u274C Initialization failed: ${r}`),{success:!1,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses,errorMessage:r}}}async detectIL2CPPModule(){return new Promise(e=>{console.log("[*] Waiting for libil2cpp.so to load...");let r=Date.now(),s=!1,n=()=>{if(Process.enumerateModules().forEach(i=>{if(i.name===this.config.module.name){this.moduleInfo={module:i,name:i.name,base:i.base,size:i.size,isValid:!0,loadTime:Date.now()},s=!0,console.log(`[+] Found ${i.name} at: ${i.base} (size: ${(i.size/1024/1024).toFixed(1)}MB)`);return}}),s){this.moduleCheckInterval&&(clearInterval(this.moduleCheckInterval),this.moduleCheckInterval=null),console.log("[+] IL2CPP module loaded successfully!"),e(!0);return}let o=Date.now()-r;if(o>this.config.module.maxWaitTime){this.moduleCheckInterval&&(clearInterval(this.moduleCheckInterval),this.moduleCheckInterval=null),console.log(`[-] Timeout waiting for ${this.config.module.name} to load after ${this.config.module.maxWaitTime/1e3} seconds`),this.listAvailableModules(),e(!1);return}o%5e3<this.config.module.checkInterval&&console.log(`[*] Still waiting for ${this.config.module.name}... (${(o/1e3).toFixed(1)}s elapsed)`)};n(),s||(this.moduleCheckInterval=setInterval(n,this.config.module.checkInterval))})}async findGoodyHutHelperClass(){try{return this.assemblyImage=Il2Cpp.domain.assembly("Assembly-CSharp").image,this.assemblyImage?(console.log("\u2705 Assembly-CSharp found"),this.goodyHutHelperClass=this.assemblyImage.class("GoodyHutHelper"),this.goodyHutHelperClass?(console.log("\u2705 GoodyHutHelper class found"),console.log(`\u{1F4CB} Class info: ${this.goodyHutHelperClass.name}`),!0):(console.log("\u274C GoodyHutHelper class not found"),this.listAvailableClasses(),!1)):(console.log("\u274C Failed to get Assembly-CSharp image"),this.listAvailableAssemblies(),!1)}catch(e){return console.log(`\u274C Failed to find GoodyHutHelper class: ${e}`),!1}}setupMethodReferences(){try{if(console.log("\u{1F527} Setting up method references..."),!this.goodyHutHelperClass){console.log("\u274C GoodyHutHelper class not available for method setup");return}this.methods.CanCollect=this.goodyHutHelperClass.method("CanCollect"),this.methods.FinishCollect=this.goodyHutHelperClass.method("FinishCollect"),this.methods.Config=this.goodyHutHelperClass.method("Config"),this.methods.Update=this.goodyHutHelperClass.method("Update");try{this.methods.SellRuins=this.goodyHutHelperClass.method("SellRuins"),this.methods.GetRewardType=this.goodyHutHelperClass.method("GetRewardType"),this.methods.GetRewardAmount=this.goodyHutHelperClass.method("GetRewardAmount"),this.methods.GetCollectTime=this.goodyHutHelperClass.method("GetCollectTime")}catch(e){console.log(`\u26A0\uFE0F Some optional methods not found: ${e}`)}Object.entries(this.methods).forEach(([e,r])=>{console.log(r?`\u2705 Found method: ${e}`:`\u26A0\uFE0F Method not found: ${e}`)})}catch(e){console.log(`\u274C Method setup failed: ${e}`)}}calculateMethodAddresses(){if(!this.moduleInfo.isValid||!this.moduleInfo.base){console.log("\u274C Cannot calculate method addresses - invalid module info");return}console.log("\u{1F9EE} Calculating method addresses...");let e={CanCollect:34136164,FinishCollect:34129736,Update:34135404,Config:34128752,SellRuins:34139196,GetRewardType:34134600,GetRewardAmount:34138044};this.methodAddresses.CanCollect=this.moduleInfo.base.add(e.CanCollect),this.methodAddresses.FinishCollect=this.moduleInfo.base.add(e.FinishCollect),this.methodAddresses.Update=this.moduleInfo.base.add(e.Update),this.methodAddresses.Config=this.moduleInfo.base.add(e.Config),this.methodAddresses.SellRuins=this.moduleInfo.base.add(e.SellRuins),this.methodAddresses.GetRewardType=this.moduleInfo.base.add(e.GetRewardType),this.methodAddresses.GetRewardAmount=this.moduleInfo.base.add(e.GetRewardAmount),console.log("\u{1F4CD} Method addresses calculated:"),Object.entries(this.methodAddresses).forEach(([r,s])=>{console.log(`   ${r}: ${s}`)})}listAvailableModules(){console.log("[-] Available modules:"),Process.enumerateModules().forEach(e=>{e.size>5e6&&console.log(`    ${e.name} (${(e.size/1024/1024).toFixed(1)}MB)`)}),console.log("[-] Use setModule('module_name.so') to manually set the correct module")}listAvailableAssemblies(){try{console.log("\u{1F4A1} Available assemblies:"),Il2Cpp.domain.assemblies.slice(0,10).forEach((r,s)=>{console.log(`   ${s}: ${r.name}`)})}catch{console.log("   Could not enumerate assemblies")}}listAvailableClasses(){if(this.assemblyImage)try{console.log("\u{1F4A1} Available classes (first 10):");let e=this.assemblyImage.classes;for(let s=0;s<Math.min(10,e.length);s++)console.log(`   ${s}: ${e[s].name}`);let r=e.filter(s=>s.name.toLowerCase().includes("goody")||s.name.toLowerCase().includes("hut")||s.name.toLowerCase().includes("collect"));r.length>0&&(console.log("\u{1F50D} Found goody/hut/collect related classes:"),r.forEach((s,n)=>{console.log(`   ${n}: ${s.name}`)}))}catch{console.log("   Could not enumerate classes")}}setupMethodHooks(){console.log("\u{1F3A3} Setting up method hooks...");try{this.hookManager=new k(this.methods,this.methodAddresses,this.moduleInfo),this.hookManager.setupAllHooks(),this.hookManager.onCanCollect((e,r)=>{r&&this.handleCanCollectEvent(e)}),this.hookManager.onFinishCollect((e,r)=>{r&&this.handleFinishCollectEvent(e)}),this.hookManager.onUpdate(e=>{this.handleUpdateEvent(e)}),console.log("\u2705 Method hooks setup complete with callbacks")}catch(e){console.log(`\u274C Failed to setup method hooks: ${e}`)}}handleCanCollectEvent(e){let r=e.toString();console.log(`\u{1F3AF} CanCollect event for instance: ${r}`),this.stats.totalInstances++,this.stats.lastUpdateTime=Date.now()}handleFinishCollectEvent(e){let r=e.toString();console.log(`\u2705 FinishCollect event for instance: ${r}`),this.stats.collectionsPerformed++,this.processedInstances.add(r),this.stats.lastUpdateTime=Date.now()}handleUpdateEvent(e){let r=e.toString();if(this.batchStats.totalDiscoveryScans++,this.batchProcessor)this.batchProcessor.addDiscoveredInstance(e,r);else if(!this.processedInstances.has(r)&&!this.discoveredInstances.has(r)){let s={ptr:e,discoveryTime:Date.now(),retryCount:0,entityId:r};this.discoveredInstances.set(r,s),this.batchStats.discoveredInstances=this.discoveredInstances.size}}initializeBatchProcessing(){console.log("\u{1F4E6} Initializing batch processing system...");try{this.batchProcessor=new N(this.config.batch,this.methods,this.methodAddresses),this.batchProcessor.onBatchComplete(e=>{console.log(`\u2705 Batch ${e.batchNumber} completed: ${e.successCount}/${e.totalProcessed} successful`)}),this.batchProcessor.onPhaseChange(e=>{console.log(`\u{1F504} Phase changed to: ${e===1?"DISCOVERY":"COLLECTION"}`),this.phaseTracker.currentPhase=e}),this.batchProcessor.onInstanceProcessed(e=>{e.success&&this.stats.collectionsPerformed++}),this.batchProcessor.startBatchProcessing(),console.log("\u2705 Batch processing system initialized and started")}catch(e){console.log(`\u274C Failed to initialize batch processing: ${e}`)}}setupRPCExports(){console.log("\u{1F50C} Setting up RPC exports..."),console.log("\u26A0\uFE0F RPC exports setup - placeholder implementation")}getStats(){let e=this.stateManager.getAutomationStats(),r=this.stateManager.getBatchStats();return{...e,...r}}getConfig(){return this.configManager.getConfig()}getBatchStatus(){this.batchProcessor?this.batchProcessor.printBatchStatus():(console.log("=== BATCH PROCESSING STATUS ==="),console.log("Batch processor not initialized"),console.log(`Discovered instances: ${this.discoveredInstances.size}`),console.log(`Processed instances: ${this.processedInstances.size}`),console.log(`Total scans performed: ${this.batchStats.totalDiscoveryScans}`),console.log("===================================="))}clearProcessedInstances(){this.processedInstances.clear(),this.stats.collectionsPerformed=0,this.batchProcessor&&this.batchProcessor.clearAllQueues(),console.log("[+] Cleared all processed instances")}clearFailedInstances(){this.failedInstances.clear(),this.batchStats.failedInstances=0,console.log("[+] Cleared all failed instances")}forceBatchProcess(){this.batchProcessor?this.batchProcessor.getPhaseInfo().currentPhase===2?(console.log("[*] Forcing immediate batch processing..."),this.batchProcessor.processCollectionBatch()):console.log("[*] Cannot force batch processing - still in discovery phase"):console.log("[*] Batch processor not initialized")}forceCompleteDiscovery(){this.batchProcessor?this.batchProcessor.getPhaseInfo().currentPhase===1?(console.log("[*] Forcing discovery phase completion..."),this.batchProcessor.completeDiscoveryPhase()):console.log("[*] Discovery phase already completed"):console.log("[*] Batch processor not initialized")}resetToDiscovery(){console.log("[*] Resetting to discovery phase..."),this.phaseTracker.currentPhase=1,this.phaseTracker.discoveryComplete=!1,this.phaseTracker.phaseStartTime=Date.now(),this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.batchStats={currentPhase:1,batchNumber:0,isProcessingBatch:!1,discoveredInstances:0,processedInstances:0,failedInstances:0,totalDiscoveryScans:0,lastDiscoveryTime:Date.now(),discoveryCompleteTime:0},this.stats.collectionsPerformed=0,this.batchProcessor&&this.batchProcessor.resetToDiscovery(),console.log("[+] Reset complete - discovery phase restarted")}setModule(e){console.log(`[*] Attempting to manually set IL2CPP module to: ${e}`);let r=Process.enumerateModules().find(s=>s.name===e);return r?(this.moduleInfo={name:r.name,base:r.base,size:r.size,module:r,isValid:!0,loadTime:Date.now()},console.log(`[+] Successfully set IL2CPP module to: ${this.moduleInfo.name}`),console.log(`[+] Module base: ${this.moduleInfo.base}`),console.log("[+] Reinitializing hooks with new module..."),this.cleanup(),setTimeout(()=>{this.initialize()},1e3),!0):(console.log(`[-] Module '${e}' not found`),console.log("[-] Available modules:"),Process.enumerateModules().forEach(s=>{s.size>1e6&&console.log(`    ${s.name} (${(s.size/1024/1024).toFixed(1)}MB)`)}),!1)}listLargeModules(){console.log("[*] Large modules (>1MB) in process:");let e=[];return Process.enumerateModules().forEach(r=>{r.size>1e6&&e.push({name:r.name,size:r.size,sizeMB:(r.size/1024/1024).toFixed(1),base:r.base.toString()})}),e.sort((r,s)=>s.size-r.size),e.forEach((r,s)=>{console.log(`  ${s+1}. ${r.name} (${r.sizeMB}MB) at ${r.base}`)}),e}getDetailedStats(){let e=this.batchProcessor?.getStats()||this.batchStats;return{phase:(this.batchProcessor?.getPhaseInfo()||this.phaseTracker).currentPhase,discovered:this.discoveredInstances.size,processed:this.processedInstances.size,failed:this.failedInstances.size,batchNumber:e.batchNumber,isProcessing:e.isProcessingBatch,totalScans:e.totalDiscoveryScans,lastDiscovery:e.lastDiscoveryTime,discoveryComplete:e.discoveryCompleteTime}}updateConfig(e){this.config={...this.config,...e},console.log("\u2705 Configuration updated")}cleanup(){console.log("\u{1F9F9} Cleaning up GoodyHutHelper resources..."),this.batchProcessor&&(this.batchProcessor.stopBatchProcessing(),this.batchProcessor=null),this.hookManager&&(this.hookManager.removeAllHooks(),this.hookManager=null),this.moduleCheckInterval&&(clearInterval(this.moduleCheckInterval),this.moduleCheckInterval=null),this.batchProcessingInterval&&(clearInterval(this.batchProcessingInterval),this.batchProcessingInterval=null),this.statusMonitoringInterval&&(clearInterval(this.statusMonitoringInterval),this.statusMonitoringInterval=null),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.errorTrackers.clear(),console.log("\u2705 GoodyHutHelper cleanup complete")}};Il2Cpp.perform(async()=>{console.log("\u{1F680} GoodyHutHelper TypeScript Agent - Il2Cpp bridge context established");try{console.log("\u23F3 Waiting for game to load..."),await new Promise(s=>setTimeout(s,5e3));let t={batch:{batchSize:1,batchInterval:3e4,retryLimit:3,discoveryTimeout:1e4,maxWaitTime:6e4},collection:{realTimeEnabled:!0,batchEnabled:!0,cleanupEnabled:!0,maxCollections:1e3},logging:{debugLevel:"normal",logBatchDetails:!0,logErrorDetails:!0}};console.log("\u{1F527} Initializing GoodyHutHelper automation system...");let e=new F(t),r=await e.initialize();if(r.success){console.log("\u2705 GoodyHutHelper TypeScript agent ready!"),console.log("\u{1F4CA} Initialization Summary:"),console.log(`   - Module: ${r.module.name} (${(r.module.size/1024/1024).toFixed(1)}MB)`),console.log(`   - Methods found: ${Object.values(r.methods).filter(s=>s!==null).length}/8`),console.log(`   - Real-time collection: ${t.collection?.realTimeEnabled?"ENABLED":"DISABLED"}`),console.log(`   - Batch processing: ${t.collection?.batchEnabled?"ENABLED":"DISABLED"}`);try{globalThis.goodyHutHelper=e,console.log("\u2705 Helper instance available as 'goodyHutHelper'")}catch(s){console.log(`\u26A0\uFE0F Could not assign to globalThis: ${s}`);try{global.goodyHutHelper=e,console.log("\u2705 Helper instance available as 'goodyHutHelper' (via global)")}catch(n){console.log(`\u26A0\uFE0F Could not assign to global either: ${n}`)}}try{globalThis.getGoodyHutStats=()=>e.getStats(),globalThis.getGoodyHutConfig=()=>e.getConfig(),globalThis.stopGoodyHutAutomation=()=>e.cleanup(),console.log("\u2705 Global convenience functions available:"),console.log("   - getGoodyHutStats() - Get current automation statistics"),console.log("   - getGoodyHutConfig() - Get current configuration"),console.log("   - stopGoodyHutAutomation() - Stop automation and cleanup")}catch(s){console.log(`\u26A0\uFE0F Could not setup global functions: ${s}`)}try{rpc.exports={getBatchStatus:()=>e.getBatchStatus(),clearProcessedInstances:()=>e.clearProcessedInstances(),clearFailedInstances:()=>e.clearFailedInstances(),forceBatchProcess:()=>e.forceBatchProcess(),forceCompleteDiscovery:()=>e.forceCompleteDiscovery(),resetToDiscovery:()=>e.resetToDiscovery(),setModule:s=>e.setModule(s),listLargeModules:()=>e.listLargeModules(),getStats:()=>e.getDetailedStats()},console.log("\u2705 RPC exports available for manual control:"),console.log("   - getBatchStatus() - Print current batch processing status"),console.log("   - clearProcessedInstances() - Clear all processed instances"),console.log("   - clearFailedInstances() - Clear all failed instances"),console.log("   - forceBatchProcess() - Force immediate batch processing"),console.log("   - forceCompleteDiscovery() - Force discovery phase completion"),console.log("   - resetToDiscovery() - Reset to discovery phase"),console.log("   - setModule(name) - Manually set IL2CPP module"),console.log("   - listLargeModules() - List large modules in process"),console.log("   - getStats() - Get detailed statistics")}catch(s){console.log(`\u26A0\uFE0F Could not setup RPC exports: ${s}`)}setInterval(()=>{let s=e.getStats();console.log(`\u{1F4CA} Status: Processed: ${s.collectionsPerformed}, Discovered: ${s.discoveredInstances}, Scans: ${s.totalDiscoveryScans}`)},6e4),console.log("\u{1F3AF} GoodyHutHelper TypeScript automation is now active!"),console.log("\u{1F4A1} The system will automatically collect ruins when CanCollect() returns true"),console.log("\u{1F4A1} Use global functions or 'goodyHutHelper' instance for manual control")}else console.log("\u274C Failed to initialize GoodyHutHelper TypeScript agent"),console.log(`\u274C Error: ${r.errorMessage}`),console.log("\u{1F527} Troubleshooting:"),console.log("   1. Ensure the game is fully loaded"),console.log("   2. Check if libil2cpp.so is available"),console.log("   3. Verify Assembly-CSharp contains GoodyHutHelper class"),console.log("   4. Try manual module detection with setModule()")}catch(t){console.log(`\u274C Fatal error in GoodyHutHelper TypeScript agent: ${t}`),console.log("\u{1F527} This may be due to:"),console.log("   - Game version incompatibility"),console.log("   - IL2CPP bridge initialization issues"),console.log("   - Missing or changed class/method signatures")}});export{F as GoodyHutHelper};
