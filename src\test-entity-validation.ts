/**
 * Conservative EntityController validation test
 * Tests instance validation without aggressive method invocation
 */

import "frida-il2cpp-bridge";
import { EntityControllerManager } from './entity-controller-manager';

Il2Cpp.perform(async () => {
    console.log("🧪 Testing EntityController instance validation...");
    
    try {
        // Create EntityController manager with conservative settings
        const entityManager = new EntityControllerManager({
            maxRetries: 2,
            retryDelay: 3000,
            upgradeTimeout: 10000,
            batchSize: 3,
            batchDelay: 8000,
            enableLogging: true,
            logLevel: 'verbose'
        });
        
        // Initialize
        console.log("🔄 Initializing EntityController manager...");
        const initialized = await entityManager.initialize();
        
        if (!initialized) {
            console.log("❌ EntityController initialization failed");
            return;
        }
        
        console.log("✅ EntityController manager initialized successfully");
        
        // Get instances
        console.log("🔍 Getting EntityController instances...");
        const instances = entityManager.getAllInstances();
        
        if (instances.length === 0) {
            console.log("⚠️ No EntityController instances found");
            return;
        }
        
        console.log(`✅ Found ${instances.length} EntityController instances`);
        
        // Test validation on first 10 instances
        const testCount = Math.min(10, instances.length);
        console.log(`\n🧪 Testing validation on first ${testCount} instances:`);
        
        let validCount = 0;
        let invalidCount = 0;
        
        for (let i = 0; i < testCount; i++) {
            const instance = instances[i];
            console.log(`\n📋 Testing instance ${i + 1}/${testCount}:`);
            
            try {
                // Test basic validation
                const isValid = await testInstanceValidation(instance, i);
                
                if (isValid) {
                    validCount++;
                    console.log(`  ✅ Instance ${i + 1}: VALID`);
                    
                    // For valid instances, try one safe method call
                    try {
                        const method = instance.method("IsSelected");
                        if (method && method.handle && !method.handle.isNull()) {
                            console.log(`  📋 Instance ${i + 1}: IsSelected method accessible`);
                        } else {
                            console.log(`  ⚠️ Instance ${i + 1}: IsSelected method not accessible`);
                        }
                    } catch (methodError) {
                        console.log(`  ⚠️ Instance ${i + 1}: Method access failed - ${methodError}`);
                    }
                    
                } else {
                    invalidCount++;
                    console.log(`  ❌ Instance ${i + 1}: INVALID`);
                }
                
            } catch (error) {
                invalidCount++;
                console.log(`  ❌ Instance ${i + 1}: Validation error - ${error}`);
            }
            
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log(`\n📊 Validation Results:`);
        console.log(`  Valid instances: ${validCount}/${testCount}`);
        console.log(`  Invalid instances: ${invalidCount}/${testCount}`);
        console.log(`  Success rate: ${((validCount / testCount) * 100).toFixed(1)}%`);
        
        // If we have valid instances, try a very conservative auto-upgrade test
        if (validCount > 0) {
            console.log(`\n🚀 Testing conservative auto-upgrade (max 3 instances)...`);
            
            // Override batch size to be very conservative
            entityManager.updateConfig({
                batchSize: 3,
                batchDelay: 10000,
                maxRetries: 1
            });
            
            const upgradeResult = await entityManager.autoUpgradeSelected();
            console.log(`✅ Conservative auto-upgrade completed: ${upgradeResult} upgrades performed`);
        }
        
        // Show final stats
        const stats = entityManager.getStats();
        console.log("\n📊 Final Statistics:");
        console.log(`  Total Instances: ${stats.totalInstances}`);
        console.log(`  Selected Instances: ${stats.selectedInstances}`);
        console.log(`  Upgradeable Instances: ${stats.upgradeableInstances}`);
        console.log(`  Upgrades Performed: ${stats.upgradesPerformed}`);
        
        console.log("\n✅ EntityController validation test completed!");
        
    } catch (error) {
        console.log(`❌ EntityController validation test failed: ${error}`);
    }
});

/**
 * Conservative instance validation test
 */
async function testInstanceValidation(instance: Il2Cpp.Object, index: number): Promise<boolean> {
    try {
        // Basic null check
        if (!instance) {
            console.log(`    ❌ Instance ${index}: Null instance`);
            return false;
        }
        
        // Handle check
        if (!instance.handle || instance.handle.isNull()) {
            console.log(`    ❌ Instance ${index}: Invalid handle`);
            return false;
        }
        
        console.log(`    ✅ Instance ${index}: Handle valid (${instance.handle})`);
        
        // Class access test
        try {
            const instanceClass = instance.class;
            if (!instanceClass) {
                console.log(`    ❌ Instance ${index}: No class accessible`);
                return false;
            }
            
            console.log(`    ✅ Instance ${index}: Class accessible (${instanceClass.name})`);
            
            // Verify class name
            if (instanceClass.name !== "EntityController") {
                console.log(`    ❌ Instance ${index}: Wrong class (${instanceClass.name})`);
                return false;
            }
            
        } catch (classError) {
            console.log(`    ❌ Instance ${index}: Class access failed - ${classError}`);
            return false;
        }
        
        // Method accessibility test (very conservative)
        try {
            const testMethod = instance.method("IsSelected");
            if (!testMethod) {
                console.log(`    ❌ Instance ${index}: IsSelected method not found`);
                return false;
            }
            
            if (!testMethod.handle || testMethod.handle.isNull()) {
                console.log(`    ❌ Instance ${index}: IsSelected method handle invalid`);
                return false;
            }
            
            console.log(`    ✅ Instance ${index}: IsSelected method accessible`);
            
        } catch (methodError) {
            console.log(`    ❌ Instance ${index}: Method access test failed - ${methodError}`);
            return false;
        }
        
        console.log(`    ✅ Instance ${index}: All validation checks passed`);
        return true;
        
    } catch (error) {
        console.log(`    ❌ Instance ${index}: Validation exception - ${error}`);
        return false;
    }
}

// Make test functions available globally
(globalThis as any).testEntityValidation = () => {
    console.log("🧪 Running EntityController validation test...");
    // The test will run automatically when the script loads
};

(globalThis as any).testInstanceValidation = testInstanceValidation;
