/**
 * GoodyHutHelper TypeScript Agent
 * Main entry point for the TypeScript-based GoodyHutHelper automation
 * Frida IL2CPP Bridge Integration for Dominations Game
 */

import "frida-il2cpp-bridge";
import { GoodyHutHelper } from './goody-hut-helper';
import { GoodyHutAutomationConfig } from './interfaces/goody-hut-interfaces';

/**
 * Main agent execution
 */
Il2Cpp.perform(async () => {
    console.log("🚀 GoodyHutHelper TypeScript Agent - Il2Cpp bridge context established");

    try {
        // Wait for game to be fully loaded
        console.log("⏳ Waiting for game to load...");
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Create custom configuration for enhanced automation
        const customConfig: Partial<GoodyHutAutomationConfig> = {
            batch: {
                batchSize: 1, // Real-time processing
                batchInterval: 30000, // 30 seconds between batch checks
                retryLimit: 3,
                discoveryTimeout: 10000, // 10 seconds
                maxWaitTime: 60000 // 60 seconds
            },
            collection: {
                realTimeEnabled: true,
                batchEnabled: true,
                cleanupEnabled: true,
                maxCollections: 1000
            },
            logging: {
                debugLevel: 'normal',
                logBatchDetails: true,
                logErrorDetails: true
            }
        };

        // Initialize GoodyHutHelper
        console.log("🔧 Initializing GoodyHutHelper automation system...");
        const goodyHutHelper = new GoodyHutHelper(customConfig);

        // Initialize the system
        const initResult = await goodyHutHelper.initialize();

        if (initResult.success) {
            console.log("✅ GoodyHutHelper TypeScript agent ready!");
            console.log("📊 Initialization Summary:");
            console.log(`   - Module: ${initResult.module.name} (${(initResult.module.size/1024/1024).toFixed(1)}MB)`);
            console.log(`   - Methods found: ${Object.values(initResult.methods).filter(m => m !== null).length}/8`);
            console.log(`   - Real-time collection: ${customConfig.collection?.realTimeEnabled ? 'ENABLED' : 'DISABLED'}`);
            console.log(`   - Batch processing: ${customConfig.collection?.batchEnabled ? 'ENABLED' : 'DISABLED'}`);

            // Make the helper instance available globally for debugging
            try {
                (globalThis as any).goodyHutHelper = goodyHutHelper;
                console.log("✅ Helper instance available as 'goodyHutHelper'");
            } catch (error) {
                console.log(`⚠️ Could not assign to globalThis: ${error}`);
                try {
                    (global as any).goodyHutHelper = goodyHutHelper;
                    console.log("✅ Helper instance available as 'goodyHutHelper' (via global)");
                } catch (globalError) {
                    console.log(`⚠️ Could not assign to global either: ${globalError}`);
                }
            }

            // Setup global convenience functions
            try {
                (globalThis as any).getGoodyHutStats = () => goodyHutHelper.getStats();
                (globalThis as any).getGoodyHutConfig = () => goodyHutHelper.getConfig();
                (globalThis as any).stopGoodyHutAutomation = () => goodyHutHelper.cleanup();

                console.log("✅ Global convenience functions available:");
                console.log("   - getGoodyHutStats() - Get current automation statistics");
                console.log("   - getGoodyHutConfig() - Get current configuration");
                console.log("   - stopGoodyHutAutomation() - Stop automation and cleanup");
            } catch (error) {
                console.log(`⚠️ Could not setup global functions: ${error}`);
            }

            // Setup RPC exports for manual control
            try {
                rpc.exports = {
                    getBatchStatus: () => goodyHutHelper.getBatchStatus(),
                    clearProcessedInstances: () => goodyHutHelper.clearProcessedInstances(),
                    clearFailedInstances: () => goodyHutHelper.clearFailedInstances(),
                    forceBatchProcess: () => goodyHutHelper.forceBatchProcess(),
                    forceCompleteDiscovery: () => goodyHutHelper.forceCompleteDiscovery(),
                    resetToDiscovery: () => goodyHutHelper.resetToDiscovery(),
                    setModule: (moduleName: string) => goodyHutHelper.setModule(moduleName),
                    listLargeModules: () => goodyHutHelper.listLargeModules(),
                    getStats: () => goodyHutHelper.getDetailedStats()
                };

                console.log("✅ RPC exports available for manual control:");
                console.log("   - getBatchStatus() - Print current batch processing status");
                console.log("   - clearProcessedInstances() - Clear all processed instances");
                console.log("   - clearFailedInstances() - Clear all failed instances");
                console.log("   - forceBatchProcess() - Force immediate batch processing");
                console.log("   - forceCompleteDiscovery() - Force discovery phase completion");
                console.log("   - resetToDiscovery() - Reset to discovery phase");
                console.log("   - setModule(name) - Manually set IL2CPP module");
                console.log("   - listLargeModules() - List large modules in process");
                console.log("   - getStats() - Get detailed statistics");
            } catch (error) {
                console.log(`⚠️ Could not setup RPC exports: ${error}`);
            }

            // Setup periodic status reporting
            setInterval(() => {
                const stats = goodyHutHelper.getStats();
                console.log(`📊 Status: Processed: ${stats.collectionsPerformed}, Discovered: ${stats.discoveredInstances}, Scans: ${stats.totalDiscoveryScans}`);
            }, 60000); // Every minute

            console.log("🎯 GoodyHutHelper TypeScript automation is now active!");
            console.log("💡 The system will automatically collect ruins when CanCollect() returns true");
            console.log("💡 Use global functions or 'goodyHutHelper' instance for manual control");

        } else {
            console.log("❌ Failed to initialize GoodyHutHelper TypeScript agent");
            console.log(`❌ Error: ${initResult.errorMessage}`);

            // Provide troubleshooting information
            console.log("🔧 Troubleshooting:");
            console.log("   1. Ensure the game is fully loaded");
            console.log("   2. Check if libil2cpp.so is available");
            console.log("   3. Verify Assembly-CSharp contains GoodyHutHelper class");
            console.log("   4. Try manual module detection with setModule()");
        }

    } catch (error) {
        console.log(`❌ Fatal error in GoodyHutHelper TypeScript agent: ${error}`);
        console.log("🔧 This may be due to:");
        console.log("   - Game version incompatibility");
        console.log("   - IL2CPP bridge initialization issues");
        console.log("   - Missing or changed class/method signatures");
    }
});

// Export for potential external use
export { GoodyHutHelper };
export * from './interfaces/goody-hut-interfaces';