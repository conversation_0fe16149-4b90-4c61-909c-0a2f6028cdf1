# Critical Fixes Required for System Stability
## Dominations Unified-Automation System

**Priority Level:** 🔴 IMMEDIATE ACTION REQUIRED  
**Estimated Fix Time:** 2-3 days  
**Risk Level:** HIGH - System may fail on game updates or different devices

---

## 🔴 CRITICAL FIX #1: Method Name Validation and Consistency

### Issue Location:
- **File:** `src/entitycontroller-hook.ts`
- **Lines:** 140, 190
- **Affected Methods:** GetUniqueId/get_uniqueId

### Problem:
```typescript
// Inconsistent method naming - WILL CAUSE RUNTIME FAILURE
this.methods.GetUniqueId = this.entityControllerClass!.method("get_uniqueId");
const entityId = thisEntity.get_uniqueId(); // Different casing assumption
```

### Required Fix:
1. **Verify actual method name** in Dominations IL2CPP using Frida inspection
2. **Standardize naming** across all references
3. **Add method existence validation** before lookup

### Implementation:
```typescript
// Add method validation utility
private validateAndGetMethod(className: string, methodName: string): Il2Cpp.Method | null {
    try {
        const method = this.entityControllerClass!.method(methodName);
        if (!method) {
            console.log(`❌ Method ${methodName} not found in ${className}`);
            return null;
        }
        console.log(`✅ Method ${methodName} validated in ${className}`);
        return method;
    } catch (error) {
        console.log(`❌ Method validation failed: ${error}`);
        return null;
    }
}
```

---

## 🔴 CRITICAL FIX #2: Standardize Method Invocation Patterns

### Issue Location:
- **File:** `src/entitycontroller-hook.ts`
- **Lines:** 530, 540-552, 564
- **Affected:** All EntityController method calls

### Problem:
Three different invocation patterns causing inconsistent behavior:
```typescript
// Pattern 1: Direct call - May bypass IL2CPP properly
const result = entityAsAny.InstantUpgrade();

// Pattern 2: Instance method - CORRECT PATTERN
const method = instance.method(methodName);
const result = method.invoke(...args);

// Pattern 3: Cached method - INCORRECT for instance methods
const result = this.methods.InstantUpgrade.invoke(instance);
```

### Required Fix:
**Standardize on Pattern 2** as documented in `docs/ENTITYCONTROLLER_FIX.md`

### Implementation:
```typescript
// Replace all method calls with this standardized pattern
private standardInvokeMethod(instance: Il2Cpp.Object, methodName: string, args: any[] = []): any {
    try {
        // Get method from instance (not from cached class methods)
        const method = instance.method(methodName);
        if (!method || !method.handle || method.handle.isNull()) {
            throw new Error(`Method ${methodName} not accessible on instance`);
        }
        
        // Invoke with args only (instance is implicit)
        return method.invoke(...args);
    } catch (error) {
        console.log(`❌ Method ${methodName} invocation failed: ${error}`);
        return null;
    }
}
```

---

## 🔴 CRITICAL FIX #3: Remove Version-Specific Hardcoded Addresses

### Issue Location:
- **File:** `src/goody-hut-helper.ts`
- **Lines:** 425-433
- **Risk:** WILL BREAK on game updates

### Problem:
```typescript
// These RVA addresses are game version-specific - REMOVE IMMEDIATELY
const rvaOffsets = {
    CanCollect: 0x208E064,        // ❌ Will break on updates
    FinishCollect: 0x208C748,     // ❌ Will break on updates
    Config: 0x208C6E8,            // ❌ Will break on updates
    Update: 0x208E0C8,            // ❌ Will break on updates
    SellRuins: 0x208C7AC,         // ❌ Will break on updates
    GetRewardType: 0x208C810,     // ❌ Will break on updates
    GetRewardAmount: 0x208C874,   // ❌ Will break on updates
    GetCollectTime: 0x208C8D8     // ❌ Will break on updates
};
```

### Required Fix:
1. **Remove all hardcoded RVA addresses**
2. **Rely exclusively on IL2CPP bridge method discovery**
3. **Add fallback discovery mechanisms**

### Implementation:
```typescript
// Replace hardcoded addresses with dynamic discovery
private setupMethodReferences(): void {
    if (!this.goodyHutHelperClass) {
        console.log("❌ Cannot setup methods - class not found");
        return;
    }

    // Use IL2CPP bridge discovery only
    this.methods = {
        CanCollect: this.goodyHutHelperClass.method("CanCollect"),
        FinishCollect: this.goodyHutHelperClass.method("FinishCollect"),
        Config: this.goodyHutHelperClass.method("Config"),
        Update: this.goodyHutHelperClass.method("Update"),
        SellRuins: this.goodyHutHelperClass.method("SellRuins"),
        GetRewardType: this.goodyHutHelperClass.method("GetRewardType"),
        GetRewardAmount: this.goodyHutHelperClass.method("GetRewardAmount"),
        GetCollectTime: this.goodyHutHelperClass.method("GetCollectTime")
    };

    // Validate all methods were found
    Object.entries(this.methods).forEach(([name, method]) => {
        if (!method) {
            console.log(`⚠️ Method ${name} not found - may not exist in this game version`);
        } else {
            console.log(`✅ Method ${name} discovered successfully`);
        }
    });
}
```

---

## 🔴 CRITICAL FIX #4: Add Parameter Type Conversion

### Issue Location:
- **File:** `src/entitycontroller-hook.ts`
- **Line:** 730
- **Affected:** All method calls with parameters

### Problem:
```typescript
// JavaScript boolean passed directly to IL2CPP - may cause type errors
const canUpgrade = this.safeInvokeMethod(canUpgradeMethod, [true], ...);
```

### Required Fix:
Add proper JavaScript-to-IL2CPP type conversion

### Implementation:
```typescript
// Add type conversion utility
private convertParametersForIL2CPP(args: any[]): any[] {
    return args.map(arg => {
        if (typeof arg === 'boolean') {
            // Convert JavaScript boolean to IL2CPP boolean if needed
            return arg; // Test if direct conversion works, otherwise use Il2Cpp.Boolean
        } else if (typeof arg === 'string') {
            // Convert JavaScript string to IL2CPP string
            return Il2Cpp.String.from(arg);
        } else if (typeof arg === 'number') {
            // Numbers usually convert directly, but verify for specific types
            return arg;
        }
        return arg;
    });
}

// Use in method invocation
private safeInvokeWithTypeConversion(method: Il2Cpp.Method, args: any[] = []): any {
    const convertedArgs = this.convertParametersForIL2CPP(args);
    return method.invoke(...convertedArgs);
}
```

---

## 🔴 CRITICAL FIX #5: Verify All Class and Method Names

### Issue Location:
- **Files:** `src/goody-hut-helper.ts`, `src/entitycontroller-hook.ts`
- **Risk:** Classes/methods may not exist in actual game

### Required Verification:
1. **GoodyHutHelper class** - Verify exists in Assembly-CSharp
2. **EntityController class** - Verify exists and determine namespace
3. **All method names** - Verify exact spelling and casing

### Implementation:
Create verification script:
```typescript
// Add to initialization process
private async verifyGameClasses(): Promise<boolean> {
    try {
        console.log("🔍 Verifying game classes and methods...");
        
        // List all available classes for verification
        const assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
        console.log("📋 Available classes in Assembly-CSharp:");
        
        assemblyImage.classes.forEach(cls => {
            console.log(`  - ${cls.name} (namespace: ${cls.namespace || 'root'})`);
        });
        
        // Verify specific classes
        const goodyHutClass = assemblyImage.class("GoodyHutHelper");
        const entityClass = assemblyImage.class("EntityController");
        
        if (!goodyHutClass) {
            console.log("❌ GoodyHutHelper class not found - check class name");
            return false;
        }
        
        if (!entityClass) {
            console.log("❌ EntityController class not found - check class name and namespace");
            return false;
        }
        
        console.log("✅ Core classes verified");
        return true;
        
    } catch (error) {
        console.log(`❌ Class verification failed: ${error}`);
        return false;
    }
}
```

---

## Implementation Timeline

### Day 1: Critical Method Validation
- [ ] Fix method name inconsistencies (Fix #1)
- [ ] Verify all class names exist in game (Fix #5)
- [ ] Create method validation utilities

### Day 2: Method Invocation Standardization  
- [ ] Standardize all method invocation patterns (Fix #2)
- [ ] Add parameter type conversion (Fix #4)
- [ ] Test method calls with proper patterns

### Day 3: Remove Version Dependencies
- [ ] Remove all hardcoded RVA addresses (Fix #3)
- [ ] Test dynamic method discovery
- [ ] Validate system works without hardcoded addresses

---

## Testing Requirements

After implementing fixes, test:
1. **Method Discovery** - All methods found via IL2CPP bridge
2. **Method Invocation** - All calls use standardized pattern
3. **Parameter Conversion** - Types converted properly
4. **Error Handling** - Graceful failure when methods don't exist
5. **Version Independence** - No hardcoded addresses used

---

## Success Criteria

✅ **All method names verified** against actual game IL2CPP  
✅ **Consistent method invocation** pattern used throughout  
✅ **No hardcoded addresses** remaining in codebase  
✅ **Proper parameter conversion** for all method calls  
✅ **Comprehensive error handling** for method failures

**Risk Mitigation:** These fixes will prevent runtime crashes, improve version compatibility, and reduce detection risk by anti-cheat systems.
