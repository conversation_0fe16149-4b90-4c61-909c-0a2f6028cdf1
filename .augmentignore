# Augment Ignore Rules for Dominations Automation Project
# Prevents common hallucination patterns and maintains code quality

# =============================================================================
# COMPILED OUTPUT AND BUILD ARTIFACTS
# =============================================================================
dist/
build/
out/
*.js.map
*.d.ts.map

# Node modules and package artifacts
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# =============================================================================
# HALLUCINATION PREVENTION PATTERNS
# =============================================================================

# Prevent AI from modifying hardcoded RVA addresses
# These are version-specific and should be removed, not modified
**/rva-addresses.ts
**/hardcoded-offsets.ts
**/memory-addresses.ts

# Prevent modification of verified IL2CPP method signatures
# These should only be changed after manual verification
**/verified-methods.json
**/il2cpp-signatures.json
**/game-methods.json

# Prevent AI from creating fake game classes or methods
# These patterns often indicate hallucinated content
**/*fake*.ts
**/*mock-game*.ts
**/*simulated*.ts
**/*dummy-il2cpp*.ts

# =============================================================================
# SENSITIVE CONFIGURATION FILES
# =============================================================================

# Environment and secrets
.env
.env.local
.env.production
.env.staging
*.key
*.pem
*.p12
secrets.json
config/production.json

# Device-specific configurations
devices.json
device-configs/
frida-server-configs/

# =============================================================================
# DEVELOPMENT AND DEBUGGING FILES
# =============================================================================

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
.idea/
*.swp
*.swo
*~

# Debug and log files
*.log
debug-*.txt
frida-debug-*.log
automation-logs/
crash-dumps/

# Temporary files
*.tmp
*.temp
.cache/
.temp/

# =============================================================================
# TESTING AND COVERAGE
# =============================================================================

# Test coverage reports
coverage/
.nyc_output/
*.lcov

# Test artifacts
test-results/
test-reports/
*.test.log

# =============================================================================
# DOCUMENTATION ARTIFACTS
# =============================================================================

# Generated documentation
docs/generated/
api-docs/
typedoc-output/

# Markdown artifacts
*.md.backup
*.md.tmp

# =============================================================================
# FRIDA-SPECIFIC PATTERNS
# =============================================================================

# Compiled Frida scripts (should be generated, not edited)
*.compiled.js
*-compiled.js
frida-agent-*.js

# Frida server binaries
frida-server*
frida-inject*

# Device-specific Frida configurations
frida-*.config
device-frida-*.json

# =============================================================================
# IL2CPP AND GAME-SPECIFIC PATTERNS
# =============================================================================

# Game-specific files that shouldn't be modified by AI
global-metadata.dat
il2cpp-metadata.json
game-assemblies/
unity-classes.json

# Reverse engineering artifacts
ghidra-projects/
ida-databases/
*.idb
*.i64

# Game memory dumps
*.dmp
memory-dumps/
heap-dumps/

# =============================================================================
# ANTI-HALLUCINATION RULES
# =============================================================================

# Prevent AI from creating files with suspicious patterns
**/*hallucinated*.ts
**/*generated-methods*.ts
**/*auto-discovered*.ts
**/*fake-il2cpp*.ts

# Prevent modification of critical validation files
VALIDATION_AUDIT_REPORT.md
CRITICAL_FIXES_REQUIRED.md
ARCHITECTURE_IMPROVEMENTS.md

# Prevent AI from creating fake game data
**/*dominations-data*.json
**/*game-constants*.ts
**/*extracted-methods*.json

# =============================================================================
# VERSION CONTROL AND DEPLOYMENT
# =============================================================================

# Git artifacts
.git/
.gitignore.backup

# Deployment artifacts
deploy/
deployment-configs/
*.deploy.json

# CI/CD artifacts
.github/workflows/*.backup
ci-configs/
build-scripts/production/

# =============================================================================
# PLATFORM-SPECIFIC FILES
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# =============================================================================
# PACKAGE MANAGER ARTIFACTS
# =============================================================================

# NPM
.npm
.npmrc.backup
package-lock.json.backup

# Yarn
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# =============================================================================
# CUSTOM PROJECT PATTERNS
# =============================================================================

# Backup files created during development
*.backup
*.bak
*.orig

# Experimental files that shouldn't be included
experimental/
sandbox/
playground/
scratch/

# Personal notes and TODO files
PERSONAL_NOTES.md
TODO_PRIVATE.md
RESEARCH_NOTES.md

# =============================================================================
# SECURITY AND COMPLIANCE
# =============================================================================

# Security scan results
security-reports/
vulnerability-scans/
*.security.json

# Compliance artifacts
compliance-reports/
audit-logs/
*.compliance.json

# =============================================================================
# PERFORMANCE AND MONITORING
# =============================================================================

# Performance profiling data
*.prof
*.perf
profiling-data/
performance-logs/

# Monitoring and metrics
metrics/
monitoring-configs/
*.metrics.json

# =============================================================================
# COMMENTS FOR AI GUIDANCE
# =============================================================================

# IMPORTANT NOTES FOR AI ASSISTANTS:
#
# 1. DO NOT modify files containing hardcoded RVA addresses
#    - These are game version-specific and will break on updates
#    - Remove them entirely rather than updating them
#
# 2. DO NOT create new IL2CPP method signatures without verification
#    - All method names must be verified against actual game IL2CPP
#    - Use Il2Cpp.domain.assembly().image.classes to discover real methods
#
# 3. DO NOT assume class names or namespaces exist
#    - Verify all class references against actual game assemblies
#    - Use fuzzy matching to suggest similar classes if exact match fails
#
# 4. DO NOT create fake game data or constants
#    - All game-specific values must come from actual reverse engineering
#    - Document the source of all game-specific information
#
# 5. ALWAYS use the IL2CPP bridge for method discovery
#    - Avoid hardcoded memory addresses or RVA offsets
#    - Prefer dynamic discovery over static addresses
#
# 6. VALIDATE all method invocation patterns
#    - Use instance.method(name).invoke(args) for instance methods
#    - Use class.method(name).invoke(instance, args) for static methods
#    - Never mix these patterns
#
# 7. IMPLEMENT proper error handling
#    - Check method existence before invocation
#    - Validate instance pointers before use
#    - Use anti-debugging protection for critical operations
#
# 8. MAINTAIN type safety
#    - Convert JavaScript types to IL2CPP types when needed
#    - Use Il2Cpp.String.from() for string parameters
#    - Validate parameter types match method signatures