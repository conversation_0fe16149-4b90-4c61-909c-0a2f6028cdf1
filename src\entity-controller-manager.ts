/**
 * EntityController Manager for Unified Automation
 * Integrates EntityController automation with the GoodyHutHelper architecture
 */

import "frida-il2cpp-bridge";
import {
    EntityControllerMethods,
    EntityUpgradeTracker,
    EntityControllerStats,
    UpgradeValidationResult,
    EntityUpgradeResult,
    EntityControllerConfig
} from './interfaces/goody-hut-interfaces';
import { AntiDebugProtection } from './anti-debug-protection';

/**
 * EntityController automation manager
 */
export class EntityControllerManager {
    private assemblyImage: Il2Cpp.Image | null = null;
    private entityControllerClass: Il2Cpp.Class | null = null;
    private methods: EntityControllerMethods;
    private antiDebugProtection: AntiDebugProtection;
    private config: EntityControllerConfig;
    private stats: EntityControllerStats;
    private upgradeTrackers: Map<string, EntityUpgradeTracker> = new Map();
    private isInitialized: boolean = false;
    
    constructor(config?: Partial<EntityControllerConfig>) {
        this.config = {
            maxRetries: 5,
            retryDelay: 1000,
            upgradeTimeout: 10000,
            batchSize: 10,
            batchDelay: 2000,
            enableLogging: true,
            logLevel: 'normal',
            ...config
        };
        
        this.methods = {
            IsSelected: null,
            CanUpgrade: null,
            GetLevel: null,
            GetMaxLevel: null,
            GetMaxUpgradeLevel: null,
            InstantUpgrade: null,
            Select: null,
            Unselect: null,
            GetUniqueId: null,
            IsUpgrading: null,
            GetUpgradeTime: null,
            GetUpgradeCost: null
        };
        
        this.stats = {
            totalInstances: 0,
            selectedInstances: 0,
            upgradeableInstances: 0,
            upgradesPerformed: 0,
            startTime: Date.now(),
            lastUpdateTime: Date.now()
        };
        
        this.antiDebugProtection = new AntiDebugProtection({
            maxRetries: this.config.maxRetries,
            retryDelay: this.config.retryDelay,
            ignoreKnownErrors: true,
            logErrorDetails: this.config.enableLogging
        });
        
        this.log('🚀 EntityController Manager initialized');
    }
    
    /**
     * Initialize the EntityController manager
     */
    public async initialize(): Promise<boolean> {
        try {
            this.log('🔍 Initializing EntityController manager...');
            
            // Wait for IL2CPP domain
            await Il2Cpp.perform(() => {});
            
            // Find Assembly-CSharp
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                this.log('❌ Assembly-CSharp not found');
                return false;
            }
            
            // Find EntityController class
            this.entityControllerClass = this.assemblyImage.class("EntityController");
            if (!this.entityControllerClass) {
                this.log('❌ EntityController class not found');
                return false;
            }
            
            this.log('✅ EntityController class found');
            
            // Setup methods
            await this.setupMethods();
            
            this.isInitialized = true;
            this.log('✅ EntityController manager initialized successfully');
            
            return true;
            
        } catch (error) {
            this.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }
    
    /**
     * Setup method references with anti-debugging protection
     */
    private async setupMethods(): Promise<void> {
        const operationId = 'setup_methods';
        
        await this.antiDebugProtection.executeWithProtection(
            operationId,
            () => {
                this.log('🔧 Setting up method references...');
                
                // Core methods
                this.methods.IsSelected = this.entityControllerClass!.method("IsSelected");
                this.methods.CanUpgrade = this.entityControllerClass!.method("CanUpgrade");
                this.methods.GetLevel = this.entityControllerClass!.method("GetLevel");
                this.methods.GetMaxLevel = this.entityControllerClass!.method("GetMaxLevel");
                this.methods.GetMaxUpgradeLevel = this.entityControllerClass!.method("GetMaxUpgradeLevel");
                this.methods.InstantUpgrade = this.entityControllerClass!.method("InstantUpgrade");
                
                // Optional methods
                try {
                    this.methods.Select = this.entityControllerClass!.method("Select");
                    this.methods.Unselect = this.entityControllerClass!.method("Unselect");
                    this.methods.GetUniqueId = this.entityControllerClass!.method("get_uniqueId");
                    this.methods.IsUpgrading = this.entityControllerClass!.method("IsUpgrading");
                    this.methods.GetUpgradeTime = this.entityControllerClass!.method("GetUpgradeTime");
                    this.methods.GetUpgradeCost = this.entityControllerClass!.method("GetUpgradeCost");
                } catch (error) {
                    this.log(`⚠️ Some optional methods not found: ${error}`);
                }
                
                this.log('✅ Method references setup complete');
            },
            'EntityController method setup'
        );
    }
    
    /**
     * Get all EntityController instances
     */
    public getAllInstances(): Il2Cpp.Object[] {
        if (!this.isInitialized || !this.entityControllerClass) {
            this.log('❌ EntityController manager not initialized');
            return [];
        }
        
        try {
            const instances = Il2Cpp.gc.choose(this.entityControllerClass);
            this.stats.totalInstances = instances.length;
            this.stats.lastUpdateTime = Date.now();
            
            this.log(`🔍 Found ${instances.length} EntityController instances`);
            return instances;
            
        } catch (error) {
            this.log(`❌ Failed to get instances: ${error}`);
            return [];
        }
    }
    
    /**
     * Auto-upgrade selected entities with anti-debugging protection
     */
    public async autoUpgradeSelected(): Promise<number> {
        if (!this.isInitialized) {
            this.log('❌ EntityController manager not initialized');
            return 0;
        }
        
        const operationId = 'auto_upgrade_selected';
        
        const result = await this.antiDebugProtection.executeWithProtection(
            operationId,
            async () => {
                this.log('🚀 Starting auto-upgrade for selected entities...');
                
                const instances = this.getAllInstances();
                if (instances.length === 0) {
                    this.log('⚠️ No EntityController instances found');
                    return 0;
                }
                
                let totalUpgrades = 0;
                let selectedCount = 0;
                let upgradeableCount = 0;
                
                // Filter valid instances first
                this.log(`🔍 Filtering valid instances from ${instances.length} total...`);
                const validInstances: Il2Cpp.Object[] = [];

                for (let i = 0; i < instances.length; i++) {
                    if (this.validateInstance(instances[i])) {
                        validInstances.push(instances[i]);
                    }

                    // Log progress every 500 instances
                    if ((i + 1) % 500 === 0) {
                        this.log(`📊 Validated ${i + 1}/${instances.length} instances, found ${validInstances.length} valid`);
                    }
                }

                this.log(`✅ Found ${validInstances.length} valid instances out of ${instances.length} total`);

                if (validInstances.length === 0) {
                    this.log('⚠️ No valid EntityController instances found');
                    return 0;
                }

                // Process valid instances in batches
                for (let i = 0; i < validInstances.length; i += this.config.batchSize) {
                    const batch = validInstances.slice(i, i + this.config.batchSize);

                    for (let j = 0; j < batch.length; j++) {
                        const instance = batch[j];
                        const globalIndex = i + j;

                        try {
                            
                            // Check if selected
                            const isSelected = this.safeInvokeInstanceMethod(instance, 'IsSelected', [], `Instance ${globalIndex} IsSelected`);
                            if (!isSelected) {
                                continue;
                            }
                            
                            selectedCount++;
                            
                            // Validate upgrade state
                            const validation = this.validateUpgradeState(instance);
                            if (!validation.canUpgrade) {
                                this.log(`📋 Entity cannot upgrade: ${validation.reason}`, 'verbose');
                                continue;
                            }
                            
                            upgradeableCount++;
                            
                            // Perform upgrade
                            const upgradeResult = await this.upgradeEntity(instance);
                            if (upgradeResult.success) {
                                totalUpgrades += upgradeResult.upgradeCount;
                                this.stats.upgradesPerformed += upgradeResult.upgradeCount;
                            }
                            
                        } catch (error) {
                            this.log(`❌ Error processing entity ${globalIndex}: ${error}`);
                        }
                    }

                    // Delay between batches
                    if (i + this.config.batchSize < validInstances.length) {
                        await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
                    }
                }
                
                this.stats.selectedInstances = selectedCount;
                this.stats.upgradeableInstances = upgradeableCount;
                this.stats.lastUpdateTime = Date.now();
                
                this.log(`✅ Auto-upgrade complete: ${totalUpgrades} upgrades performed`);
                this.log(`📊 Selected: ${selectedCount}, Upgradeable: ${upgradeableCount}`);
                
                return totalUpgrades;
            },
            'EntityController auto-upgrade'
        );
        
        return result || 0;
    }
    
    /**
     * Validate if an IL2CPP instance is safe to use with comprehensive testing
     */
    private validateInstance(instance: Il2Cpp.Object): boolean {
        try {
            if (!instance) {
                return false;
            }

            // Check if instance handle is valid
            if (!instance.handle || instance.handle.isNull()) {
                return false;
            }

            // Try to access the class - this will fail for destroyed objects
            const instanceClass = instance.class;
            if (!instanceClass) {
                return false;
            }

            // Verify it's actually an EntityController
            if (instanceClass.name !== "EntityController") {
                return false;
            }

            // This is the key test - many objects have valid handles
            // but their methods are no longer accessible
            try {
                const testMethod = instance.method("IsSelected");
                if (!testMethod || !testMethod.handle || testMethod.handle.isNull()) {
                    return false;
                }

                // Additional method accessibility test
                const testMethod2 = instance.method("GetLevel");
                if (!testMethod2 || !testMethod2.handle || testMethod2.handle.isNull()) {
                    return false;
                }

            } catch (methodError) {
                // Method access failed - this instance is destroyed/invalid
                return false;
            }

            return true;
        } catch (error) {
            // Any exception means the instance is invalid
            return false;
        }
    }
    
    /**
     * Safely get a method from an instance with validation
     */
    private safeGetMethod(instance: Il2Cpp.Object, methodName: string): Il2Cpp.Method | null {
        try {
            if (!this.validateInstance(instance)) {
                return null;
            }

            const method = instance.method(methodName);
            if (!method) {
                return null;
            }

            // Verify method handle is valid
            if (!method.handle || method.handle.isNull()) {
                return null;
            }

            return method;
        } catch (error) {
            return null;
        }
    }

    /**
     * Safely invoke a method with enhanced error handling for anti-debugging
     */
    private safeInvokeMethod(method: Il2Cpp.Method, args: any[] = [], context: string = "Unknown"): any {
        try {
            if (!method) {
                this.log(`⚠️ ${context}: Method is null`, 'verbose');
                return null;
            }

            const result = method.invoke(...args);
            return result;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);

            // Handle specific anti-debugging errors
            if (errorMsg.includes('access violation') ||
                errorMsg.includes('abort was called') ||
                errorMsg.includes('breakpoint')) {
                this.log(`⚠️ ${context}: Anti-debug protection triggered - ${errorMsg}`, 'verbose');
                return null;
            }

            this.log(`❌ ${context}: Method invocation failed - ${errorMsg}`, 'verbose');
            return null;
        }
    }

    /**
     * Safely invoke an instance method using the correct pattern
     */
    private safeInvokeInstanceMethod(instance: Il2Cpp.Object, methodName: string, args: any[] = [], context: string = "Unknown"): any {
        const operationId = `invoke_${methodName}_${instance.handle.toString()}`;

        return this.antiDebugProtection.executeWithProtection(
            operationId,
            () => {
                // Get method from instance
                const method = this.safeGetMethod(instance, methodName);
                if (!method) {
                    throw new Error(`Method ${methodName} not accessible on instance`);
                }

                // Invoke method with args
                return this.safeInvokeMethod(method, args, context);
            },
            `EntityController.${methodName}`
        );
    }
    
    /**
     * Validate upgrade state for an entity
     */
    private validateUpgradeState(instance: Il2Cpp.Object): UpgradeValidationResult {
        try {
            const canUpgrade = this.safeInvokeInstanceMethod(instance, 'CanUpgrade', [true], 'CanUpgrade validation');
            const isUpgrading = this.safeInvokeInstanceMethod(instance, 'IsUpgrading', [], 'IsUpgrading validation') || false;
            const currentLevel = this.safeInvokeInstanceMethod(instance, 'GetLevel', [], 'GetLevel validation') || 0;
            const maxLevel = this.safeInvokeInstanceMethod(instance, 'GetMaxUpgradeLevel', [], 'GetMaxUpgradeLevel validation') || 0;
            
            if (isUpgrading) {
                return {
                    canUpgrade: false,
                    isUpgrading: true,
                    currentLevel,
                    maxLevel,
                    reason: 'Already upgrading'
                };
            }
            
            if (!canUpgrade) {
                return {
                    canUpgrade: false,
                    isUpgrading: false,
                    currentLevel,
                    maxLevel,
                    reason: 'Cannot upgrade (resources/requirements not met)'
                };
            }
            
            if (currentLevel >= maxLevel - 1) {
                return {
                    canUpgrade: false,
                    isUpgrading: false,
                    currentLevel,
                    maxLevel,
                    reason: 'At max level or max-1 (leaving final upgrade for manual)'
                };
            }
            
            return {
                canUpgrade: true,
                isUpgrading: false,
                currentLevel,
                maxLevel,
                reason: 'Ready for upgrade'
            };
            
        } catch (error) {
            return {
                canUpgrade: false,
                isUpgrading: false,
                currentLevel: 0,
                maxLevel: 0,
                reason: `Validation error: ${error}`
            };
        }
    }
    
    /**
     * Upgrade entity with retry logic
     */
    private async upgradeEntity(instance: Il2Cpp.Object): Promise<EntityUpgradeResult> {
        const entityId = this.getEntityId(instance);
        const initialLevel = this.safeInvokeInstanceMethod(instance, 'GetLevel', [], `${entityId} GetLevel initial`) || 0;
        let upgradeCount = 0;
        
        const operationId = `upgrade_entity_${entityId}`;
        
        const result = await this.antiDebugProtection.executeWithProtection(
            operationId,
            async () => {
                const maxLevel = this.safeInvokeInstanceMethod(instance, 'GetMaxUpgradeLevel', [], `${entityId} GetMaxUpgradeLevel`) || 0;
                let currentLevel = initialLevel;
                
                // Upgrade until max-1 level
                while (currentLevel < maxLevel - 1) {
                    // Check if can still upgrade
                    const canUpgrade = this.safeInvokeInstanceMethod(instance, 'CanUpgrade', [true], `${entityId} CanUpgrade check`);
                    if (!canUpgrade) {
                        break;
                    }

                    // Perform upgrade
                    this.safeInvokeInstanceMethod(instance, 'InstantUpgrade', [], `${entityId} InstantUpgrade`);
                    upgradeCount++;
                    
                    // Wait a bit and check new level
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    const newLevel = this.safeInvokeInstanceMethod(instance, 'GetLevel', [], `${entityId} GetLevel check`) || currentLevel;
                    if (newLevel <= currentLevel) {
                        // Level didn't increase, might need more time or failed
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        const recheckLevel = this.safeInvokeInstanceMethod(instance, 'GetLevel', [], `${entityId} GetLevel recheck`) || currentLevel;
                        if (recheckLevel <= currentLevel) {
                            this.log(`⚠️ Upgrade may have failed, level didn't increase: ${currentLevel}`);
                            break;
                        }
                        currentLevel = recheckLevel;
                    } else {
                        currentLevel = newLevel;
                    }
                    
                    this.log(`📈 Entity ${entityId}: Level ${currentLevel}/${maxLevel}`, 'verbose');
                }
                
                return {
                    success: upgradeCount > 0,
                    newLevel: currentLevel,
                    waitTime: 0,
                    upgradeCount
                };
            },
            `EntityController upgrade for ${entityId}`
        );
        
        return result || {
            success: false,
            newLevel: initialLevel,
            waitTime: 0,
            upgradeCount: 0
        };
    }
    
    /**
     * Get entity ID for tracking
     */
    private getEntityId(instance: Il2Cpp.Object): string {
        try {
            const uniqueId = this.safeInvokeInstanceMethod(instance, 'GetUniqueId', [], 'GetUniqueId');
            return uniqueId ? uniqueId.toString() : instance.handle.toString();
        } catch {
            return instance.handle.toString();
        }
    }
    
    /**
     * Get current statistics
     */
    public getStats(): EntityControllerStats {
        return { ...this.stats };
    }
    
    /**
     * Get current configuration
     */
    public getConfig(): EntityControllerConfig {
        return { ...this.config };
    }
    
    /**
     * Update configuration
     */
    public updateConfig(updates: Partial<EntityControllerConfig>): void {
        this.config = { ...this.config, ...updates };
        this.log('⚙️ Configuration updated');
    }
    
    /**
     * Cleanup resources
     */
    public cleanup(): void {
        this.upgradeTrackers.clear();
        this.log('🧹 EntityController manager cleaned up');
    }
    
    /**
     * Logging helper
     */
    private log(message: string, level: 'minimal' | 'normal' | 'verbose' = 'normal'): void {
        if (!this.config.enableLogging) return;
        
        const logLevels = { minimal: 0, normal: 1, verbose: 2 };
        const currentLevel = logLevels[this.config.logLevel];
        const messageLevel = logLevels[level];
        
        if (messageLevel <= currentLevel) {
            console.log(`[EntityController] ${message}`);
        }
    }
}
