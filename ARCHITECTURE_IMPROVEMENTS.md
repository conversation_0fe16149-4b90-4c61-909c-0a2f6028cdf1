# Architecture Improvements and Code Consolidation
## Dominations Unified-Automation System

**Focus:** Long-term maintainability, code reuse, and system robustness  
**Timeline:** 2-4 weeks implementation  
**Benefits:** Reduced code duplication, improved reliability, easier maintenance

---

## 1. Shared IL2CPP Method Invocation Utility

### Current Problem:
Method invocation logic is duplicated across multiple files with inconsistent patterns:
- `src/entitycontroller-hook.ts` - 3 different patterns
- `src/entity-controller-manager.ts` - Similar but different implementation  
- `src/method-hooks.ts` - Basic pattern without full validation

### Proposed Solution:
Create a centralized `IL2CPPMethodInvoker` utility class.

### Implementation:
```typescript
// src/utils/il2cpp-method-invoker.ts
export class IL2CPPMethodInvoker {
    private antiDebugProtection: AntiDebugProtection;
    
    constructor(antiDebugProtection: AntiDebugProtection) {
        this.antiDebugProtection = antiDebugProtection;
    }
    
    /**
     * Safely invoke an instance method with full validation and error handling
     */
    public async invokeInstanceMethod<T = any>(
        instance: Il2Cpp.Object,
        methodName: string,
        args: any[] = [],
        options: InvocationOptions = {}
    ): Promise<T | null> {
        const operationId = `invoke_${methodName}_${instance.handle.toString()}`;
        
        return this.antiDebugProtection.executeWithProtection(
            operationId,
            () => this.performMethodInvocation<T>(instance, methodName, args, options),
            `IL2CPP.${methodName}`
        );
    }
    
    /**
     * Safely invoke a static method with validation
     */
    public async invokeStaticMethod<T = any>(
        classRef: Il2Cpp.Class,
        methodName: string,
        args: any[] = [],
        options: InvocationOptions = {}
    ): Promise<T | null> {
        // Implementation for static methods
    }
    
    private performMethodInvocation<T>(
        instance: Il2Cpp.Object,
        methodName: string,
        args: any[],
        options: InvocationOptions
    ): T | null {
        // Validate instance
        if (!this.validateInstance(instance)) {
            throw new Error("Invalid instance");
        }
        
        // Get method from instance
        const method = instance.method(methodName);
        if (!method || !method.handle || method.handle.isNull()) {
            throw new Error(`Method ${methodName} not accessible`);
        }
        
        // Convert parameters
        const convertedArgs = this.convertParameters(args, options.parameterTypes);
        
        // Invoke method
        return method.invoke(...convertedArgs);
    }
    
    private convertParameters(args: any[], expectedTypes?: string[]): any[] {
        return args.map((arg, index) => {
            const expectedType = expectedTypes?.[index];
            
            if (typeof arg === 'string') {
                return Il2Cpp.String.from(arg);
            } else if (typeof arg === 'boolean') {
                // Handle boolean conversion if needed
                return arg;
            }
            
            return arg;
        });
    }
    
    private validateInstance(instance: Il2Cpp.Object): boolean {
        return instance && instance.handle && !instance.handle.isNull();
    }
}

interface InvocationOptions {
    parameterTypes?: string[];
    timeout?: number;
    retryCount?: number;
    logLevel?: 'none' | 'basic' | 'verbose';
}
```

### Benefits:
- **Consistent behavior** across all method invocations
- **Centralized error handling** and anti-debug protection
- **Parameter type conversion** handled automatically
- **Easier testing** and maintenance

---

## 2. Unified Class Discovery and Validation System

### Current Problem:
Class discovery is scattered across files with different error handling:
- `src/goody-hut-helper.ts` - Manual class lookup with basic validation
- `src/entitycontroller-hook.ts` - Similar but different implementation
- No systematic validation of class existence

### Proposed Solution:
Create a `ClassDiscoveryManager` for centralized class management.

### Implementation:
```typescript
// src/utils/class-discovery-manager.ts
export class ClassDiscoveryManager {
    private discoveredClasses: Map<string, Il2Cpp.Class> = new Map();
    private assemblyCache: Map<string, Il2Cpp.Image> = new Map();
    
    /**
     * Discover and validate a class with comprehensive error handling
     */
    public async discoverClass(
        assemblyName: string,
        className: string,
        namespace?: string
    ): Promise<Il2Cpp.Class | null> {
        const classKey = `${assemblyName}.${namespace || 'root'}.${className}`;
        
        // Check cache first
        if (this.discoveredClasses.has(classKey)) {
            return this.discoveredClasses.get(classKey)!;
        }
        
        try {
            // Get assembly
            const assembly = await this.getAssembly(assemblyName);
            if (!assembly) {
                return null;
            }
            
            // Find class
            const classRef = namespace 
                ? assembly.class(className, namespace)
                : assembly.class(className);
                
            if (!classRef) {
                console.log(`❌ Class ${className} not found in ${assemblyName}`);
                await this.suggestSimilarClasses(assembly, className);
                return null;
            }
            
            // Cache and return
            this.discoveredClasses.set(classKey, classRef);
            console.log(`✅ Class ${className} discovered in ${assemblyName}`);
            return classRef;
            
        } catch (error) {
            console.log(`❌ Class discovery failed: ${error}`);
            return null;
        }
    }
    
    /**
     * Validate all required classes exist before starting automation
     */
    public async validateRequiredClasses(requirements: ClassRequirement[]): Promise<ValidationResult> {
        const results: ValidationResult = {
            success: true,
            discoveredClasses: new Map(),
            missingClasses: [],
            suggestions: new Map()
        };
        
        for (const req of requirements) {
            const classRef = await this.discoverClass(req.assembly, req.className, req.namespace);
            
            if (classRef) {
                results.discoveredClasses.set(req.key, classRef);
            } else {
                results.success = false;
                results.missingClasses.push(req);
            }
        }
        
        return results;
    }
    
    private async suggestSimilarClasses(assembly: Il2Cpp.Image, targetName: string): Promise<void> {
        console.log(`🔍 Searching for similar classes to '${targetName}':`);
        
        assembly.classes.forEach(cls => {
            const similarity = this.calculateSimilarity(cls.name, targetName);
            if (similarity > 0.6) {
                console.log(`  📋 Similar: ${cls.name} (${Math.round(similarity * 100)}% match)`);
            }
        });
    }
    
    private calculateSimilarity(str1: string, str2: string): number {
        // Simple similarity calculation - can be improved
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }
    
    private levenshteinDistance(str1: string, str2: string): number {
        // Standard Levenshtein distance implementation
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        
        for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
        for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
        
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(
                    matrix[j][i - 1] + 1,
                    matrix[j - 1][i] + 1,
                    matrix[j - 1][i - 1] + indicator
                );
            }
        }
        
        return matrix[str2.length][str1.length];
    }
}

interface ClassRequirement {
    key: string;
    assembly: string;
    className: string;
    namespace?: string;
    required: boolean;
}

interface ValidationResult {
    success: boolean;
    discoveredClasses: Map<string, Il2Cpp.Class>;
    missingClasses: ClassRequirement[];
    suggestions: Map<string, string[]>;
}
```

---

## 3. Centralized Configuration and Type System

### Current Problem:
- Configuration scattered across multiple files
- TypeScript interfaces duplicated
- No runtime type validation

### Proposed Solution:
Create unified configuration and type system.

### Implementation:
```typescript
// src/config/automation-config.ts
export class AutomationConfigManager {
    private static instance: AutomationConfigManager;
    private config: UnifiedAutomationConfig;
    
    public static getInstance(): AutomationConfigManager {
        if (!AutomationConfigManager.instance) {
            AutomationConfigManager.instance = new AutomationConfigManager();
        }
        return AutomationConfigManager.instance;
    }
    
    public loadConfig(configPath?: string): UnifiedAutomationConfig {
        // Load configuration from file or use defaults
        this.config = this.mergeWithDefaults(this.loadFromFile(configPath));
        this.validateConfig();
        return this.config;
    }
    
    public getGoodyHutConfig(): GoodyHutAutomationConfig {
        return this.config.goodyHut;
    }
    
    public getEntityControllerConfig(): EntityControllerConfig {
        return this.config.entityController;
    }
    
    private validateConfig(): void {
        // Runtime configuration validation
        if (this.config.goodyHut.batch.batchSize <= 0) {
            throw new Error("Invalid batch size configuration");
        }
        
        if (this.config.entityController.maxRetries < 1) {
            throw new Error("Invalid retry configuration");
        }
    }
}

// src/types/unified-types.ts - Consolidate all interfaces
export interface UnifiedAutomationConfig {
    goodyHut: GoodyHutAutomationConfig;
    entityController: EntityControllerConfig;
    logging: LoggingConfig;
    antiDebug: AntiDebugConfig;
    discovery: ClassDiscoveryConfig;
}

export interface ClassDiscoveryConfig {
    assemblies: string[];
    classRequirements: ClassRequirement[];
    discoveryTimeout: number;
    enableSuggestions: boolean;
}
```

---

## 4. Enhanced Error Handling and Logging Framework

### Current Problem:
- Inconsistent logging patterns
- Different error handling approaches
- No centralized error tracking

### Proposed Solution:
Unified logging and error handling system.

### Implementation:
```typescript
// src/utils/enhanced-logger.ts
export class EnhancedLogger {
    private logLevel: LogLevel;
    private logHistory: LogEntry[] = [];
    private errorTracker: Map<string, ErrorStats> = new Map();
    
    constructor(logLevel: LogLevel = 'normal') {
        this.logLevel = logLevel;
    }
    
    public logMethodCall(
        className: string,
        methodName: string,
        result: any,
        duration: number,
        context?: string
    ): void {
        const entry: LogEntry = {
            timestamp: Date.now(),
            level: 'info',
            category: 'method_call',
            message: `${className}.${methodName}`,
            metadata: { result, duration, context }
        };
        
        this.addLogEntry(entry);
        
        if (this.logLevel === 'verbose') {
            console.log(`🔧 ${className}.${methodName} -> ${result} (${duration}ms)`);
        }
    }
    
    public logError(
        error: Error,
        context: string,
        className?: string,
        methodName?: string
    ): void {
        const errorKey = `${className || 'unknown'}.${methodName || 'unknown'}`;
        
        // Track error statistics
        const stats = this.errorTracker.get(errorKey) || { count: 0, lastOccurrence: 0 };
        stats.count++;
        stats.lastOccurrence = Date.now();
        this.errorTracker.set(errorKey, stats);
        
        const entry: LogEntry = {
            timestamp: Date.now(),
            level: 'error',
            category: 'error',
            message: error.message,
            metadata: { context, className, methodName, stack: error.stack }
        };
        
        this.addLogEntry(entry);
        console.log(`❌ ${context}: ${error.message}`);
    }
    
    public getErrorStatistics(): Map<string, ErrorStats> {
        return new Map(this.errorTracker);
    }
    
    public exportLogs(): LogEntry[] {
        return [...this.logHistory];
    }
}

interface LogEntry {
    timestamp: number;
    level: 'info' | 'warn' | 'error' | 'debug';
    category: string;
    message: string;
    metadata?: any;
}

interface ErrorStats {
    count: number;
    lastOccurrence: number;
}

type LogLevel = 'none' | 'basic' | 'normal' | 'verbose';
```

---

## 5. Modular Plugin Architecture

### Current Problem:
- Monolithic automation classes
- Difficult to add new automation features
- Tight coupling between components

### Proposed Solution:
Plugin-based architecture for automation modules.

### Implementation:
```typescript
// src/core/automation-plugin.ts
export abstract class AutomationPlugin {
    protected logger: EnhancedLogger;
    protected methodInvoker: IL2CPPMethodInvoker;
    protected classDiscovery: ClassDiscoveryManager;
    
    constructor(
        logger: EnhancedLogger,
        methodInvoker: IL2CPPMethodInvoker,
        classDiscovery: ClassDiscoveryManager
    ) {
        this.logger = logger;
        this.methodInvoker = methodInvoker;
        this.classDiscovery = classDiscovery;
    }
    
    abstract getName(): string;
    abstract getRequiredClasses(): ClassRequirement[];
    abstract initialize(): Promise<boolean>;
    abstract start(): Promise<void>;
    abstract stop(): Promise<void>;
    abstract getStatus(): PluginStatus;
}

// src/plugins/goody-hut-plugin.ts
export class GoodyHutPlugin extends AutomationPlugin {
    private goodyHutClass: Il2Cpp.Class | null = null;
    private isRunning: boolean = false;
    
    getName(): string {
        return "GoodyHutAutomation";
    }
    
    getRequiredClasses(): ClassRequirement[] {
        return [
            {
                key: 'goodyHut',
                assembly: 'Assembly-CSharp',
                className: 'GoodyHutHelper',
                required: true
            }
        ];
    }
    
    async initialize(): Promise<boolean> {
        this.goodyHutClass = await this.classDiscovery.discoverClass(
            'Assembly-CSharp',
            'GoodyHutHelper'
        );
        
        return this.goodyHutClass !== null;
    }
    
    async start(): Promise<void> {
        if (!this.goodyHutClass) {
            throw new Error("Plugin not initialized");
        }
        
        this.isRunning = true;
        this.logger.logMethodCall('GoodyHutPlugin', 'start', 'success', 0);
        
        // Start automation logic
        await this.runAutomationLoop();
    }
    
    async stop(): Promise<void> {
        this.isRunning = false;
        this.logger.logMethodCall('GoodyHutPlugin', 'stop', 'success', 0);
    }
    
    getStatus(): PluginStatus {
        return {
            name: this.getName(),
            isRunning: this.isRunning,
            initialized: this.goodyHutClass !== null,
            lastActivity: Date.now()
        };
    }
    
    private async runAutomationLoop(): Promise<void> {
        // Plugin-specific automation logic
    }
}

interface PluginStatus {
    name: string;
    isRunning: boolean;
    initialized: boolean;
    lastActivity: number;
}
```

---

## Implementation Roadmap

### Phase 1: Core Utilities (Week 1-2)
- [ ] Implement `IL2CPPMethodInvoker`
- [ ] Create `ClassDiscoveryManager`
- [ ] Build `EnhancedLogger`
- [ ] Test core utilities

### Phase 2: Configuration System (Week 2-3)
- [ ] Implement `AutomationConfigManager`
- [ ] Consolidate type definitions
- [ ] Add runtime validation
- [ ] Migrate existing configurations

### Phase 3: Plugin Architecture (Week 3-4)
- [ ] Create plugin base classes
- [ ] Migrate GoodyHut to plugin
- [ ] Migrate EntityController to plugin
- [ ] Test plugin system

### Phase 4: Integration and Testing (Week 4)
- [ ] Integrate all components
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] Documentation updates

---

## Expected Benefits

### Code Quality:
- **50% reduction** in duplicate code
- **Consistent patterns** across all modules
- **Improved type safety** with runtime validation

### Maintainability:
- **Centralized utilities** for common operations
- **Plugin architecture** for easy feature addition
- **Comprehensive logging** for debugging

### Reliability:
- **Unified error handling** across all components
- **Better validation** of IL2CPP interactions
- **Improved anti-debug protection**

### Performance:
- **Cached class discovery** reduces lookup overhead
- **Optimized method invocation** patterns
- **Reduced memory usage** through better object management
