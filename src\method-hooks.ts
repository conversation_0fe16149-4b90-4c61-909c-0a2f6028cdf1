/**
 * Method Hooking System for GoodyHutHelper
 * TypeScript implementation of IL2CPP method hooks with frida-il2cpp-bridge
 */

import "frida-il2cpp-bridge";
import {
    GoodyHutHelperMethods,
    MethodAddresses,
    IL2CPPModuleInfo,
    CollectionResult,
    AntiDebugError,
    ErrorTracker
} from './interfaces/goody-hut-interfaces';
import { AntiDebugProtection } from './anti-debug-protection';

/**
 * Method hooking manager for GoodyHutHelper automation
 */
export class MethodHookManager {
    private methods: GoodyHutHelperMethods;
    private addresses: MethodAddresses;
    private moduleInfo: IL2CPPModuleInfo;
    private hooks: Map<string, InvocationListener> = new Map();
    private errorTrackers: Map<string, ErrorTracker> = new Map();
    private antiDebugProtection: AntiDebugProtection;

    // Callback functions for hook events
    private onCanCollectCallback?: (instance: NativePointer, canCollect: boolean) => void;
    private onFinishCollectCallback?: (instance: NativePointer, success: boolean) => void;
    private onUpdateCallback?: (instance: NativePointer) => void;

    // Real-time collection tracking
    private processedInstances: Set<string> = new Set();
    private realTimeCollectionEnabled: boolean = true;

    constructor(
        methods: GoodyHutHelperMethods,
        addresses: MethodAddresses,
        moduleInfo: IL2CPPModuleInfo
    ) {
        this.methods = methods;
        this.addresses = addresses;
        this.moduleInfo = moduleInfo;

        // Initialize anti-debugging protection
        this.antiDebugProtection = new AntiDebugProtection({
            maxRetries: 5,
            retryDelay: 1000,
            enableRetryBackoff: true,
            ignoreKnownErrors: true,
            logErrorDetails: true
        });

        console.log("🎣 MethodHookManager initialized with anti-debugging protection");
    }

    // ========================================================================
    // Hook Setup Methods
    // ========================================================================

    /**
     * Setup all method hooks for automation
     */
    public setupAllHooks(): void {
        console.log("🎯 Setting up all method hooks...");

        try {
            // Primary hooks for automation
            this.setupCanCollectHook();
            this.setupFinishCollectHook();
            this.setupUpdateHook();

            // Optional monitoring hooks
            this.setupRewardHooks();

            console.log("✅ All method hooks setup complete");

        } catch (error) {
            console.log(`❌ Failed to setup hooks: ${error}`);
        }
    }

    /**
     * Setup CanCollect method hook using IL2CPP bridge native hooking
     */
    private setupCanCollectHook(): void {
        try {
            console.log("🎣 Setting up CanCollect hook...");

            // Use IL2CPP bridge method (preferred approach)
            if (!this.methods.CanCollect) {
                console.log("⚠️ CanCollect method not available for hooking");
                return;
            }

            // Store original implementation
            const originalCanCollect = this.methods.CanCollect.implementation;
            const hookManager = this; // Capture reference for closure

            // Replace with our hook using IL2CPP bridge pattern
            this.methods.CanCollect.implementation = function() {
                try {
                    // Call original method
                    const result = originalCanCollect.call(this);
                    const canCollect = result ? true : false;

                    console.log(`[*] CanCollect() called on GoodyHutHelper: ${this.handle} -> ${canCollect}`);

                    if (canCollect) {
                        console.log("[+] Ruins can be collected! Auto-triggering...");

                        // Call the callback if registered
                        if (hookManager.onCanCollectCallback) {
                            hookManager.onCanCollectCallback(this.handle, canCollect);
                        }

                        // Auto-trigger collection
                        hookManager.triggerAutoCollection(this.handle);
                    }

                    return result;

                } catch (error) {
                    hookManager.handleHookError('CanCollect', error, this.handle);
                    return originalCanCollect.call(this); // Fallback to original
                }
            };

            console.log("✅ CanCollect hook setup successfully using IL2CPP bridge");

        } catch (error) {
            console.log(`❌ Failed to setup CanCollect hook: ${error}`);
        }
    }

    /**
     * Setup FinishCollect method hook using IL2CPP bridge native hooking
     */
    private setupFinishCollectHook(): void {
        try {
            console.log("🎣 Setting up FinishCollect hook...");

            // Use IL2CPP bridge method (preferred approach)
            if (!this.methods.FinishCollect) {
                console.log("⚠️ FinishCollect method not available for hooking");
                return;
            }

            // Store original implementation
            const originalFinishCollect = this.methods.FinishCollect.implementation;
            const hookManager = this; // Capture reference for closure

            // Replace with our hook using IL2CPP bridge pattern
            this.methods.FinishCollect.implementation = function() {
                try {
                    const startTime = Date.now();

                    // Call original method
                    const result = originalFinishCollect.call(this);

                    const processingTime = Date.now() - startTime;
                    console.log(`[+] FinishCollect() completed in ${processingTime}ms`);

                    // Call the callback if registered
                    if (hookManager.onFinishCollectCallback) {
                        hookManager.onFinishCollectCallback(this.handle, true);
                    }

                    // Update entity state for cleanup
                    hookManager.updateEntityStateForCleanup(this.handle);

                    return result;

                } catch (error) {
                    hookManager.handleHookError('FinishCollect', error, this.handle);
                    return originalFinishCollect.call(this); // Fallback to original
                }
            };

            console.log("✅ FinishCollect hook setup successfully using IL2CPP bridge");

        } catch (error) {
            console.log(`❌ Failed to setup FinishCollect hook: ${error}`);
        }
    }

    /**
     * Setup Update method hook using IL2CPP bridge native hooking
     */
    private setupUpdateHook(): void {
        try {
            console.log("🎣 Setting up Update hook for real-time discovery...");

            // Use IL2CPP bridge method (preferred approach)
            if (!this.methods.Update) {
                console.log("⚠️ Update method not available for hooking");
                return;
            }

            // Store original implementation
            const originalUpdate = this.methods.Update.implementation;
            const hookManager = this; // Capture reference for closure
            let updateCallCount = 0;

            // Replace with our hook using IL2CPP bridge pattern
            this.methods.Update.implementation = function() {
                try {
                    updateCallCount++;

                    // Only process every 15th call to reduce overhead
                    if (updateCallCount % 15 === 0) {
                        // Call the callback if registered
                        if (hookManager.onUpdateCallback) {
                            hookManager.onUpdateCallback(this.handle);
                        }

                        // Check if this instance can be collected
                        hookManager.checkInstanceForRealTimeCollection(this.handle);
                    }

                    // Call original method
                    return originalUpdate.call(this);

                } catch (error) {
                    hookManager.handleHookError('Update', error, this.handle);
                    return originalUpdate.call(this); // Fallback to original
                }
            };

            console.log("✅ Update hook setup successfully using IL2CPP bridge");

        } catch (error) {
            console.log(`❌ Failed to setup Update hook: ${error}`);
        }
    }

    /**
     * Setup reward monitoring hooks using IL2CPP bridge native hooking
     */
    private setupRewardHooks(): void {
        try {
            console.log("🎣 Setting up reward monitoring hooks...");

            // GetRewardType hook
            if (this.methods.GetRewardType) {
                const originalGetRewardType = this.methods.GetRewardType.implementation;

                this.methods.GetRewardType.implementation = function() {
                    const result = originalGetRewardType.call(this);
                    const rewardType = result ? result.valueOf() : 0;
                    console.log(`[*] Reward Type: ${rewardType}`);
                    return result;
                };
            }

            // GetRewardAmount hook
            if (this.methods.GetRewardAmount) {
                const originalGetRewardAmount = this.methods.GetRewardAmount.implementation;

                this.methods.GetRewardAmount.implementation = function() {
                    const result = originalGetRewardAmount.call(this);
                    const amount = result ? result.valueOf() : 0;
                    console.log(`[*] Reward Amount: ${amount}`);
                    return result;
                };
            }

            console.log("✅ Reward monitoring hooks setup successfully using IL2CPP bridge");

        } catch (error) {
            console.log(`❌ Failed to setup reward hooks: ${error}`);
        }
    }

    // ========================================================================
    // Hook Action Methods
    // ========================================================================

    /**
     * Trigger automatic collection for a GoodyHutHelper instance
     */
    private triggerAutoCollection(goodyHutPtr: NativePointer): void {
        try {
            console.log(`[+] Triggering auto-collection for instance: ${goodyHutPtr}`);

            // Get config and set cleanUp flag
            this.setCleanupFlag(goodyHutPtr);

            // Call FinishCollect
            this.callFinishCollect(goodyHutPtr);

        } catch (error) {
            this.handleHookError('triggerAutoCollection', error, goodyHutPtr);
        }
    }

    /**
     * Set cleanup flag on GoodyHutHelper config
     */
    private setCleanupFlag(goodyHutPtr: NativePointer): boolean {
        try {
            const configTarget = this.methods.Config?.handle || this.addresses.Config;

            if (!configTarget || configTarget.isNull()) {
                console.log("⚠️ Config method not available for cleanup flag");
                return false;
            }

            // Call Config() method
            const configFunc = new NativeFunction(configTarget, 'pointer', ['pointer']);
            const configPtr = configFunc(goodyHutPtr);

            if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                try {
                    const cleanUpOffset = 0x30;
                    configPtr.add(cleanUpOffset).writeU8(1);
                    console.log(`[+] Set cleanUp flag for instance: ${goodyHutPtr}`);
                    return true;
                } catch (e) {
                    console.log(`[-] Error setting cleanUp flag: ${e}`);
                    return false;
                }
            } else {
                console.log(`[-] Config() returned null/invalid pointer for: ${goodyHutPtr}`);
                return false;
            }

        } catch (error) {
            console.log(`[-] Error in setCleanupFlag: ${error}`);
            return false;
        }
    }

    /**
     * Call FinishCollect on a GoodyHutHelper instance
     */
    private callFinishCollect(goodyHutPtr: NativePointer): boolean {
        try {
            const finishCollectTarget = this.methods.FinishCollect?.handle || this.addresses.FinishCollect;

            if (!finishCollectTarget || finishCollectTarget.isNull()) {
                console.log("⚠️ FinishCollect method not available");
                return false;
            }

            const finishCollectFunc = new NativeFunction(finishCollectTarget, 'void', ['pointer']);
            finishCollectFunc(goodyHutPtr);
            console.log(`[+] FinishCollect() executed successfully on: ${goodyHutPtr}`);
            return true;

        } catch (error) {
            console.log(`[-] Error executing FinishCollect(): ${error}`);
            return false;
        }
    }

    /**
     * Check instance for real-time collection during Update hook
     */
    private checkInstanceForRealTimeCollection(goodyHutPtr: NativePointer): void {
        try {
            if (!this.realTimeCollectionEnabled) {
                return;
            }

            const instanceId = goodyHutPtr.toString();

            // Check if we've already processed this instance
            if (this.processedInstances.has(instanceId)) {
                return;
            }

            // Call CanCollect() to check if ready
            const canCollectTarget = this.methods.CanCollect?.handle || this.addresses.CanCollect;

            if (!canCollectTarget || canCollectTarget.isNull()) {
                return;
            }

            const canCollectFunc = new NativeFunction(canCollectTarget, 'int', ['pointer']);
            const result = canCollectFunc(goodyHutPtr);

            if (result === 1) {
                console.log(`[+] REAL-TIME: Found collectible ruins at: ${goodyHutPtr} - Processing immediately`);

                // Process immediately while pointer is fresh and valid
                const success = this.triggerRealTimeCollection(goodyHutPtr);

                if (success) {
                    // Mark as processed to prevent duplicate processing
                    this.processedInstances.add(instanceId);
                    console.log(`[+] REAL-TIME SUCCESS: Collected ruins at ${goodyHutPtr} (Total processed: ${this.processedInstances.size})`);
                } else {
                    console.log(`[-] REAL-TIME FAILED: Error processing ${instanceId}`);
                }
            }

        } catch (error) {
            // Silently ignore errors during real-time discovery to avoid spam
        }
    }

    /**
     * Trigger real-time collection with enhanced error handling
     */
    private triggerRealTimeCollection(goodyHutPtr: NativePointer): boolean {
        try {
            console.log(`[+] Triggering real-time collection for instance: ${goodyHutPtr}`);

            // Set cleanup flag first
            const cleanupSet = this.setCleanupFlag(goodyHutPtr);

            // Execute collection
            const collectionSuccess = this.callFinishCollect(goodyHutPtr);

            return cleanupSet && collectionSuccess;

        } catch (error) {
            this.handleHookError('triggerRealTimeCollection', error, goodyHutPtr);
            return false;
        }
    }

    /**
     * Update entity state for cleanup after collection
     */
    private updateEntityStateForCleanup(goodyHutPtr: NativePointer): void {
        try {
            console.log(`[*] Updating entity state for cleanup: ${goodyHutPtr}`);

            // Trigger automatic selling after a short delay
            setTimeout(() => {
                try {
                    console.log("[*] Attempting automatic ruin selling...");

                    const sellRuinsTarget = this.methods.SellRuins?.handle || this.addresses.SellRuins;

                    if (sellRuinsTarget && !sellRuinsTarget.isNull()) {
                        const sellRuinsFunc = new NativeFunction(sellRuinsTarget, 'void', ['pointer']);
                        sellRuinsFunc(goodyHutPtr);
                        console.log("[+] SellRuins() executed for automatic cleanup");
                    }

                } catch (e) {
                    console.log(`[-] Error in automatic ruin selling: ${e}`);
                }
            }, 1000); // 1 second delay

        } catch (error) {
            console.log(`[-] Error updating state for cleanup: ${error}`);
        }
    }

    // ========================================================================
    // Error Handling
    // ========================================================================

    /**
     * Handle errors that occur in hooks with anti-debugging protection
     */
    private handleHookError(methodName: string, error: any, instancePtr?: NativePointer): void {
        const instanceId = instancePtr ? instancePtr.toString() : 'unknown';
        const operationId = `${methodName}_${instanceId}`;

        // Use anti-debugging protection to classify and handle error
        const errorType = this.antiDebugProtection.classifyError(error);

        // Record the error attempt
        this.antiDebugProtection.recordRetryAttempt(operationId, errorType, false);

        // Create error tracker for internal tracking
        const errorTracker: ErrorTracker = {
            errorType,
            errorMessage: error instanceof Error ? error.message : String(error),
            occurredAt: Date.now(),
            instanceId,
            methodName,
            retryCount: this.antiDebugProtection.getRetryStats(operationId).retryCount
        };

        this.errorTrackers.set(`${methodName}_${instanceId}_${Date.now()}`, errorTracker);

        // Log based on error classification and protection settings
        if (this.antiDebugProtection.shouldIgnoreError(errorType)) {
            console.log(`⚠️ ${methodName}: Known anti-debug error (${errorType}) - continuing operation`);
        } else {
            console.log(`❌ ${methodName}: Error requiring attention - ${errorType}: ${errorTracker.errorMessage}`);
        }
    }

    /**
     * Execute method call with anti-debugging protection
     */
    private async executeWithProtection<T>(
        operationId: string,
        operation: () => T | Promise<T>,
        context?: string
    ): Promise<T | null> {
        return await this.antiDebugProtection.executeWithProtection(
            operationId,
            operation,
            context
        );
    }

    /**
     * Validate instance pointer with anti-debugging protection
     */
    private validateInstance(instancePtr: NativePointer, context?: string): boolean {
        return this.antiDebugProtection.validateInstancePointer(instancePtr, context);
    }

    /**
     * Get anti-debugging protection statistics
     */
    public getProtectionStats(): any {
        return this.antiDebugProtection.getProtectionStats();
    }

    // ========================================================================
    // Callback Registration
    // ========================================================================

    /**
     * Register callback for CanCollect hook events
     */
    public onCanCollect(callback: (instance: NativePointer, canCollect: boolean) => void): void {
        this.onCanCollectCallback = callback;
    }

    /**
     * Register callback for FinishCollect hook events
     */
    public onFinishCollect(callback: (instance: NativePointer, success: boolean) => void): void {
        this.onFinishCollectCallback = callback;
    }

    /**
     * Register callback for Update hook events
     */
    public onUpdate(callback: (instance: NativePointer) => void): void {
        this.onUpdateCallback = callback;
    }

    /**
     * Enable or disable real-time collection
     */
    public setRealTimeCollectionEnabled(enabled: boolean): void {
        this.realTimeCollectionEnabled = enabled;
        console.log(`[*] Real-time collection ${enabled ? 'ENABLED' : 'DISABLED'}`);
    }

    /**
     * Get real-time collection statistics
     */
    public getRealTimeStats(): { processed: number, enabled: boolean } {
        return {
            processed: this.processedInstances.size,
            enabled: this.realTimeCollectionEnabled
        };
    }

    /**
     * Clear processed instances tracking
     */
    public clearProcessedInstances(): void {
        this.processedInstances.clear();
        console.log("[+] Cleared real-time processed instances tracking");
    }

    // ========================================================================
    // Hook Management
    // ========================================================================

    /**
     * Remove all hooks and cleanup
     */
    public removeAllHooks(): void {
        console.log("🧹 Removing all method hooks...");

        this.hooks.forEach((hook, methodName) => {
            try {
                hook.detach();
                console.log(`✅ Removed ${methodName} hook`);
            } catch (error) {
                console.log(`⚠️ Error removing ${methodName} hook: ${error}`);
            }
        });

        this.hooks.clear();
        this.errorTrackers.clear();

        console.log("✅ All hooks removed and cleaned up");
    }

    /**
     * Get hook statistics
     */
    public getHookStats(): { activeHooks: number, errorCount: number, errors: ErrorTracker[] } {
        return {
            activeHooks: this.hooks.size,
            errorCount: this.errorTrackers.size,
            errors: Array.from(this.errorTrackers.values())
        };
    }
}