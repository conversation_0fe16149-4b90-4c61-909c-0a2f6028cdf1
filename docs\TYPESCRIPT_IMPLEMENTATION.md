# GoodyHutHelper TypeScript Implementation

## Overview

The GoodyHutHelper TypeScript implementation provides a robust, type-safe automation system for Dominations game collection mechanics using Frida IL2CPP bridge. This implementation converts the original 790-line JavaScript automation into a modular, maintainable TypeScript architecture.

## Architecture

### Core Components

```
src/
├── goody-hut-agent.ts          # Main entry point with Il2Cpp.perform()
├── goody-hut-helper.ts         # Core automation class
├── method-hooks.ts             # IL2CPP method hooking system
├── batch-processor.ts          # Two-phase batch processing
├── config-manager.ts           # Configuration management
├── state-manager.ts            # State and statistics tracking
├── anti-debug-protection.ts    # Error handling and retry logic
└── interfaces/
    └── goody-hut-interfaces.ts # TypeScript type definitions
```

### Key Features

- **Type Safety**: Comprehensive TypeScript interfaces for all data structures
- **Modular Design**: Separation of concerns with dedicated classes
- **Anti-debugging Protection**: Robust error handling with retry logic
- **Real-time Collection**: Immediate processing via Update() hook
- **Batch Processing**: Two-phase system (discovery → collection)
- **Configuration Management**: Centralized config with validation
- **State Management**: Comprehensive statistics and phase tracking

## Installation & Setup

### Prerequisites

```bash
npm install
```

### Build System

```bash
# Build the TypeScript agent
npm run build-goodyhut

# Watch for changes during development
npm run watch-goodyhut

# Run unit tests
npm test

# Run tests with coverage
npm test:coverage
```

### Deployment

```bash
# Spawn with the game
npm run spawn-goodyhut

# Or attach to running game
npm run attach-goodyhut
```

## Usage Examples

### Basic Initialization

```typescript
import { GoodyHutHelper } from './src/goody-hut-helper';

// Initialize with default configuration
const helper = new GoodyHutHelper();

// Initialize with custom configuration
const helper = new GoodyHutHelper({
    batch: {
        batchSize: 10,
        batchInterval: 5000,
        retryLimit: 5
    },
    logging: {
        debugLevel: 'verbose'
    }
});

// Initialize the automation system
const result = await helper.initialize();
if (result.success) {
    console.log('✅ GoodyHutHelper initialized successfully');
    helper.startAutomation();
} else {
    console.error('❌ Initialization failed:', result.error);
}
```

### Configuration Management

```typescript
// Get current configuration
const config = helper.getConfig();
console.log('Current batch size:', config.batch.batchSize);

// Update configuration at runtime
helper.updateBatchConfig({
    batchSize: 15,
    batchInterval: 3000
});

// Update logging configuration
helper.updateLoggingConfig({
    debugLevel: 'minimal',
    logBatchDetails: false
});
```

### Statistics and Monitoring

```typescript
// Get automation statistics
const stats = helper.getStats();
console.log(`Collections performed: ${stats.collectionsPerformed}`);
console.log(`Total instances: ${stats.totalInstances}`);
console.log(`Valid instances: ${stats.validInstances}`);

// Get detailed state summary
const summary = helper.getStateSummary();
console.log(summary);
```

### Manual Control via RPC

```typescript
// Available RPC functions (accessible from Frida console)
rpc.exports = {
    // Batch processing control
    getBatchStatus: () => helper.getBatchStatus(),
    forceBatchProcess: () => helper.forceBatchProcess(),
    resetToDiscovery: () => helper.resetToDiscovery(),
    
    // Configuration management
    getConfig: () => helper.getConfig(),
    updateBatchSize: (size: number) => helper.updateBatchConfig({ batchSize: size }),
    
    // Statistics and monitoring
    getStats: () => helper.getStats(),
    getStateSummary: () => helper.getStateSummary(),
    
    // Automation control
    startAutomation: () => helper.startAutomation(),
    stopAutomation: () => helper.stopAutomation(),
    
    // Cleanup and maintenance
    clearProcessedInstances: () => helper.clearProcessedInstances(),
    clearFailedInstances: () => helper.clearFailedInstances()
};
```

## Configuration Options

### Batch Configuration

```typescript
interface BatchConfig {
    batchSize: number;          // Number of instances to process per batch
    batchInterval: number;      // Time between batch processing cycles (ms)
    retryLimit: number;         // Maximum retry attempts for failed operations
    discoveryTimeout: number;   // Timeout for discovery phase (ms)
    maxWaitTime: number;        // Maximum wait time for operations (ms)
}
```

### Error Handling Configuration

```typescript
interface ErrorHandlingConfig {
    maxRetries: number;             // Maximum retry attempts
    retryDelay: number;             // Base delay between retries (ms)
    ignoreAntiDebugErrors: boolean; // Ignore known anti-debugging errors
    logErrorDetails: boolean;       // Log detailed error information
    continueOnError: boolean;       // Continue automation on errors
}
```

### Logging Configuration

```typescript
interface LoggingConfig {
    debugLevel: 'minimal' | 'normal' | 'verbose';
    logBatchDetails: boolean;   // Log batch processing details
    logErrorDetails: boolean;   // Log error details
}
```

## Method Signatures

### Core GoodyHutHelper Class

```typescript
class GoodyHutHelper {
    constructor(customConfig?: Partial<GoodyHutAutomationConfig>);
    
    // Initialization
    initialize(): Promise<InitializationResult>;
    cleanup(): void;
    
    // Automation control
    startAutomation(): boolean;
    stopAutomation(): void;
    
    // Configuration management
    getConfig(): GoodyHutAutomationConfig;
    updateBatchConfig(updates: Partial<BatchConfig>): void;
    updateLoggingConfig(updates: Partial<LoggingConfig>): void;
    
    // Statistics and monitoring
    getStats(): AutomationStats & BatchStats;
    getStateSummary(): string;
    
    // Manual operations
    forceBatchProcess(): Promise<BatchProcessingResult>;
    resetToDiscovery(): void;
    clearProcessedInstances(): void;
    clearFailedInstances(): void;
}
```

### Anti-debugging Protection

```typescript
class AntiDebugProtection {
    constructor(config?: Partial<AntiDebugConfig>);
    
    // Error classification and handling
    classifyError(error: any): AntiDebugError;
    shouldIgnoreError(errorType: AntiDebugError): boolean;
    
    // Retry logic
    shouldRetry(operationId: string, errorType: AntiDebugError): boolean;
    calculateRetryDelay(attemptNumber: number): number;
    
    // Protected execution
    executeWithProtection<T>(
        operationId: string,
        operation: () => T | Promise<T>,
        context?: string
    ): Promise<T | null>;
    
    // Instance validation
    validateInstancePointer(instancePtr: NativePointer, context?: string): boolean;
}
```

## Integration Patterns

### IL2CPP Method Invocation Best Practices

**Important**: When calling instance methods on IL2CPP objects, there are two correct patterns:

#### Pattern 1: Direct Method Call (Preferred)
```typescript
// Get method from instance and invoke with arguments
const method = instance.method("MethodName");
const result = method.invoke(...args);
```

#### Pattern 2: Cached Method with Instance Parameter
```typescript
// Use cached method reference with instance as first parameter
const cachedMethod = this.methods.MethodName;
const result = cachedMethod.invoke(instance, ...args);
```

**❌ Incorrect Pattern:**
```typescript
// This will fail with "cannot invoke non-static method as it must be invoked through a Il2Cpp.Object"
const method = this.methods.MethodName;
const result = method.invoke(instance, ...args); // Wrong - this treats it like a static method
```

The EntityController manager uses both patterns with fallback logic for maximum reliability.

### Custom Hook Integration

```typescript
// Extend the MethodHookManager for custom hooks
class CustomMethodHooks extends MethodHookManager {
    setupCustomHooks(): void {
        // Add custom method hooks
        this.setupCustomCanCollectHook();
        this.setupCustomFinishCollectHook();
    }
    
    private setupCustomCanCollectHook(): void {
        // Custom implementation
    }
}
```

### State Management Integration

```typescript
// Custom state tracking
const stateManager = new StateManager();

// Track custom events
stateManager.recordCollection();
stateManager.updateInstanceCounts(total, valid, invalid, upgradeable);

// Get comprehensive state
const summary = stateManager.getStateSummary();
```

### Configuration Validation

```typescript
// Custom configuration validation
const configManager = new ConfigManager();

try {
    configManager.validateConfig(customConfig);
    console.log('✅ Configuration is valid');
} catch (error) {
    console.error('❌ Configuration validation failed:', error.message);
}
```

## Error Handling

### Anti-debugging Error Types

```typescript
type AntiDebugError =
    | 'breakpoint_triggered'    // Debugger breakpoint hit
    | 'access_violation'        // Memory access violation
    | 'illegal_instruction'     // Illegal CPU instruction
    | 'abort_called'           // Process abort called
    | 'unknown_error';         // Unclassified error
```

### Error Recovery Strategies

1. **Automatic Retry**: Failed operations are automatically retried with exponential backoff
2. **Error Classification**: Errors are classified and handled appropriately
3. **Graceful Degradation**: Known anti-debugging errors are ignored to continue operation
4. **State Recovery**: Failed instances are tracked and can be retried later

## Performance Considerations

### Memory Management

- Instance tracking uses efficient Map and Set data structures
- Error history is limited to prevent memory bloat
- Cleanup methods are provided for resource management

### Processing Optimization

- Real-time collection for immediate processing
- Batch processing for efficient bulk operations
- Configurable batch sizes and intervals
- Instance validation to avoid processing destroyed objects

## Testing

### Unit Tests

```bash
# Run all tests
npm test

# Run specific test file
npm run test:goodyhut

# Run tests with coverage
npm test:coverage

# Watch mode for development
npm test:watch
```

### Test Coverage

The test suite covers:
- Configuration management and validation
- State management and statistics tracking
- Anti-debugging protection and error handling
- Method discovery and hook setup
- Batch processing workflows
- Integration scenarios

## Troubleshooting

### Common Issues

1. **Module Detection Failure**
   - Ensure the game is running and IL2CPP module is loaded
   - Check module name configuration matches target platform

2. **Method Discovery Errors**
   - Verify IL2CPP bridge compatibility with game version
   - Check RVA addresses are correct for current game build

3. **Hook Setup Failures**
   - Enable detailed logging to diagnose hook issues
   - Verify method signatures match game implementation

4. **Performance Issues**
   - Adjust batch size and interval for optimal performance
   - Enable minimal logging to reduce overhead

### Debug Mode

```typescript
// Enable verbose logging for debugging
const helper = new GoodyHutHelper({
    logging: {
        debugLevel: 'verbose',
        logBatchDetails: true,
        logErrorDetails: true
    }
});
```

## Migration from JavaScript

### Key Differences

1. **Type Safety**: All data structures are now strongly typed
2. **Modular Architecture**: Code is split into focused classes
3. **Enhanced Error Handling**: Comprehensive anti-debugging protection
4. **Configuration Management**: Centralized and validated configuration
5. **State Management**: Detailed statistics and phase tracking

### Migration Steps

1. Update import statements to use TypeScript modules
2. Replace global variables with class-based state management
3. Update configuration objects to match new interfaces
4. Replace manual error handling with anti-debugging protection
5. Update RPC exports to use new method signatures

## Contributing

### Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Build the project: `npm run build-goodyhut`
4. Run tests: `npm test`
5. Start development: `npm run watch-goodyhut`

### Code Style

- Follow TypeScript strict mode guidelines
- Use comprehensive type definitions
- Document all public methods with JSDoc
- Write unit tests for new functionality
- Follow the existing modular architecture

## API Reference

For detailed API documentation, see [API_REFERENCE.md](./API_REFERENCE.md).

## License

This project is part of the Dominations game automation suite and follows the same licensing terms as the parent project.
