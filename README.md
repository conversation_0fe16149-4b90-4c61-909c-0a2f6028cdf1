# Dominations Game Automation

A comprehensive Frida-based automation suite for the Dominations mobile game, featuring TypeScript implementation with IL2CPP bridge integration.

## 🚀 Features

### GoodyHutHelper TypeScript Implementation
- **Type-Safe Automation**: Complete TypeScript rewrite with comprehensive type definitions
- **Modular Architecture**: Clean separation of concerns with dedicated classes
- **Anti-debugging Protection**: Robust error handling with retry logic
- **Real-time Collection**: Immediate processing via Update() hook with duplicate prevention
- **Batch Processing**: Two-phase system (discovery → collection) with configurable batch sizes
- **Configuration Management**: Centralized config with validation and runtime updates
- **State Management**: Comprehensive statistics and phase tracking

### EntityController Auto-Upgrade
- **Universal Upgrade System**: Processes ALL upgradeable entities automatically
- **Smart Level Management**: Upgrades to max-1 level, leaving final upgrade for manual completion
- **Anti-debugging Protection**: Handles game protection mechanisms gracefully
- **Configurable Retry Logic**: Continues beyond 3 attempts until max level reached

## 📁 Project Structure

```
dominations/
├── src/                          # TypeScript source code
│   ├── goody-hut-agent.ts       # Main GoodyHutHelper entry point
│   ├── goody-hut-helper.ts      # Core automation class
│   ├── method-hooks.ts          # IL2CPP method hooking system
│   ├── batch-processor.ts       # Two-phase batch processing
│   ├── config-manager.ts        # Configuration management
│   ├── state-manager.ts         # State and statistics tracking
│   ├── anti-debug-protection.ts # Error handling and retry logic
│   ├── entitycontroller-hook.ts # EntityController automation
│   └── interfaces/
│       └── goody-hut-interfaces.ts # TypeScript type definitions
├── tests/                        # Unit tests
│   ├── goody-hut-helper.test.ts # Comprehensive test suite
│   └── setup.ts                 # Test configuration
├── docs/                         # Documentation
│   ├── TYPESCRIPT_IMPLEMENTATION.md # Implementation guide
│   └── API_REFERENCE.md         # API documentation
├── dist/                         # Compiled JavaScript output
└── legacy/                       # Original JavaScript implementations
```

## 🛠️ Installation & Setup

### Prerequisites

- Node.js (v16 or higher)
- Frida (latest version)
- Android device with Dominations installed
- USB debugging enabled

### Installation

```bash
# Clone the repository
git clone https://github.com/djfaizp/dominations.git
cd dominations

# Install dependencies
npm install

# Build the TypeScript agents
npm run build-goodyhut
npm run build-entitycontroller
```

## 🎮 Usage

### GoodyHutHelper Automation

```bash
# Build and deploy
npm run build-goodyhut

# Spawn with the game (recommended)
npm run spawn-goodyhut

# Or attach to running game
npm run attach-goodyhut
```

### EntityController Auto-Upgrade

```bash
# Build and deploy
npm run build-entitycontroller

# Attach to running game
frida -U -n com.nexonm.dominations.adk -l dist/entitycontroller-hook.js
```

### Development Mode

```bash
# Watch for changes and auto-rebuild
npm run watch-goodyhut

# Run unit tests
npm test

# Run tests with coverage
npm test:coverage
```

## 📊 Runtime Control

### Global Functions (Available in Frida console)

```javascript
// Get automation statistics
getGoodyHutStats()

// Get current configuration
getGoodyHutConfig()

// Manual control
startGoodyHutAutomation()
stopGoodyHutAutomation()

// Force batch processing
forceBatchProcess()

// Reset to discovery phase
resetToDiscovery()
```

### RPC Exports

```javascript
// Batch processing control
rpc.exports.getBatchStatus()
rpc.exports.forceBatchProcess()
rpc.exports.resetToDiscovery()

// Configuration management
rpc.exports.updateBatchSize(15)
rpc.exports.getConfig()

// Statistics and monitoring
rpc.exports.getStats()
rpc.exports.getStateSummary()
```

## ⚙️ Configuration

### GoodyHutHelper Configuration

```typescript
const config = {
    batch: {
        batchSize: 10,              // Instances per batch
        batchInterval: 5000,        // Time between batches (ms)
        retryLimit: 5,              // Max retry attempts
        discoveryTimeout: 10000,    // Discovery timeout (ms)
        maxWaitTime: 60000          // Max operation wait (ms)
    },
    collection: {
        realTimeEnabled: true,      // Enable real-time collection
        batchEnabled: true,         // Enable batch processing
        cleanupEnabled: true,       // Enable cleanup automation
        maxCollections: 1000        // Max collections per session
    },
    logging: {
        debugLevel: 'normal',       // 'minimal' | 'normal' | 'verbose'
        logBatchDetails: true,      // Log batch processing details
        logErrorDetails: true       // Log error information
    },
    errorHandling: {
        maxRetries: 5,              // Max retry attempts
        retryDelay: 1000,           // Base retry delay (ms)
        ignoreAntiDebugErrors: true, // Ignore known anti-debug errors
        continueOnError: true       // Continue on non-critical errors
    }
};
```

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
npm test

# Run specific test file
npm run test:goodyhut

# Watch mode for development
npm test:watch

# Generate coverage report
npm test:coverage
```

### Test Coverage

The test suite covers:
- ✅ Configuration management and validation
- ✅ State management and statistics tracking
- ✅ Anti-debugging protection and error handling
- ✅ Method discovery and hook setup
- ✅ Batch processing workflows
- ✅ Integration scenarios

## 📈 Performance

### Optimization Features

- **Real-time Collection**: Immediate processing for optimal efficiency
- **Batch Processing**: Bulk operations for high-volume scenarios
- **Instance Validation**: Prevents processing of destroyed objects
- **Memory Management**: Efficient data structures and cleanup
- **Configurable Intervals**: Adjustable timing for different devices

### Recommended Settings

- **High-end devices**: `batchSize: 20, batchInterval: 3000`
- **Mid-range devices**: `batchSize: 10, batchInterval: 5000`
- **Low-end devices**: `batchSize: 5, batchInterval: 10000`

## 🛡️ Anti-debugging Protection

### Error Handling

The system automatically handles common anti-debugging mechanisms:

- **Breakpoint Triggered**: Ignored and operation continues
- **Access Violations**: Classified and handled appropriately
- **Abort Called**: Graceful recovery with retry logic
- **Illegal Instructions**: Automatic fallback mechanisms

### Retry Logic

- **Exponential Backoff**: Increasing delays between retry attempts
- **Maximum Attempts**: Configurable retry limits
- **Error Classification**: Different handling for different error types
- **State Recovery**: Failed operations can be retried later

## 📚 Documentation

- [TypeScript Implementation Guide](docs/TYPESCRIPT_IMPLEMENTATION.md)
- [API Reference](docs/API_REFERENCE.md)
- [IL2CPP Documentation](il2cpp-documentation.md)
- [Planning Document](PLANNING.md)

## 🔧 Development

### Building from Source

```bash
# Install dependencies
npm install

# Build TypeScript
npm run build-goodyhut

# Run tests
npm test

# Start development
npm run watch-goodyhut
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 License

This project is for educational and research purposes. Please respect the game's terms of service and use responsibly.

## 🤝 Support

For issues, questions, or contributions:

1. Check existing issues in the repository
2. Create a new issue with detailed information
3. Include logs and configuration details
4. Specify your device and game version

## 🎯 Roadmap

- [ ] Library Research Automation
- [ ] Armory Research Automation  
- [ ] Enhanced EntityController Features
- [ ] Web Dashboard for Monitoring
- [ ] Multi-device Support
- [ ] Configuration Profiles
