/**
 * State Management for GoodyHutHelper Automation
 * Manages automation state, statistics, and phase tracking
 */

import {
    AutomationStats,
    PhaseTracker,
    BatchStats,
    InstanceTracker,
    ErrorTracker
} from './interfaces/goody-hut-interfaces';

/**
 * State Manager for GoodyHutHelper automation
 */
export class StateManager {
    private automationStats: AutomationStats;
    private phaseTracker: PhaseTracker;
    private batchStats: BatchStats;
    private discoveredInstances: Map<string, InstanceTracker> = new Map();
    private processedInstances: Set<string> = new Set();
    private failedInstances: Map<string, InstanceTracker> = new Map();
    private errorTrackers: Map<string, ErrorTracker> = new Map();

    constructor() {
        this.automationStats = this.createInitialStats();
        this.phaseTracker = this.createInitialPhaseTracker();
        this.batchStats = this.createInitialBatchStats();
    }

    /**
     * Create initial automation statistics
     */
    private createInitialStats(): AutomationStats {
        return {
            totalInstances: 0,
            validInstances: 0,
            invalidInstances: 0,
            upgradeableInstances: 0,
            collectionsPerformed: 0,
            startTime: Date.now(),
            lastUpdateTime: Date.now()
        };
    }

    /**
     * Create initial phase tracker
     */
    private createInitialPhaseTracker(): PhaseTracker {
        return {
            currentPhase: 1,
            phaseStartTime: Date.now(),
            phaseEndTime: 0,
            phaseDuration: 0,
            discoveryComplete: false
        };
    }

    /**
     * Create initial batch statistics
     */
    private createInitialBatchStats(): BatchStats {
        return {
            currentPhase: 1,
            batchNumber: 0,
            isProcessingBatch: false,
            discoveredInstances: 0,
            processedInstances: 0,
            failedInstances: 0,
            totalDiscoveryScans: 0,
            lastDiscoveryTime: Date.now(),
            discoveryCompleteTime: 0
        };
    }

    // ========================================================================
    // Statistics Management
    // ========================================================================

    /**
     * Get current automation statistics
     */
    public getAutomationStats(): AutomationStats {
        return { ...this.automationStats };
    }

    /**
     * Update collection statistics
     */
    public recordCollection(): void {
        this.automationStats.collectionsPerformed++;
        this.automationStats.lastUpdateTime = Date.now();
    }

    /**
     * Record discovery scan
     */
    public recordDiscoveryScan(): void {
        this.batchStats.totalDiscoveryScans++;
        this.batchStats.lastDiscoveryTime = Date.now();
        this.automationStats.lastUpdateTime = Date.now();
    }

    /**
     * Update instance counts
     */
    public updateInstanceCounts(total: number, valid: number, invalid: number, upgradeable: number): void {
        this.automationStats.totalInstances = total;
        this.automationStats.validInstances = valid;
        this.automationStats.invalidInstances = invalid;
        this.automationStats.upgradeableInstances = upgradeable;
        this.automationStats.lastUpdateTime = Date.now();
    }

    /**
     * Reset automation statistics
     */
    public resetAutomationStats(): void {
        this.automationStats = this.createInitialStats();
        console.log("[+] Automation statistics reset");
    }

    // ========================================================================
    // Phase Management
    // ========================================================================

    /**
     * Get current phase information
     */
    public getPhaseTracker(): PhaseTracker {
        return { ...this.phaseTracker };
    }

    /**
     * Start discovery phase
     */
    public startDiscoveryPhase(): void {
        this.phaseTracker.currentPhase = 1;
        this.phaseTracker.phaseStartTime = Date.now();
        this.phaseTracker.discoveryComplete = false;
        this.batchStats.currentPhase = 1;

        console.log("[+] Discovery phase started");
    }

    /**
     * Complete discovery phase and start collection
     */
    public completeDiscoveryPhase(): void {
        this.phaseTracker.currentPhase = 2;
        this.phaseTracker.phaseEndTime = Date.now();
        this.phaseTracker.phaseDuration = this.phaseTracker.phaseEndTime - this.phaseTracker.phaseStartTime;
        this.phaseTracker.discoveryComplete = true;

        this.batchStats.currentPhase = 2;
        this.batchStats.discoveryCompleteTime = this.phaseTracker.phaseEndTime;

        console.log(`[+] Discovery phase completed in ${(this.phaseTracker.phaseDuration / 1000).toFixed(1)}s`);
    }

    /**
     * Reset to discovery phase
     */
    public resetToDiscoveryPhase(): void {
        this.phaseTracker = this.createInitialPhaseTracker();
        this.batchStats = this.createInitialBatchStats();
        this.clearAllInstances();

        console.log("[+] Reset to discovery phase");
    }

    // ========================================================================
    // Batch Statistics Management
    // ========================================================================

    /**
     * Get current batch statistics
     */
    public getBatchStats(): BatchStats {
        // Update current counts
        this.batchStats.discoveredInstances = this.discoveredInstances.size;
        this.batchStats.processedInstances = this.processedInstances.size;
        this.batchStats.failedInstances = this.failedInstances.size;

        return { ...this.batchStats };
    }

    /**
     * Start batch processing
     */
    public startBatchProcessing(batchNumber: number): void {
        this.batchStats.batchNumber = batchNumber;
        this.batchStats.isProcessingBatch = true;

        console.log(`[*] Started batch processing #${batchNumber}`);
    }

    /**
     * Complete batch processing
     */
    public completeBatchProcessing(): void {
        this.batchStats.isProcessingBatch = false;
        console.log(`[+] Completed batch processing #${this.batchStats.batchNumber}`);
    }

    // ========================================================================
    // Instance Management
    // ========================================================================

    /**
     * Add discovered instance
     */
    public addDiscoveredInstance(instanceId: string, instancePtr: NativePointer): void {
        if (!this.discoveredInstances.has(instanceId) && !this.processedInstances.has(instanceId)) {
            const tracker: InstanceTracker = {
                ptr: instancePtr,
                discoveryTime: Date.now(),
                retryCount: 0,
                entityId: instanceId
            };

            this.discoveredInstances.set(instanceId, tracker);
        }
    }

    /**
     * Mark instance as processed
     */
    public markInstanceProcessed(instanceId: string): void {
        this.processedInstances.add(instanceId);
        this.discoveredInstances.delete(instanceId);
        this.failedInstances.delete(instanceId);
    }

    /**
     * Mark instance as failed
     */
    public markInstanceFailed(instanceId: string, instancePtr: NativePointer, error?: string): void {
        const existingTracker = this.discoveredInstances.get(instanceId) || this.failedInstances.get(instanceId);
        const retryCount = existingTracker ? existingTracker.retryCount + 1 : 1;

        const failedTracker: InstanceTracker = {
            ptr: instancePtr,
            discoveryTime: existingTracker?.discoveryTime || Date.now(),
            retryCount,
            entityId: instanceId,
            lastFailTime: Date.now()
        };

        this.failedInstances.set(instanceId, failedTracker);
        this.discoveredInstances.delete(instanceId);
    }

    /**
     * Get discovered instances
     */
    public getDiscoveredInstances(): Map<string, InstanceTracker> {
        return new Map(this.discoveredInstances);
    }

    /**
     * Get processed instances
     */
    public getProcessedInstances(): Set<string> {
        return new Set(this.processedInstances);
    }

    /**
     * Get failed instances
     */
    public getFailedInstances(): Map<string, InstanceTracker> {
        return new Map(this.failedInstances);
    }

    /**
     * Clear all instances
     */
    public clearAllInstances(): void {
        this.discoveredInstances.clear();
        this.processedInstances.clear();
        this.failedInstances.clear();

        console.log("[+] All instance tracking cleared");
    }

    /**
     * Clear processed instances
     */
    public clearProcessedInstances(): void {
        this.processedInstances.clear();
        console.log("[+] Processed instances cleared");
    }

    /**
     * Clear failed instances
     */
    public clearFailedInstances(): void {
        this.failedInstances.clear();
        console.log("[+] Failed instances cleared");
    }

    // ========================================================================
    // Error Tracking
    // ========================================================================

    /**
     * Record error
     */
    public recordError(context: string, error: any, instancePtr?: NativePointer): void {
        const errorId = `${context}_${Date.now()}`;
        const errorTracker: ErrorTracker = {
            errorType: 'unknown' as any, // Will need to be properly typed
            errorMessage: error instanceof Error ? error.message : String(error),
            occurredAt: Date.now(),
            instanceId: instancePtr?.toString(),
            methodName: context,
            retryCount: 1
        };

        this.errorTrackers.set(errorId, errorTracker);

        // Keep only the last 100 errors to prevent memory bloat
        if (this.errorTrackers.size > 100) {
            const oldestKey = Array.from(this.errorTrackers.keys())[0];
            this.errorTrackers.delete(oldestKey);
        }
    }

    /**
     * Get error statistics
     */
    public getErrorStats(): { totalErrors: number; recentErrors: ErrorTracker[] } {
        const recentErrors = Array.from(this.errorTrackers.values())
            .filter(error => Date.now() - error.occurredAt < 300000) // Last 5 minutes
            .sort((a, b) => b.occurredAt - a.occurredAt);

        return {
            totalErrors: this.errorTrackers.size,
            recentErrors
        };
    }

    /**
     * Clear error tracking
     */
    public clearErrorTracking(): void {
        this.errorTrackers.clear();
        console.log("[+] Error tracking cleared");
    }

    // ========================================================================
    // State Summary
    // ========================================================================

    /**
     * Get comprehensive state summary
     */
    public getStateSummary(): string {
        const stats = this.getAutomationStats();
        const phase = this.getPhaseTracker();
        const batch = this.getBatchStats();
        const errors = this.getErrorStats();

        const uptime = Date.now() - stats.startTime;
        const uptimeStr = `${Math.floor(uptime / 60000)}m ${Math.floor((uptime % 60000) / 1000)}s`;

        return [
            "=== AUTOMATION STATE SUMMARY ===",
            `Uptime: ${uptimeStr}`,
            `Current Phase: ${phase.currentPhase} (${phase.currentPhase === 1 ? 'Discovery' : 'Collection'})`,
            `Collections Performed: ${stats.collectionsPerformed}`,
            `Total Instances: ${stats.totalInstances}`,
            `Valid Instances: ${stats.validInstances}`,
            `Invalid Instances: ${stats.invalidInstances}`,
            "",
            `Instance Counts:`,
            `  - Discovered: ${this.discoveredInstances.size}`,
            `  - Processed: ${this.processedInstances.size}`,
            `  - Failed: ${this.failedInstances.size}`,
            "",
            `Batch Processing:`,
            `  - Current Batch: #${batch.batchNumber}`,
            `  - Processing: ${batch.isProcessingBatch ? 'YES' : 'NO'}`,
            "",
            `Errors:`,
            `  - Total: ${errors.totalErrors}`,
            `  - Recent (5min): ${errors.recentErrors.length}`,
            "================================="
        ].join('\n');
    }
}