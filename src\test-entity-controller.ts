/**
 * Test script for EntityController integration
 * Verifies that method invocation works correctly
 */

import "frida-il2cpp-bridge";
import { EntityControllerManager } from './entity-controller-manager';

Il2Cpp.perform(async () => {
    console.log("🧪 Testing EntityController integration...");
    
    try {
        // Create EntityController manager with verbose logging
        const entityManager = new EntityControllerManager({
            maxRetries: 3,
            retryDelay: 500,
            upgradeTimeout: 5000,
            batchSize: 5,
            batchDelay: 1000,
            enableLogging: true,
            logLevel: 'verbose'
        });
        
        // Initialize
        console.log("🔄 Initializing EntityController manager...");
        const initialized = await entityManager.initialize();
        
        if (!initialized) {
            console.log("❌ EntityController initialization failed");
            return;
        }
        
        console.log("✅ EntityController manager initialized successfully");
        
        // Get instances
        console.log("🔍 Getting EntityController instances...");
        const instances = entityManager.getAllInstances();
        
        if (instances.length === 0) {
            console.log("⚠️ No EntityController instances found");
            return;
        }
        
        console.log(`✅ Found ${instances.length} EntityController instances`);
        
        // Test method invocation on first few instances
        const testInstances = instances.slice(0, Math.min(3, instances.length));
        
        for (let i = 0; i < testInstances.length; i++) {
            const instance = testInstances[i];
            console.log(`\n🧪 Testing instance ${i + 1}/${testInstances.length}:`);
            
            try {
                // Test basic method calls
                console.log("  📋 Testing IsSelected...");
                const isSelected = await testMethodCall(instance, 'IsSelected');
                console.log(`  ✅ IsSelected result: ${isSelected}`);
                
                if (isSelected) {
                    console.log("  📋 Testing GetLevel...");
                    const level = await testMethodCall(instance, 'GetLevel');
                    console.log(`  ✅ GetLevel result: ${level}`);
                    
                    console.log("  📋 Testing GetMaxUpgradeLevel...");
                    const maxLevel = await testMethodCall(instance, 'GetMaxUpgradeLevel');
                    console.log(`  ✅ GetMaxUpgradeLevel result: ${maxLevel}`);
                    
                    console.log("  📋 Testing CanUpgrade...");
                    const canUpgrade = await testMethodCall(instance, 'CanUpgrade', [true]);
                    console.log(`  ✅ CanUpgrade result: ${canUpgrade}`);
                    
                    console.log("  📋 Testing IsUpgrading...");
                    const isUpgrading = await testMethodCall(instance, 'IsUpgrading');
                    console.log(`  ✅ IsUpgrading result: ${isUpgrading}`);
                }
                
            } catch (error) {
                console.log(`  ❌ Error testing instance ${i + 1}: ${error}`);
            }
        }
        
        // Test auto-upgrade if we have selected entities
        console.log("\n🚀 Testing auto-upgrade functionality...");
        const upgradeResult = await entityManager.autoUpgradeSelected();
        console.log(`✅ Auto-upgrade completed: ${upgradeResult} upgrades performed`);
        
        // Show final stats
        const stats = entityManager.getStats();
        console.log("\n📊 Final Statistics:");
        console.log(`  Total Instances: ${stats.totalInstances}`);
        console.log(`  Selected Instances: ${stats.selectedInstances}`);
        console.log(`  Upgradeable Instances: ${stats.upgradeableInstances}`);
        console.log(`  Upgrades Performed: ${stats.upgradesPerformed}`);
        
        console.log("\n✅ EntityController integration test completed successfully!");
        
    } catch (error) {
        console.log(`❌ EntityController test failed: ${error}`);
    }
});

/**
 * Helper function to test method calls with proper error handling
 */
async function testMethodCall(instance: Il2Cpp.Object, methodName: string, args: any[] = []): Promise<any> {
    try {
        // Validate instance
        if (!instance || !instance.handle || instance.handle.isNull()) {
            throw new Error("Invalid instance");
        }
        
        // Try direct method call
        const method = instance.method(methodName);
        if (!method) {
            throw new Error(`Method ${methodName} not found`);
        }
        
        const result = method.invoke(...args);
        return result;
        
    } catch (error) {
        console.log(`    ⚠️ Method ${methodName} failed: ${error}`);
        return null;
    }
}

// Make test functions available globally
(globalThis as any).testEntityController = () => {
    console.log("🧪 Running EntityController test...");
    // The test will run automatically when the script loads
};

(globalThis as any).testMethodCall = testMethodCall;
