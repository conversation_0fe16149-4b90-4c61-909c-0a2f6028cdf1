📦
127448 /src/unified-automation-agent.js
✄
var u=function(t,e,n,s){var r=arguments.length,o=r<3?e:s===null?s=Object.getOwnPropertyDescriptor(e,n):s,i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(t,e,n,s);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(r<3?i(o):r>3?i(e,n,o):i(e,n))||o);return r>3&&o&&Object.defineProperty(e,n,o),o},h;(function(t){t.application={get dataPath(){return e("get_persistentDataPath")},get identifier(){return e("get_identifier")??e("get_bundleIdentifier")??Process.mainModule.name},get version(){return e("get_version")??V(t.module).toString(16)}},T(t,"unityVersion",()=>{try{let s=t.$config.unityVersion??e("get_unityVersion");if(s!=null)return s}catch{}let n="69 6c 32 63 70 70";for(let s of t.module.enumerateRanges("r--").concat(Process.getRangeByAddress(t.module.base)))for(let{address:r}of Memory.scanSync(s.base,s.size,n)){for(;r.readU8()!=0;)r=r.sub(1);let o=x.find(r.add(1).readCString());if(o!=null)return o}m("couldn't determine the Unity version, please specify it manually")},c),T(t,"unityVersionIsBelow201830",()=>x.lt(t.unityVersion,"2018.3.0"),c),T(t,"unityVersionIsBelow202120",()=>x.lt(t.unityVersion,"2021.2.0"),c);function e(n){let s=t.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+n)),r=new NativeFunction(s,"pointer",[]);return r.isNull()?null:new t.String(r()).asNullable()?.content??null}})(h||(h={}));var h;(function(t){function e(n,s){let r={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},o=typeof n=="boolean"?"System.Boolean":typeof n=="number"?r[s??"int32"]:n instanceof Int64?"System.Int64":n instanceof UInt64?"System.UInt64":n instanceof NativePointer?r[s??"intptr"]:m(`Cannot create boxed primitive using value of type '${typeof n}'`),i=t.corlib.class(o??m(`Unknown primitive type name '${s}'`)).alloc();return(i.tryField("m_value")??i.tryField("_pointer")??m(`Could not find primitive field in class '${o}'`)).value=n,i}t.boxed=e})(h||(h={}));var h;(function(t){t.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(h||(h={}));var h;(function(t){function e(i,a){i=i??`${t.application.identifier}_${t.application.version}.cs`,a=a??t.application.dataPath??Process.getCurrentDir(),r(a);let l=`${a}/${i}`,d=new File(l,"w");for(let g of t.domain.assemblies){P(`dumping ${g.name}...`);for(let f of g.image.classes)d.write(`${f}

`)}d.flush(),d.close(),B(`dump saved to ${l}`),o()}t.dump=e;function n(i,a=!1){i=i??`${t.application.dataPath??Process.getCurrentDir()}/${t.application.identifier}_${t.application.version}`,!a&&s(i)&&m(`directory ${i} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let l of t.domain.assemblies){P(`dumping ${l.name}...`);let d=`${i}/${l.name.replaceAll(".","/")}.cs`;r(d.substring(0,d.lastIndexOf("/")));let g=new File(d,"w");for(let f of l.image.classes)g.write(`${f}

`);g.flush(),g.close()}B(`dump saved to ${i}`),o()}t.dumpTree=n;function s(i){return t.corlib.class("System.IO.Directory").method("Exists").invoke(t.string(i))}function r(i){t.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(t.string(i))}function o(){k("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(h||(h={}));var h;(function(t){function e(n="current"){let s=t.exports.threadGetCurrent();return Interceptor.attach(t.module.getExportByName("__cxa_throw"),function(r){n=="current"&&!t.exports.threadGetCurrent().equals(s)||P(new t.Object(r[0].readPointer()))})}t.installExceptionListener=e})(h||(h={}));var h;(function(t){t.exports={get alloc(){return e("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return e("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return e("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return e("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return e("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return e("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return e("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return e("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return e("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return e("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return e("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return e("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return e("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return e("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return e("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return e("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return e("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return e("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return e("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return e("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return e("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return e("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return e("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return e("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return e("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return e("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return e("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return e("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return e("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return e("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return e("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return e("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return e("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return e("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return e("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return e("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return e("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return e("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return e("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return e("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return e("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return e("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return e("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return e("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return e("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return e("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return e("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return e("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return e("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return e("il2cpp_free","void",["pointer"])},get gcCollect(){return e("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return e("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return e("il2cpp_gc_disable","void",[])},get gcEnable(){return e("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return e("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return e("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return e("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return e("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return e("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return e("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return e("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return e("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return e("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return e("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return e("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return e("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return e("il2cpp_stop_gc_world","void",[])},get getCorlib(){return e("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return e("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return e("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return e("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return e("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return e("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return e("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return e("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return e("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return e("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return e("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return e("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return e("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return e("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return e("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return e("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return e("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return e("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return e("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return e("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return e("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return e("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return e("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return e("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return e("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return e("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return e("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return e("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return e("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return e("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return e("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return e("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return e("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return e("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return e("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return e("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return e("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return e("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return e("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return e("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return e("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return e("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return e("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return e("il2cpp_string_length","int32",["pointer"])},get stringNew(){return e("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return e("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return e("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return e("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return e("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return e("il2cpp_thread_current","pointer",[])},get threadIsVm(){return e("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return e("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return e("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return e("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return e("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return e("il2cpp_type_get_type","int",["pointer"])}},j(t.exports,c),T(t,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),c);function e(n,s,r){let o=t.$config.exports?.[n]?.()??t.module.findExportByName(n)??t.memorySnapshotExports[n],i=new NativeFunction(o??NULL,s,r);return i.isNull()?new Proxy(i,{get(a,l){let d=a[l];return typeof d=="function"?d.bind(a):d},apply(){o==null?m(`couldn't resolve export ${n}`):o.isNull()&&m(`export ${n} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):i}})(h||(h={}));var h;(function(t){function e(s){return r=>r instanceof t.Class?s.isAssignableFrom(r):s.isAssignableFrom(r.class)}t.is=e;function n(s){return r=>r instanceof t.Class?r.equals(s):r.class.equals(s)}t.isExactly=n})(h||(h={}));var h;(function(t){t.gc={get heapSize(){return t.exports.gcGetHeapSize()},get isEnabled(){return!t.exports.gcIsDisabled()},get isIncremental(){return!!t.exports.gcIsIncremental()},get maxTimeSlice(){return t.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return t.exports.gcGetUsedSize()},set isEnabled(e){e?t.exports.gcEnable():t.exports.gcDisable()},set maxTimeSlice(e){t.exports.gcSetMaxTimeSlice(e)},choose(e){let n=[],s=(o,i)=>{for(let a=0;a<i;a++)n.push(new t.Object(o.add(a*Process.pointerSize).readPointer()))},r=new NativeCallback(s,"void",["pointer","int","pointer"]);if(t.unityVersionIsBelow202120){let o=new NativeCallback(()=>{},"void",[]),i=t.exports.livenessCalculationBegin(e,0,r,NULL,o,o);t.exports.livenessCalculationFromStatics(i),t.exports.livenessCalculationEnd(i)}else{let o=(l,d)=>!l.isNull()&&d.compare(0)==0?(t.free(l),NULL):t.alloc(d),i=new NativeCallback(o,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let a=t.exports.livenessAllocateStruct(e,0,r,NULL,i);t.exports.livenessCalculationFromStatics(a),t.exports.livenessFinalize(a),this.startWorld(),t.exports.livenessFreeStruct(a)}return n},collect(e){t.exports.gcCollect(e<0?0:e>2?2:e)},collectALittle(){t.exports.gcCollectALittle()},startWorld(){return t.exports.gcStartWorld()},startIncrementalCollection(){return t.exports.gcStartIncrementalCollection()},stopWorld(){return t.exports.gcStopWorld()}}})(h||(h={}));var F;(function(t){T(t,"apiLevel",()=>{let n=e("ro.build.version.sdk");return n?parseInt(n):null},c);function e(n){let s=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(s){let r=new NativeFunction(s,"void",["pointer","pointer"]),o=Memory.alloc(92).writePointer(NULL);return r(Memory.allocUtf8String(n),o),o.readCString()??void 0}}})(F||(F={}));function m(t){let e=new Error(t);throw e.name="Il2CppError",e.stack=e.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),e}function k(t){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${t}`)}function B(t){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${t}`)}function P(t){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${t}`)}function j(t,e,n=Object.getOwnPropertyDescriptors(t)){for(let s in n)n[s]=e(t,s,n[s]);return Object.defineProperties(t,n),t}function T(t,e,n,s){globalThis.Object.defineProperty(t,e,s?.(t,e,{get:n,configurable:!0})??{get:n,configurable:!0})}function H(t){let e=3735928559,n=1103547991;for(let s=0,r;s<t.length;s++)r=t.charCodeAt(s),e=Math.imul(e^r,2654435761),n=Math.imul(n^r,1597334677);return e=Math.imul(e^e>>>16,2246822507),e^=Math.imul(n^n>>>13,3266489909),n=Math.imul(n^n>>>16,2246822507),n^=Math.imul(e^e>>>13,3266489909),4294967296*(2097151&n)+(e>>>0)}function V(t){return H(t.enumerateExports().sort((e,n)=>e.name.localeCompare(n.name)).map(e=>e.name+e.address.sub(t.base)).join(""))}function c(t,e,n){let s=n.get;if(!s)throw new Error("@lazy can only be applied to getter accessors");return n.get=function(){let r=s.call(this);return Object.defineProperty(this,e,{value:r,configurable:n.configurable,enumerable:n.enumerable,writable:!1}),r},n}var b=class{handle;constructor(e){e instanceof NativePointer?this.handle=e:this.handle=e.handle}equals(e){return this.handle.equals(e.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function W(t){return Object.keys(t).reduce((e,n)=>(e[e[n]]=n,e),t)}NativePointer.prototype.offsetOf=function(t,e){e??=512;for(let n=0;e>0?n<e:n<-e;n++)if(t(e>0?this.add(n):this.sub(n)))return n;return null};function $(t){let e=[],n=Memory.alloc(Process.pointerSize),s=t(n);for(;!s.isNull();)e.push(s),s=t(n);return e}function z(t){let e=Memory.alloc(Process.pointerSize),n=t(e);if(n.isNull())return[];let s=new Array(e.readInt());for(let r=0;r<s.length;r++)s[r]=n.add(r*Process.pointerSize).readPointer();return s}function A(t){return new Proxy(t,{cache:new Map,construct(e,n){let s=n[0].toUInt32();return this.cache.has(s)||this.cache.set(s,new e(n[0])),this.cache.get(s)}})}var x;(function(t){let e=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function n(i){return i?.match(e)?.[0]}t.find=n;function s(i,a){return o(i,a)>=0}t.gte=s;function r(i,a){return o(i,a)<0}t.lt=r;function o(i,a){let l=i.match(e),d=a.match(e);for(let g=1;g<=3;g++){let f=Number(l?.[g]??-1),p=Number(d?.[g]??-1);if(f>p)return 1;if(f<p)return-1}return 0}})(x||(x={}));var h;(function(t){function e(a=Process.pointerSize){return t.exports.alloc(a)}t.alloc=e;function n(a){return t.exports.free(a)}t.free=n;function s(a,l){switch(l.enumValue){case t.Type.Enum.BOOLEAN:return!!a.readS8();case t.Type.Enum.BYTE:return a.readS8();case t.Type.Enum.UBYTE:return a.readU8();case t.Type.Enum.SHORT:return a.readS16();case t.Type.Enum.USHORT:return a.readU16();case t.Type.Enum.INT:return a.readS32();case t.Type.Enum.UINT:return a.readU32();case t.Type.Enum.CHAR:return a.readU16();case t.Type.Enum.LONG:return a.readS64();case t.Type.Enum.ULONG:return a.readU64();case t.Type.Enum.FLOAT:return a.readFloat();case t.Type.Enum.DOUBLE:return a.readDouble();case t.Type.Enum.NINT:case t.Type.Enum.NUINT:return a.readPointer();case t.Type.Enum.POINTER:return new t.Pointer(a.readPointer(),l.class.baseType);case t.Type.Enum.VALUE_TYPE:return new t.ValueType(a,l);case t.Type.Enum.OBJECT:case t.Type.Enum.CLASS:return new t.Object(a.readPointer());case t.Type.Enum.GENERIC_INSTANCE:return l.class.isValueType?new t.ValueType(a,l):new t.Object(a.readPointer());case t.Type.Enum.STRING:return new t.String(a.readPointer());case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return new t.Array(a.readPointer())}m(`couldn't read the value from ${a} using an unhandled or unknown type ${l.name} (${l.enumValue}), please file an issue`)}t.read=s;function r(a,l,d){switch(d.enumValue){case t.Type.Enum.BOOLEAN:return a.writeS8(+l);case t.Type.Enum.BYTE:return a.writeS8(l);case t.Type.Enum.UBYTE:return a.writeU8(l);case t.Type.Enum.SHORT:return a.writeS16(l);case t.Type.Enum.USHORT:return a.writeU16(l);case t.Type.Enum.INT:return a.writeS32(l);case t.Type.Enum.UINT:return a.writeU32(l);case t.Type.Enum.CHAR:return a.writeU16(l);case t.Type.Enum.LONG:return a.writeS64(l);case t.Type.Enum.ULONG:return a.writeU64(l);case t.Type.Enum.FLOAT:return a.writeFloat(l);case t.Type.Enum.DOUBLE:return a.writeDouble(l);case t.Type.Enum.NINT:case t.Type.Enum.NUINT:case t.Type.Enum.POINTER:case t.Type.Enum.STRING:case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return a.writePointer(l);case t.Type.Enum.VALUE_TYPE:return Memory.copy(a,l,d.class.valueTypeSize),a;case t.Type.Enum.OBJECT:case t.Type.Enum.CLASS:case t.Type.Enum.GENERIC_INSTANCE:return l instanceof t.ValueType?(Memory.copy(a,l,d.class.valueTypeSize),a):a.writePointer(l)}m(`couldn't write value ${l} to ${a} using an unhandled or unknown type ${d.name} (${d.enumValue}), please file an issue`)}t.write=r;function o(a,l){if(globalThis.Array.isArray(a)){let d=Memory.alloc(l.class.valueTypeSize),g=l.class.fields.filter(f=>!f.isStatic);for(let f=0;f<g.length;f++){let p=o(a[f],g[f].type);r(d.add(g[f].offset).sub(t.Object.headerSize),p,g[f].type)}return new t.ValueType(d,l)}else if(a instanceof NativePointer){if(l.isByReference)return new t.Reference(a,l);switch(l.enumValue){case t.Type.Enum.POINTER:return new t.Pointer(a,l.class.baseType);case t.Type.Enum.STRING:return new t.String(a);case t.Type.Enum.CLASS:case t.Type.Enum.GENERIC_INSTANCE:case t.Type.Enum.OBJECT:return new t.Object(a);case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return new t.Array(a);default:return a}}else return l.enumValue==t.Type.Enum.BOOLEAN?!!a:l.enumValue==t.Type.Enum.VALUE_TYPE&&l.class.isEnum?o([a],l):a}t.fromFridaValue=o;function i(a){if(typeof a=="boolean")return+a;if(a instanceof t.ValueType){if(a.type.class.isEnum)return a.field("value__").value;{let l=a.type.class.fields.filter(d=>!d.isStatic).map(d=>i(d.bind(a).value));return l.length==0?[0]:l}}else return a}t.toFridaValue=i})(h||(h={}));var h;(function(t){T(t,"module",()=>n()??m("Could not find IL2CPP module"));async function e(r=!1){let o=n()??await new Promise(i=>{let[a,l]=s(),d=setTimeout(()=>{k(`after 10 seconds, IL2CPP module '${a}' has not been loaded yet, is the app running?`)},1e4),g=Process.attachModuleObserver({onAdded(f){(f.name==a||l&&f.name==l)&&(clearTimeout(d),setImmediate(()=>{i(f),g.detach()}))}})});return Reflect.defineProperty(t,"module",{value:o}),t.exports.getCorlib().isNull()?await new Promise(i=>{let a=Interceptor.attach(t.exports.initialize,{onLeave(){a.detach(),r?i(!0):setImmediate(()=>i(!1))}})}):!1}t.initialize=e;function n(){let[r,o]=s();return Process.findModuleByName(r)??Process.findModuleByName(o??r)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function s(){if(t.$config.moduleName)return[t.$config.moduleName];switch(Process.platform){case"linux":return[F.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}m(`${Process.platform} is not supported yet`)}})(h||(h={}));var h;(function(t){async function e(n,s="bind"){let r=null;try{let o=await t.initialize(s=="main");if(s=="main"&&!o)return e(()=>t.mainThread.schedule(n),"free");t.currentThread==null&&(r=t.domain.attach()),s=="bind"&&r!=null&&Script.bindWeak(globalThis,()=>r?.detach());let i=n();return i instanceof Promise?await i:i}catch(o){return Script.nextTick(i=>{throw i},o),Promise.reject(o)}finally{s=="free"&&r!=null&&r.detach()}}t.perform=e})(h||(h={}));var h;(function(t){class e{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let o=`
${this.#e.buffer.join(`
`)}
`;if(this.#h)P(o);else{let i=H(o);this.#e.history.has(i)||(this.#e.history.add(i),P(o))}this.#e.buffer.length=0}}};#u=t.mainThread.id;#h=!1;#d;#l=[];#c;#t;#s;#n;#r;#o;#i;#a;constructor(o){this.#d=o}thread(o){return this.#u=o.id,this}verbose(o){return this.#h=o,this}domain(){return this.#c=t.domain,this}assemblies(...o){return this.#t=o,this}classes(...o){return this.#s=o,this}methods(...o){return this.#n=o,this}filterAssemblies(o){return this.#r=o,this}filterClasses(o){return this.#o=o,this}filterMethods(o){return this.#i=o,this}filterParameters(o){return this.#a=o,this}and(){let o=p=>{if(this.#a==null){this.#l.push(p);return}for(let y of p.parameters)if(this.#a(y)){this.#l.push(p);break}},i=p=>{for(let y of p)o(y)},a=p=>{if(this.#i==null){i(p.methods);return}for(let y of p.methods)this.#i(y)&&o(y)},l=p=>{for(let y of p)a(y)},d=p=>{if(this.#o==null){l(p.image.classes);return}for(let y of p.image.classes)this.#o(y)&&a(y)},g=p=>{for(let y of p)d(y)},f=p=>{if(this.#r==null){g(p.assemblies);return}for(let y of p.assemblies)this.#r(y)&&d(y)};return this.#n?i(this.#n):this.#s?l(this.#s):this.#t?g(this.#t):this.#c&&f(this.#c),this.#t=void 0,this.#s=void 0,this.#n=void 0,this.#r=void 0,this.#o=void 0,this.#i=void 0,this.#a=void 0,this}attach(){for(let o of this.#l)if(!o.virtualAddress.isNull())try{this.#d(o,this.#e,this.#u)}catch(i){switch(i.message){case/unable to intercept function at \w+; please file a bug/.exec(i.message)?.input:case"already replaced this function":break;default:throw i}}}}t.Tracer=e;function n(r=!1){let o=()=>(a,l,d)=>{let g=a.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(a.virtualAddress,{onEnter(){this.threadId==d&&l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(l.depth++)}\u250C\u2500\x1B[35m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==d&&(l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--l.depth)}\u2514\u2500\x1B[33m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m`),l.flush())}})},i=()=>(a,l,d)=>{let g=a.relativeVirtualAddress.toString(16).padStart(8,"0"),f=+!a.isStatic|+t.unityVersionIsBelow201830,p=function(...S){if(this.threadId==d){let L=a.isStatic?void 0:new t.Parameter("this",-1,a.class.type),O=L?[L].concat(a.parameters):a.parameters;l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(l.depth++)}\u250C\u2500\x1B[35m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m(${O.map(R=>`\x1B[32m${R.name}\x1B[0m = \x1B[31m${t.fromFridaValue(S[R.position+f],R.type)}\x1B[0m`).join(", ")})`)}let w=a.nativeFunction(...S);return this.threadId==d&&(l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--l.depth)}\u2514\u2500\x1B[33m${a.class.type.name}::\x1B[1m${a.name}\x1B[0m\x1B[0m${w==null?"":` = \x1B[36m${t.fromFridaValue(w,a.returnType)}`}\x1B[0m`),l.flush()),w};a.revert();let y=new NativeCallback(p,a.returnType.fridaAlias,a.fridaSignature);Interceptor.replace(a.virtualAddress,y)};return new t.Tracer(r?i():o())}t.trace=n;function s(r){let o=t.domain.assemblies.flatMap(l=>l.image.classes.flatMap(d=>d.methods.filter(g=>!g.virtualAddress.isNull()))).sort((l,d)=>l.virtualAddress.compare(d.virtualAddress)),i=l=>{let d=0,g=o.length-1;for(;d<=g;){let f=Math.floor((d+g)/2),p=o[f].virtualAddress.compare(l);if(p==0)return o[f];p>0?g=f-1:d=f+1}return o[g]},a=()=>(l,d,g)=>{Interceptor.attach(l.virtualAddress,function(){if(this.threadId==g){let f=globalThis.Thread.backtrace(this.context,r);f.unshift(l.virtualAddress);for(let p of f)if(p.compare(t.module.base)>0&&p.compare(t.module.base.add(t.module.size))<0){let y=i(p);if(y){let S=p.sub(y.virtualAddress);S.compare(4095)<0&&d.buffer.push(`\x1B[2m0x${y.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${S.toString(16).padStart(3,"0")}\x1B[0m ${y.class.type.name}::\x1B[1m${y.name}\x1B[0m`)}}d.flush()}})};return new t.Tracer(a())}t.backtrace=s})(h||(h={}));var h;(function(t){class e extends b{static get headerSize(){return t.corlib.class("System.Array").instanceSize}get elements(){let o=t.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(i=>i.readS16()==118)??m("couldn't find the elements offset in the native array struct");return T(t.Array.prototype,"elements",function(){return new t.Pointer(this.handle.add(o),this.elementType)},c),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return t.exports.arrayGetLength(this)}get object(){return new t.Object(this)}get(r){return(r<0||r>=this.length)&&m(`cannot get element at index ${r} as the array length is ${this.length}`),this.elements.get(r)}set(r,o){(r<0||r>=this.length)&&m(`cannot set element at index ${r} as the array length is ${this.length}`),this.elements.set(r,o)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let r=0;r<this.length;r++)yield this.elements.get(r)}}u([c],e.prototype,"elementSize",null),u([c],e.prototype,"elementType",null),u([c],e.prototype,"length",null),u([c],e.prototype,"object",null),u([c],e,"headerSize",null),t.Array=e;function n(s,r){let o=typeof r=="number"?r:r.length,i=new t.Array(t.exports.arrayNew(s,o));return globalThis.Array.isArray(r)&&i.elements.write(r),i}t.array=n})(h||(h={}));var h;(function(t){let e=class extends b{get image(){if(t.exports.assemblyGetImage.isNull()){let s=this.object.tryMethod("GetType",1)?.invoke(t.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??m(`couldn't find the runtime module object of assembly ${this.name}`);return new t.Image(s.field("_impl").value)}return new t.Image(t.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let s of t.domain.object.method("GetAssemblies",1).invoke(!1))if(s.field("_mono_assembly").value.equals(this))return s;m("couldn't find the object of the native assembly struct")}};u([c],e.prototype,"name",null),u([c],e.prototype,"object",null),e=u([A],e),t.Assembly=e})(h||(h={}));var h;(function(t){let e=class extends b{get actualInstanceSize(){let s=t.corlib.class("System.String"),r=s.handle.offsetOf(o=>o.readInt()==s.instanceSize-2)??m("couldn't find the actual instance size offset in the native class struct");return T(t.Class.prototype,"actualInstanceSize",function(){return this.handle.add(r).readS32()},c),this.actualInstanceSize}get arrayClass(){return new t.Class(t.exports.classGetArrayClass(this,1))}get arrayElementSize(){return t.exports.classGetArrayElementSize(this)}get assemblyName(){return t.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new t.Class(t.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new t.Type(t.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new t.Class(t.exports.classGetElementClass(this)).asNullable()}get fields(){return $(s=>t.exports.classGetFields(this,s)).map(s=>new t.Field(s))}get flags(){return t.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let s=this.image.tryClass(this.fullName)?.asNullable();return s?.equals(this)?null:s??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let s=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(s).map(r=>new t.Class(t.exports.classFromObject(r)))}get hasReferences(){return!!t.exports.classHasReferences(this)}get hasStaticConstructor(){let s=this.tryMethod(".cctor");return s!=null&&!s.virtualAddress.isNull()}get image(){return new t.Image(t.exports.classGetImage(this))}get instanceSize(){return t.exports.classGetInstanceSize(this)}get isAbstract(){return!!t.exports.classIsAbstract(this)}get isBlittable(){return!!t.exports.classIsBlittable(this)}get isEnum(){return!!t.exports.classIsEnum(this)}get isGeneric(){return!!t.exports.classIsGeneric(this)}get isInflated(){return!!t.exports.classIsInflated(this)}get isInterface(){return!!t.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!t.exports.classIsValueType(this)}get interfaces(){return $(s=>t.exports.classGetInterfaces(this,s)).map(s=>new t.Class(s))}get methods(){return $(s=>t.exports.classGetMethods(this,s)).map(s=>new t.Method(s))}get name(){return t.exports.classGetName(this).readUtf8String()}get namespace(){return t.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return $(s=>t.exports.classGetNestedClasses(this,s)).map(s=>new t.Class(s))}get parent(){return new t.Class(t.exports.classGetParent(this)).asNullable()}get pointerClass(){return new t.Class(t.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let s=0,r=this.name;for(let o=this.name.length-1;o>0;o--){let i=r[o];if(i=="]")s++;else{if(i=="["||s==0)break;if(i==",")s++;else break}}return s}get staticFieldsData(){return t.exports.classGetStaticFieldData(this)}get valueTypeSize(){return t.exports.classGetValueTypeSize(this,NULL)}get type(){return new t.Type(t.exports.classGetType(this))}alloc(){return new t.Object(t.exports.objectNew(this))}field(s){return this.tryField(s)??m(`couldn't find field ${s} in class ${this.type.name}`)}*hierarchy(s){let r=s?.includeCurrent??!0?this:this.parent;for(;r;)yield r,r=r.parent}inflate(...s){this.isGeneric||m(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=s.length&&m(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${s.length}`);let r=s.map(a=>a.type.object),o=t.array(t.corlib.class("System.Type"),r),i=this.type.object.method("MakeGenericType",1).invoke(o);return new t.Class(t.exports.classFromObject(i))}initialize(){return t.exports.classInitialize(this),this}isAssignableFrom(s){return!!t.exports.classIsAssignableFrom(this,s)}isSubclassOf(s,r){return!!t.exports.classIsSubclassOf(this,s,+r)}method(s,r=-1){return this.tryMethod(s,r)??m(`couldn't find method ${s} in class ${this.type.name}`)}nested(s){return this.tryNested(s)??m(`couldn't find nested class ${s} in class ${this.type.name}`)}new(){let s=this.alloc(),r=Memory.alloc(Process.pointerSize);t.exports.objectInitialize(s,r);let o=r.readPointer();return o.isNull()||m(new t.Object(o).toString()),s}tryField(s){return new t.Field(t.exports.classGetFieldFromName(this,Memory.allocUtf8String(s))).asNullable()}tryMethod(s,r=-1){return new t.Method(t.exports.classGetMethodFromName(this,Memory.allocUtf8String(s),r)).asNullable()}tryNested(s){return this.nestedClasses.find(r=>r.name==s)}toString(){let s=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${s?` : ${s.map(r=>r?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(s){let r=new NativeCallback(o=>s(new t.Class(o)),"void",["pointer","pointer"]);return t.exports.classForEach(r,NULL)}};u([c],e.prototype,"arrayClass",null),u([c],e.prototype,"arrayElementSize",null),u([c],e.prototype,"assemblyName",null),u([c],e.prototype,"declaringClass",null),u([c],e.prototype,"baseType",null),u([c],e.prototype,"elementClass",null),u([c],e.prototype,"fields",null),u([c],e.prototype,"flags",null),u([c],e.prototype,"fullName",null),u([c],e.prototype,"generics",null),u([c],e.prototype,"hasReferences",null),u([c],e.prototype,"hasStaticConstructor",null),u([c],e.prototype,"image",null),u([c],e.prototype,"instanceSize",null),u([c],e.prototype,"isAbstract",null),u([c],e.prototype,"isBlittable",null),u([c],e.prototype,"isEnum",null),u([c],e.prototype,"isGeneric",null),u([c],e.prototype,"isInflated",null),u([c],e.prototype,"isInterface",null),u([c],e.prototype,"isValueType",null),u([c],e.prototype,"interfaces",null),u([c],e.prototype,"methods",null),u([c],e.prototype,"name",null),u([c],e.prototype,"namespace",null),u([c],e.prototype,"nestedClasses",null),u([c],e.prototype,"parent",null),u([c],e.prototype,"pointerClass",null),u([c],e.prototype,"rank",null),u([c],e.prototype,"staticFieldsData",null),u([c],e.prototype,"valueTypeSize",null),u([c],e.prototype,"type",null),e=u([A],e),t.Class=e})(h||(h={}));var h;(function(t){function e(n,s){let r=t.corlib.class("System.Delegate"),o=t.corlib.class("System.MulticastDelegate");r.isAssignableFrom(n)||m(`cannot create a delegate for ${n.type.name} as it's a non-delegate class`),(n.equals(r)||n.equals(o))&&m(`cannot create a delegate for neither ${r.type.name} nor ${o.type.name}, use a subclass instead`);let i=n.alloc(),a=i.handle.toString(),l=i.tryMethod("Invoke")??m(`cannot create a delegate for ${n.type.name}, there is no Invoke method`);i.method(".ctor").invoke(i,l.handle);let d=l.wrap(s);return i.field("method_ptr").value=d,i.field("invoke_impl").value=d,t._callbacksToKeepAlive[a]=d,i}t.delegate=e,t._callbacksToKeepAlive={}})(h||(h={}));var h;(function(t){let e=class extends b{get assemblies(){let s=z(r=>t.exports.domainGetAssemblies(this,r));if(s.length==0){let r=this.object.method("GetAssemblies").overload().invoke();s=globalThis.Array.from(r).map(o=>o.field("_mono_assembly").value)}return s.map(r=>new t.Assembly(r))}get object(){return t.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(s){return this.tryAssembly(s)??m(`couldn't find assembly ${s}`)}attach(){return new t.Thread(t.exports.threadAttach(this))}tryAssembly(s){return new t.Assembly(t.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(s))).asNullable()}};u([c],e.prototype,"assemblies",null),u([c],e.prototype,"object",null),e=u([A],e),t.Domain=e,T(t,"domain",()=>new t.Domain(t.exports.domainGet()),c)})(h||(h={}));var h;(function(t){class e extends b{get class(){return new t.Class(t.exports.fieldGetClass(this))}get flags(){return t.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let s=t.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return T(t.Field.prototype,"isThreadStatic",function(){return this.offset==s},c),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return t.exports.fieldGetName(this).readUtf8String()}get offset(){return t.exports.fieldGetOffset(this)}get type(){return new t.Type(t.exports.fieldGetType(this))}get value(){this.isStatic||m(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let s=Memory.alloc(Process.pointerSize);return t.exports.fieldGetStaticValue(this.handle,s),t.read(s,this.type)}set value(s){this.isStatic||m(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&m(`cannot write the value of field ${this.name} as it's thread static or literal`);let r=s instanceof t.Object&&this.type.class.isValueType?s.unbox():s instanceof b?s.handle:s instanceof NativePointer?s:t.write(Memory.alloc(this.type.class.valueTypeSize),s,this.type);t.exports.fieldSetStaticValue(this.handle,r)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?t.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(s){this.isStatic&&m(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let r=this.offset-(s instanceof t.ValueType?t.Object.headerSize:0);return new Proxy(this,{get(o,i){return i=="value"?t.read(s.handle.add(r),o.type):Reflect.get(o,i)},set(o,i,a){return i=="value"?(t.write(s.handle.add(r),a,o.type),!0):Reflect.set(o,i,a)}})}}u([c],e.prototype,"class",null),u([c],e.prototype,"flags",null),u([c],e.prototype,"isLiteral",null),u([c],e.prototype,"isStatic",null),u([c],e.prototype,"isThreadStatic",null),u([c],e.prototype,"modifier",null),u([c],e.prototype,"name",null),u([c],e.prototype,"offset",null),u([c],e.prototype,"type",null),t.Field=e})(h||(h={}));var h;(function(t){class e{handle;constructor(s){this.handle=s}get target(){return new t.Object(t.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return t.exports.gcHandleFree(this.handle)}}t.GCHandle=e})(h||(h={}));var h;(function(t){let e=class extends b{get assembly(){return new t.Assembly(t.exports.imageGetAssembly(this))}get classCount(){return t.unityVersionIsBelow201830?this.classes.length:t.exports.imageGetClassCount(this)}get classes(){if(t.unityVersionIsBelow201830){let s=this.assembly.object.method("GetTypes").invoke(!1),r=globalThis.Array.from(s,i=>new t.Class(t.exports.classFromObject(i))),o=this.tryClass("<Module>");return o&&r.unshift(o),r}else return globalThis.Array.from(globalThis.Array(this.classCount),(s,r)=>new t.Class(t.exports.imageGetClass(this,r)))}get name(){return t.exports.imageGetName(this).readUtf8String()}class(s){return this.tryClass(s)??m(`couldn't find class ${s} in assembly ${this.name}`)}tryClass(s){let r=s.lastIndexOf("."),o=Memory.allocUtf8String(r==-1?"":s.slice(0,r)),i=Memory.allocUtf8String(s.slice(r+1));return new t.Class(t.exports.classFromName(this,o,i)).asNullable()}};u([c],e.prototype,"assembly",null),u([c],e.prototype,"classCount",null),u([c],e.prototype,"classes",null),u([c],e.prototype,"name",null),e=u([A],e),t.Image=e,T(t,"corlib",()=>new t.Image(t.exports.getCorlib()),c)})(h||(h={}));var h;(function(t){class e extends b{static capture(){return new t.MemorySnapshot}constructor(r=t.exports.memorySnapshotCapture()){super(r)}get classes(){return $(r=>t.exports.memorySnapshotGetClasses(this,r)).map(r=>new t.Class(r))}get objects(){return z(r=>t.exports.memorySnapshotGetObjects(this,r)).filter(r=>!r.isNull()).map(r=>new t.Object(r))}free(){t.exports.memorySnapshotFree(this)}}u([c],e.prototype,"classes",null),u([c],e.prototype,"objects",null),t.MemorySnapshot=e;function n(s){let r=t.MemorySnapshot.capture(),o=s(r);return r.free(),o}t.memorySnapshot=n})(h||(h={}));var h;(function(t){class e extends b{get class(){return new t.Class(t.exports.methodGetClass(this))}get flags(){return t.exports.methodGetFlags(this,NULL)}get implementationFlags(){let r=Memory.alloc(Process.pointerSize);return t.exports.methodGetFlags(this,r),r.readU32()}get fridaSignature(){let r=[];for(let o of this.parameters)r.push(o.type.fridaAlias);return(!this.isStatic||t.unityVersionIsBelow201830)&&r.unshift("pointer"),this.isInflated&&r.push("pointer"),r}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let r=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(r).map(o=>new t.Class(t.exports.classFromObject(o)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!t.exports.methodIsGeneric(this)}get isInflated(){return!!t.exports.methodIsInflated(this)}get isStatic(){return!t.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return t.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new t.Object(t.exports.methodGetObject(this,NULL))}get parameterCount(){return t.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(r,o)=>{let i=t.exports.methodGetParameterName(this,o).readUtf8String(),a=t.exports.methodGetParameterType(this,o);return new t.Parameter(i,o,new t.Type(a))})}get relativeVirtualAddress(){return this.virtualAddress.sub(t.module.base)}get returnType(){return new t.Type(t.exports.methodGetReturnType(this))}get virtualAddress(){let r=t.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,o=r.field("method_ptr").value,a=r.field("method").value.offsetOf(l=>l.readPointer().equals(o))??m("couldn't find the virtual address offset in the native method struct");return T(t.Method.prototype,"virtualAddress",function(){return this.handle.add(a).readPointer()},c),t.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(r){try{Interceptor.replace(this.virtualAddress,this.wrap(r))}catch(o){switch(o.message){case"access violation accessing 0x0":m(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:k(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":k(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw o}}}inflate(...r){if(!this.isGeneric||this.generics.length!=r.length){for(let l of this.overloads())if(l.isGeneric&&l.generics.length==r.length)return l.inflate(...r);m(`could not find inflatable signature of method ${this.name} with ${r.length} generic parameter(s)`)}let o=r.map(l=>l.type.object),i=t.array(t.corlib.class("System.Type"),o),a=this.object.method("MakeGenericMethod",1).invoke(i);return new t.Method(a.field("mhandle").value)}invoke(...r){return this.isStatic||m(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...r)}invokeRaw(r,...o){let i=o.map(t.toFridaValue);(!this.isStatic||t.unityVersionIsBelow201830)&&i.unshift(r),this.isInflated&&i.push(this.handle);try{let a=this.nativeFunction(...i);return t.fromFridaValue(a,this.returnType)}catch(a){switch(a==null&&m("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),a.message){case"bad argument count":m(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${o.length}`);case"expected a pointer":case"expected number":case"expected array with fields":m(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw a}}overload(...r){return this.tryOverload(...r)??m(`couldn't find overloaded method ${this.name}(${r.map(i=>i instanceof t.Class?i.type.name:i)})`)}*overloads(){for(let r of this.class.hierarchy())for(let o of r.methods)this.name==o.name&&(yield o)}parameter(r){return this.tryParameter(r)??m(`couldn't find parameter ${r} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...r){let o=r.length*1,i=r.length*2,a;e:for(let l of this.overloads()){if(l.parameterCount!=r.length)continue;let d=0,g=0;for(let f of l.parameters){let p=r[g];if(p instanceof t.Class)if(f.type.is(p.type))d+=2;else if(f.type.class.isAssignableFrom(p))d+=1;else continue e;else if(f.type.name==p)d+=2;else continue e;g++}if(!(d<o)){if(d==i)return l;if(a==null||d>a[0])a=[d,l];else if(d==a[0]){let f=0;for(let p of a[1].parameters){if(p.type.class.isAssignableFrom(l.parameters[f].type.class)){a=[d,l];continue e}f++}}}}return a?.[1]}tryParameter(r){return this.parameters.find(o=>o.name==r)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(r=>r.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(r){return this.isStatic&&m(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(o,i,a){switch(i){case"invoke":let l=r instanceof t.ValueType?o.class.isValueType?r.handle.sub(n()?t.Object.headerSize:0):m(`cannot invoke method ${o.class.type.name}::${o.name} against a value type, you must box it first`):o.class.isValueType?r.handle.add(n()?0:t.Object.headerSize):r.handle;return o.invokeRaw.bind(o,l);case"overloads":return function*(){for(let g of o[i]())g.isStatic||(yield g)};case"inflate":case"overload":case"tryOverload":let d=Reflect.get(o,i).bind(a);return function(...g){return d(...g)?.bind(r)}}return Reflect.get(o,i)}})}wrap(r){let o=+!this.isStatic|+t.unityVersionIsBelow201830;return new NativeCallback((...i)=>{let a=this.isStatic?this.class:this.class.isValueType?new t.ValueType(i[0].add(n()?t.Object.headerSize:0),this.class.type):new t.Object(i[0]),l=this.parameters.map((g,f)=>t.fromFridaValue(i[f+o],g.type)),d=r.call(a,...l);return t.toFridaValue(d)},this.returnType.fridaAlias,this.fridaSignature)}}u([c],e.prototype,"class",null),u([c],e.prototype,"flags",null),u([c],e.prototype,"implementationFlags",null),u([c],e.prototype,"fridaSignature",null),u([c],e.prototype,"generics",null),u([c],e.prototype,"isExternal",null),u([c],e.prototype,"isGeneric",null),u([c],e.prototype,"isInflated",null),u([c],e.prototype,"isStatic",null),u([c],e.prototype,"isSynchronized",null),u([c],e.prototype,"modifier",null),u([c],e.prototype,"name",null),u([c],e.prototype,"nativeFunction",null),u([c],e.prototype,"object",null),u([c],e.prototype,"parameterCount",null),u([c],e.prototype,"parameters",null),u([c],e.prototype,"relativeVirtualAddress",null),u([c],e.prototype,"returnType",null),t.Method=e;let n=()=>{let s=t.corlib.class("System.Int64").alloc();s.field("m_value").value=3735928559;let r=s.method("Equals",1).overload(s.class).invokeRaw(s,3735928559);return(n=()=>r)()}})(h||(h={}));var h;(function(t){class e extends b{static get headerSize(){return t.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&m(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(s,r,o){return r=="class"?Reflect.get(s,r).parent:r=="base"?Reflect.getOwnPropertyDescriptor(t.Object.prototype,r).get.bind(o)():Reflect.get(s,r)}})}get class(){return new t.Class(t.exports.objectGetClass(this))}get monitor(){return new t.Object.Monitor(this)}get size(){return t.exports.objectGetSize(this)}field(s){return this.tryField(s)??m(`couldn't find non-static field ${s} in hierarchy of class ${this.class.type.name}`)}method(s,r=-1){return this.tryMethod(s,r)??m(`couldn't find non-static method ${s} in hierarchy of class ${this.class.type.name}`)}ref(s){return new t.GCHandle(t.exports.gcHandleNew(this,+s))}virtualMethod(s){return new t.Method(t.exports.objectGetVirtualMethod(this,s)).bind(this)}tryField(s){let r=this.class.tryField(s);if(r?.isStatic){for(let o of this.class.hierarchy({includeCurrent:!1}))for(let i of o.fields)if(i.name==s&&!i.isStatic)return i.bind(this);return}return r?.bind(this)}tryMethod(s,r=-1){let o=this.class.tryMethod(s,r);if(o?.isStatic){for(let i of this.class.hierarchy())for(let a of i.methods)if(a.name==s&&!a.isStatic&&(r<0||a.parameterCount==r))return a.bind(this);return}return o?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new t.ValueType(t.exports.objectUnbox(this),this.class.type):m(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(s){return new t.GCHandle(t.exports.gcHandleNewWeakRef(this,+s))}}u([c],e.prototype,"class",null),u([c],e.prototype,"size",null),u([c],e,"headerSize",null),t.Object=e,function(n){class s{handle;constructor(o){this.handle=o}enter(){return t.exports.monitorEnter(this.handle)}exit(){return t.exports.monitorExit(this.handle)}pulse(){return t.exports.monitorPulse(this.handle)}pulseAll(){return t.exports.monitorPulseAll(this.handle)}tryEnter(o){return!!t.exports.monitorTryEnter(this.handle,o)}tryWait(o){return!!t.exports.monitorTryWait(this.handle,o)}wait(){return t.exports.monitorWait(this.handle)}}n.Monitor=s}(e=t.Object||(t.Object={}))})(h||(h={}));var h;(function(t){class e{name;position;type;constructor(s,r,o){this.name=s,this.position=r,this.type=o}toString(){return`${this.type.name} ${this.name}`}}t.Parameter=e})(h||(h={}));var h;(function(t){class e extends b{type;constructor(s,r){super(s),this.type=r}get(s){return t.read(this.handle.add(s*this.type.class.arrayElementSize),this.type)}read(s,r=0){let o=new globalThis.Array(s);for(let i=0;i<s;i++)o[i]=this.get(i+r);return o}set(s,r){t.write(this.handle.add(s*this.type.class.arrayElementSize),r,this.type)}toString(){return this.handle.toString()}write(s,r=0){for(let o=0;o<s.length;o++)this.set(o+r,s[o])}}t.Pointer=e})(h||(h={}));var h;(function(t){class e extends b{type;constructor(r,o){super(r),this.type=o}get value(){return t.read(this.handle,this.type)}set value(r){t.write(this.handle,r,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}t.Reference=e;function n(s,r){let o=Memory.alloc(Process.pointerSize);switch(typeof s){case"boolean":return new t.Reference(o.writeS8(+s),t.corlib.class("System.Boolean").type);case"number":switch(r?.enumValue){case t.Type.Enum.UBYTE:return new t.Reference(o.writeU8(s),r);case t.Type.Enum.BYTE:return new t.Reference(o.writeS8(s),r);case t.Type.Enum.CHAR:case t.Type.Enum.USHORT:return new t.Reference(o.writeU16(s),r);case t.Type.Enum.SHORT:return new t.Reference(o.writeS16(s),r);case t.Type.Enum.UINT:return new t.Reference(o.writeU32(s),r);case t.Type.Enum.INT:return new t.Reference(o.writeS32(s),r);case t.Type.Enum.ULONG:return new t.Reference(o.writeU64(s),r);case t.Type.Enum.LONG:return new t.Reference(o.writeS64(s),r);case t.Type.Enum.FLOAT:return new t.Reference(o.writeFloat(s),r);case t.Type.Enum.DOUBLE:return new t.Reference(o.writeDouble(s),r)}case"object":if(s instanceof t.ValueType||s instanceof t.Pointer)return new t.Reference(s.handle,s.type);if(s instanceof t.Object)return new t.Reference(o.writePointer(s),s.class.type);if(s instanceof t.String||s instanceof t.Array)return new t.Reference(o.writePointer(s),s.object.class.type);if(s instanceof NativePointer)switch(r?.enumValue){case t.Type.Enum.NUINT:case t.Type.Enum.NINT:return new t.Reference(o.writePointer(s),r)}else{if(s instanceof Int64)return new t.Reference(o.writeS64(s),t.corlib.class("System.Int64").type);if(s instanceof UInt64)return new t.Reference(o.writeU64(s),t.corlib.class("System.UInt64").type)}default:m(`couldn't create a reference to ${s} using an unhandled type ${r?.name}`)}}t.reference=n})(h||(h={}));var h;(function(t){class e extends b{get content(){return t.exports.stringGetChars(this).readUtf16String(this.length)}set content(r){let o=t.string("vfsfitvnm").handle.offsetOf(i=>i.readInt()==9)??m("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(t.String.prototype,"content",{set(i){t.exports.stringGetChars(this).writeUtf16String(i??""),this.handle.add(o).writeS32(i?.length??0)}}),this.content=r}get length(){return t.exports.stringGetLength(this)}get object(){return new t.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}t.String=e;function n(s){return new t.String(t.exports.stringNew(Memory.allocUtf8String(s??"")))}t.string=n})(h||(h={}));var h;(function(t){class e extends b{get id(){let s=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let r=Process.getCurrentThreadId(),i=ptr(s.apply(t.currentThread)).offsetOf(l=>l.readS32()==r,1024)??m("couldn't find the offset for determining the kernel id of a posix thread"),a=s;s=function(){return ptr(a.apply(this)).add(i).readS32()}}return T(t.Thread.prototype,"id",s,c),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!t.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new t.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let r=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(r.tryField("_syncContext")?.value??r.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(t.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return t.exports.threadDetach(this)}schedule(s){let r=this.synchronizationContext?.tryMethod("Post");return r==null?Process.runOnThread(this.id,s):new Promise(o=>{let i=t.delegate(t.corlib.class("System.Threading.SendOrPostCallback"),()=>{let a=s();setImmediate(()=>o(a))});Script.bindWeak(globalThis,()=>{i.field("method_ptr").value=i.field("invoke_impl").value=t.exports.domainGet}),r.invoke(i,NULL)})}tryLocalValue(s){for(let r=0;r<16;r++){let o=this.staticData.add(r*Process.pointerSize).readPointer();if(!o.isNull()){let i=new t.Object(o.readPointer()).asNullable();if(i?.class?.isSubclassOf(s,!1))return i}}}}u([c],e.prototype,"internal",null),u([c],e.prototype,"isFinalizer",null),u([c],e.prototype,"managedId",null),u([c],e.prototype,"object",null),u([c],e.prototype,"staticData",null),u([c],e.prototype,"synchronizationContext",null),t.Thread=e,T(t,"attachedThreads",()=>{if(t.exports.threadGetAttachedThreads.isNull()){let n=t.currentThread?.handle??m("Current thread is not attached to IL2CPP"),s=n.toMatchPattern(),r=[];for(let o of Process.enumerateRanges("rw-"))if(o.file==null){let i=Memory.scanSync(o.base,o.size,s);if(i.length==1){for(;;){let a=i[0].address.sub(i[0].size*r.length).readPointer();if(a.isNull()||!a.readPointer().equals(n.readPointer()))break;r.unshift(new t.Thread(a))}break}}return r}return z(t.exports.threadGetAttachedThreads).map(n=>new t.Thread(n))}),T(t,"currentThread",()=>new t.Thread(t.exports.threadGetCurrent()).asNullable()),T(t,"mainThread",()=>t.attachedThreads[0])})(h||(h={}));var h;(function(t){let e=class extends b{static get Enum(){let s=(o,i=a=>a)=>i(t.corlib.class(o)).type.enumValue,r={VOID:s("System.Void"),BOOLEAN:s("System.Boolean"),CHAR:s("System.Char"),BYTE:s("System.SByte"),UBYTE:s("System.Byte"),SHORT:s("System.Int16"),USHORT:s("System.UInt16"),INT:s("System.Int32"),UINT:s("System.UInt32"),LONG:s("System.Int64"),ULONG:s("System.UInt64"),NINT:s("System.IntPtr"),NUINT:s("System.UIntPtr"),FLOAT:s("System.Single"),DOUBLE:s("System.Double"),POINTER:s("System.IntPtr",o=>o.field("m_value")),VALUE_TYPE:s("System.Decimal"),OBJECT:s("System.Object"),STRING:s("System.String"),CLASS:s("System.Array"),ARRAY:s("System.Void",o=>o.arrayClass),NARRAY:s("System.Void",o=>new t.Class(t.exports.classGetArrayClass(o,2))),GENERIC_INSTANCE:s("System.Int32",o=>o.interfaces.find(i=>i.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:r}),W({...r,VAR:s("System.Action`1",o=>o.generics[0]),MVAR:s("System.Array",o=>o.method("AsReadOnly",1).generics[0])})}get class(){return new t.Class(t.exports.typeGetClass(this))}get fridaAlias(){function s(r){let o=r.class.fields.filter(i=>!i.isStatic);return o.length==0?["char"]:o.map(i=>i.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case t.Type.Enum.VOID:return"void";case t.Type.Enum.BOOLEAN:return"bool";case t.Type.Enum.CHAR:return"uchar";case t.Type.Enum.BYTE:return"int8";case t.Type.Enum.UBYTE:return"uint8";case t.Type.Enum.SHORT:return"int16";case t.Type.Enum.USHORT:return"uint16";case t.Type.Enum.INT:return"int32";case t.Type.Enum.UINT:return"uint32";case t.Type.Enum.LONG:return"int64";case t.Type.Enum.ULONG:return"uint64";case t.Type.Enum.FLOAT:return"float";case t.Type.Enum.DOUBLE:return"double";case t.Type.Enum.NINT:case t.Type.Enum.NUINT:case t.Type.Enum.POINTER:case t.Type.Enum.STRING:case t.Type.Enum.ARRAY:case t.Type.Enum.NARRAY:return"pointer";case t.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:s(this);case t.Type.Enum.CLASS:case t.Type.Enum.OBJECT:case t.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?s(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case t.Type.Enum.BOOLEAN:case t.Type.Enum.CHAR:case t.Type.Enum.BYTE:case t.Type.Enum.UBYTE:case t.Type.Enum.SHORT:case t.Type.Enum.USHORT:case t.Type.Enum.INT:case t.Type.Enum.UINT:case t.Type.Enum.LONG:case t.Type.Enum.ULONG:case t.Type.Enum.FLOAT:case t.Type.Enum.DOUBLE:case t.Type.Enum.NINT:case t.Type.Enum.NUINT:return!0;default:return!1}}get name(){let s=t.exports.typeGetName(this);try{return s.readUtf8String()}finally{t.free(s)}}get object(){return new t.Object(t.exports.typeGetObject(this))}get enumValue(){return t.exports.typeGetTypeEnum(this)}is(s){return t.exports.typeEquals.isNull()?this.object.method("Equals").invoke(s.object):!!t.exports.typeEquals(this,s)}toString(){return this.name}};u([c],e.prototype,"class",null),u([c],e.prototype,"fridaAlias",null),u([c],e.prototype,"isByReference",null),u([c],e.prototype,"isPrimitive",null),u([c],e.prototype,"name",null),u([c],e.prototype,"object",null),u([c],e.prototype,"enumValue",null),u([c],e,"Enum",null),e=u([A],e),t.Type=e})(h||(h={}));var h;(function(t){class e extends b{type;constructor(s,r){super(s),this.type=r}box(){return new t.Object(t.exports.valueTypeBox(this.type.class,this))}field(s){return this.tryField(s)??m(`couldn't find non-static field ${s} in hierarchy of class ${this.type.name}`)}method(s,r=-1){return this.tryMethod(s,r)??m(`couldn't find non-static method ${s} in hierarchy of class ${this.type.name}`)}tryField(s){let r=this.type.class.tryField(s);if(r?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.fields)if(i.name==s&&!i.isStatic)return i.bind(this);return}return r?.bind(this)}tryMethod(s,r=-1){let o=this.type.class.tryMethod(s,r);if(o?.isStatic){for(let i of this.type.class.hierarchy())for(let a of i.methods)if(a.name==s&&!a.isStatic&&(r<0||a.parameterCount==r))return a.bind(this);return}return o?.bind(this)}toString(){let s=this.method("ToString",0);return this.isNull()?"null":s.class.isValueType?s.invoke().content??"null":this.box().toString()??"null"}}t.ValueType=e})(h||(h={}));globalThis.Il2Cpp=h;var E=class{config;errorCounts=new Map;retryHistory=new Map;lastErrorTime=new Map;constructor(e){this.config={maxRetries:5,retryDelay:1e3,enableRetryBackoff:!0,maxBackoffDelay:1e4,ignoreKnownErrors:!0,logErrorDetails:!0,...e}}classifyError(e){let s=(e instanceof Error?e.message:String(e)).toLowerCase();return s.includes("breakpoint triggered")||s.includes("breakpoint")?"breakpoint_triggered":s.includes("access violation")||s.includes("segmentation fault")||s.includes("sigsegv")?"access_violation":s.includes("abort was called")||s.includes("abort()")||s.includes("sigabrt")?"abort_called":(s.includes("invalid pointer")||s.includes("null pointer")||s.includes("bad pointer")||s.includes("timeout")||s.includes("timed out"),"unknown_error")}shouldIgnoreError(e){return this.config.ignoreKnownErrors?["breakpoint_triggered","access_violation","abort_called","illegal_instruction"].includes(e):!1}calculateRetryDelay(e){if(!this.config.enableRetryBackoff)return this.config.retryDelay;let n=this.config.retryDelay*Math.pow(2,e-1);return Math.min(n,this.config.maxBackoffDelay)}shouldRetry(e,n){if((this.errorCounts.get(e)||0)>=this.config.maxRetries||n==="unknown_error"&&this.config.ignoreKnownErrors)return!1;let r=this.lastErrorTime.get(e);return!(r&&Date.now()-r<100)}recordRetryAttempt(e,n,s){let r=this.errorCounts.get(e)||0,o=s?0:r+1;this.errorCounts.set(e,o),this.lastErrorTime.set(e,Date.now());let i=this.retryHistory.get(e)||[],a={attemptNumber:r+1,timestamp:Date.now(),errorType:n,success:s,delay:this.calculateRetryDelay(r+1)};i.push(a),i.length>10&&i.shift(),this.retryHistory.set(e,i)}async executeWithProtection(e,n,s){let r=null;for(let o=1;o<=this.config.maxRetries+1;o++)try{let i=await n();return this.recordRetryAttempt(e,"unknown_error",!0),o>1&&this.config.logErrorDetails&&console.log(`[+] ${s||e}: Succeeded on attempt ${o}`),i}catch(i){r=i;let a=this.classifyError(i);if(this.recordRetryAttempt(e,a,!1),this.config.logErrorDetails){let l=i instanceof Error?i.message:String(i);console.log(`[!] ${s||e}: Attempt ${o} failed - ${a}: ${l}`)}if(this.shouldIgnoreError(a))return this.config.logErrorDetails&&console.log(`[~] ${s||e}: Ignoring known anti-debug error (${a})`),null;if(o<=this.config.maxRetries&&this.shouldRetry(e,a)){let l=this.calculateRetryDelay(o);this.config.logErrorDetails&&console.log(`[*] ${s||e}: Retrying in ${l}ms (attempt ${o+1}/${this.config.maxRetries+1})`),await new Promise(d=>setTimeout(d,l));continue}break}if(this.config.logErrorDetails){let o=this.classifyError(r);console.log(`[\u274C] ${s||e}: All attempts failed - final error: ${o}`)}return null}validateInstancePointer(e,n){let s=`validate_${e.toString()}`;try{if(e.isNull())return!1;let r=e.readU32();return!0}catch(r){let o=this.classifyError(r);return this.shouldIgnoreError(o)?(this.config.logErrorDetails&&console.log(`[~] ${n||"validateInstance"}: Ignoring validation error (${o}) for ${e}`),!1):(this.config.logErrorDetails&&console.log(`[!] ${n||"validateInstance"}: Validation failed for ${e} - ${o}`),!1)}}getRetryStats(e){return{retryCount:this.errorCounts.get(e)||0,history:this.retryHistory.get(e)||[]}}clearRetryHistory(e){this.errorCounts.delete(e),this.retryHistory.delete(e),this.lastErrorTime.delete(e)}getProtectionStats(){let e=this.errorCounts.size,n=Array.from(this.errorCounts.values()).reduce((r,o)=>r+o,0),s=new Map;for(let r of this.retryHistory.values())for(let o of r)if(!o.success){let i=s.get(o.errorType)||0;s.set(o.errorType,i+1)}return{totalOperations:e,totalRetries:n,errorTypeDistribution:s}}};var M=class{methods;addresses;moduleInfo;hooks=new Map;errorTrackers=new Map;antiDebugProtection;onCanCollectCallback;onFinishCollectCallback;onUpdateCallback;processedInstances=new Set;realTimeCollectionEnabled=!0;constructor(e,n,s){this.methods=e,this.addresses=n,this.moduleInfo=s,this.antiDebugProtection=new E({maxRetries:5,retryDelay:1e3,enableRetryBackoff:!0,ignoreKnownErrors:!0,logErrorDetails:!0}),console.log("\u{1F3A3} MethodHookManager initialized with anti-debugging protection")}setupAllHooks(){console.log("\u{1F3AF} Setting up all method hooks...");try{this.setupCanCollectHook(),this.setupFinishCollectHook(),this.setupUpdateHook(),this.setupRewardHooks(),console.log("\u2705 All method hooks setup complete")}catch(e){console.log(`\u274C Failed to setup hooks: ${e}`)}}setupCanCollectHook(){try{if(console.log("\u{1F3A3} Setting up CanCollect hook..."),!this.methods.CanCollect){console.log("\u26A0\uFE0F CanCollect method not available for hooking");return}let e=this.methods.CanCollect.implementation,n=this;this.methods.CanCollect.implementation=function(){try{let s=e.call(this),r=!!s;return console.log(`[*] CanCollect() called on GoodyHutHelper: ${this.handle} -> ${r}`),r&&(console.log("[+] Ruins can be collected! Auto-triggering..."),n.onCanCollectCallback&&n.onCanCollectCallback(this.handle,r),n.triggerAutoCollection(this.handle)),s}catch(s){return n.handleHookError("CanCollect",s,this.handle),e.call(this)}},console.log("\u2705 CanCollect hook setup successfully using IL2CPP bridge")}catch(e){console.log(`\u274C Failed to setup CanCollect hook: ${e}`)}}setupFinishCollectHook(){try{if(console.log("\u{1F3A3} Setting up FinishCollect hook..."),!this.methods.FinishCollect){console.log("\u26A0\uFE0F FinishCollect method not available for hooking");return}let e=this.methods.FinishCollect.implementation,n=this;this.methods.FinishCollect.implementation=function(){try{let s=Date.now(),r=e.call(this),o=Date.now()-s;return console.log(`[+] FinishCollect() completed in ${o}ms`),n.onFinishCollectCallback&&n.onFinishCollectCallback(this.handle,!0),n.updateEntityStateForCleanup(this.handle),r}catch(s){return n.handleHookError("FinishCollect",s,this.handle),e.call(this)}},console.log("\u2705 FinishCollect hook setup successfully using IL2CPP bridge")}catch(e){console.log(`\u274C Failed to setup FinishCollect hook: ${e}`)}}setupUpdateHook(){try{if(console.log("\u{1F3A3} Setting up Update hook for real-time discovery..."),!this.methods.Update){console.log("\u26A0\uFE0F Update method not available for hooking");return}let e=this.methods.Update.implementation,n=this,s=0;this.methods.Update.implementation=function(){try{return s++,s%15===0&&(n.onUpdateCallback&&n.onUpdateCallback(this.handle),n.checkInstanceForRealTimeCollection(this.handle)),e.call(this)}catch(r){return n.handleHookError("Update",r,this.handle),e.call(this)}},console.log("\u2705 Update hook setup successfully using IL2CPP bridge")}catch(e){console.log(`\u274C Failed to setup Update hook: ${e}`)}}setupRewardHooks(){try{if(console.log("\u{1F3A3} Setting up reward monitoring hooks..."),this.methods.GetRewardType){let e=this.methods.GetRewardType.implementation;this.methods.GetRewardType.implementation=function(){let n=e.call(this),s=n?n.valueOf():0;return console.log(`[*] Reward Type: ${s}`),n}}if(this.methods.GetRewardAmount){let e=this.methods.GetRewardAmount.implementation;this.methods.GetRewardAmount.implementation=function(){let n=e.call(this),s=n?n.valueOf():0;return console.log(`[*] Reward Amount: ${s}`),n}}console.log("\u2705 Reward monitoring hooks setup successfully using IL2CPP bridge")}catch(e){console.log(`\u274C Failed to setup reward hooks: ${e}`)}}triggerAutoCollection(e){try{console.log(`[+] Triggering auto-collection for instance: ${e}`),this.setCleanupFlag(e),this.callFinishCollect(e)}catch(n){this.handleHookError("triggerAutoCollection",n,e)}}setCleanupFlag(e){try{let n=this.methods.Config?.handle||this.addresses.Config;if(!n||n.isNull())return console.log("\u26A0\uFE0F Config method not available for cleanup flag"),!1;let r=new NativeFunction(n,"pointer",["pointer"])(e);if(r&&!r.isNull()&&!r.equals(ptr(0)))try{return r.add(48).writeU8(1),console.log(`[+] Set cleanUp flag for instance: ${e}`),!0}catch(o){return console.log(`[-] Error setting cleanUp flag: ${o}`),!1}else return console.log(`[-] Config() returned null/invalid pointer for: ${e}`),!1}catch(n){return console.log(`[-] Error in setCleanupFlag: ${n}`),!1}}callFinishCollect(e){try{let n=this.methods.FinishCollect?.handle||this.addresses.FinishCollect;return!n||n.isNull()?(console.log("\u26A0\uFE0F FinishCollect method not available"),!1):(new NativeFunction(n,"void",["pointer"])(e),console.log(`[+] FinishCollect() executed successfully on: ${e}`),!0)}catch(n){return console.log(`[-] Error executing FinishCollect(): ${n}`),!1}}checkInstanceForRealTimeCollection(e){try{if(!this.realTimeCollectionEnabled)return;let n=e.toString();if(this.processedInstances.has(n))return;let s=this.methods.CanCollect?.handle||this.addresses.CanCollect;if(!s||s.isNull())return;new NativeFunction(s,"int",["pointer"])(e)===1&&(console.log(`[+] REAL-TIME: Found collectible ruins at: ${e} - Processing immediately`),this.triggerRealTimeCollection(e)?(this.processedInstances.add(n),console.log(`[+] REAL-TIME SUCCESS: Collected ruins at ${e} (Total processed: ${this.processedInstances.size})`)):console.log(`[-] REAL-TIME FAILED: Error processing ${n}`))}catch{}}triggerRealTimeCollection(e){try{console.log(`[+] Triggering real-time collection for instance: ${e}`);let n=this.setCleanupFlag(e),s=this.callFinishCollect(e);return n&&s}catch(n){return this.handleHookError("triggerRealTimeCollection",n,e),!1}}updateEntityStateForCleanup(e){try{console.log(`[*] Updating entity state for cleanup: ${e}`),setTimeout(()=>{try{console.log("[*] Attempting automatic ruin selling...");let n=this.methods.SellRuins?.handle||this.addresses.SellRuins;n&&!n.isNull()&&(new NativeFunction(n,"void",["pointer"])(e),console.log("[+] SellRuins() executed for automatic cleanup"))}catch(n){console.log(`[-] Error in automatic ruin selling: ${n}`)}},1e3)}catch(n){console.log(`[-] Error updating state for cleanup: ${n}`)}}handleHookError(e,n,s){let r=s?s.toString():"unknown",o=`${e}_${r}`,i=this.antiDebugProtection.classifyError(n);this.antiDebugProtection.recordRetryAttempt(o,i,!1);let a={errorType:i,errorMessage:n instanceof Error?n.message:String(n),occurredAt:Date.now(),instanceId:r,methodName:e,retryCount:this.antiDebugProtection.getRetryStats(o).retryCount};this.errorTrackers.set(`${e}_${r}_${Date.now()}`,a),this.antiDebugProtection.shouldIgnoreError(i)?console.log(`\u26A0\uFE0F ${e}: Known anti-debug error (${i}) - continuing operation`):console.log(`\u274C ${e}: Error requiring attention - ${i}: ${a.errorMessage}`)}async executeWithProtection(e,n,s){return await this.antiDebugProtection.executeWithProtection(e,n,s)}validateInstance(e,n){return this.antiDebugProtection.validateInstancePointer(e,n)}getProtectionStats(){return this.antiDebugProtection.getProtectionStats()}onCanCollect(e){this.onCanCollectCallback=e}onFinishCollect(e){this.onFinishCollectCallback=e}onUpdate(e){this.onUpdateCallback=e}setRealTimeCollectionEnabled(e){this.realTimeCollectionEnabled=e,console.log(`[*] Real-time collection ${e?"ENABLED":"DISABLED"}`)}getRealTimeStats(){return{processed:this.processedInstances.size,enabled:this.realTimeCollectionEnabled}}clearProcessedInstances(){this.processedInstances.clear(),console.log("[+] Cleared real-time processed instances tracking")}removeAllHooks(){console.log("\u{1F9F9} Removing all method hooks..."),this.hooks.forEach((e,n)=>{try{e.detach(),console.log(`\u2705 Removed ${n} hook`)}catch(s){console.log(`\u26A0\uFE0F Error removing ${n} hook: ${s}`)}}),this.hooks.clear(),this.errorTrackers.clear(),console.log("\u2705 All hooks removed and cleaned up")}getHookStats(){return{activeHooks:this.hooks.size,errorCount:this.errorTrackers.size,errors:Array.from(this.errorTrackers.values())}}};var D=class{config;stats;phaseTracker;methods;addresses;discoveredInstances=new Map;processedInstances=new Set;failedInstances=new Map;isProcessingBatch=!1;batchNumber=0;batchProcessingInterval=null;statusMonitoringInterval=null;cleanupInterval=null;onBatchCompleteCallback;onPhaseChangeCallback;onInstanceProcessedCallback;constructor(e,n,s){this.config=e,this.methods=n,this.addresses=s,this.stats={totalDiscoveryScans:0,discoveredInstances:0,processedInstances:0,failedInstances:0,batchNumber:0,isProcessingBatch:!1,currentPhase:1,lastDiscoveryTime:Date.now(),discoveryCompleteTime:0},this.phaseTracker={currentPhase:1,phaseStartTime:Date.now(),discoveryComplete:!1},console.log("\u{1F4E6} BatchProcessor initialized with configuration:"),console.log(`   - Batch size: ${this.config.batchSize}`),console.log(`   - Batch interval: ${this.config.batchInterval}ms`),console.log(`   - Retry limit: ${this.config.retryLimit}`),console.log(`   - Discovery timeout: ${this.config.discoveryTimeout}ms`)}async startBatchProcessing(){console.log("\u{1F680} Starting two-phase batch processing system..."),this.startDiscoveryPhase(),this.setupBatchProcessingTimer(),this.setupStatusMonitoring(),this.setupCleanupTimer(),console.log("\u2705 Batch processing system started")}stopBatchProcessing(){console.log("\u{1F6D1} Stopping batch processing system..."),this.batchProcessingInterval&&(clearInterval(this.batchProcessingInterval),this.batchProcessingInterval=null),this.statusMonitoringInterval&&(clearInterval(this.statusMonitoringInterval),this.statusMonitoringInterval=null),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.isProcessingBatch=!1,console.log("\u2705 Batch processing system stopped")}addDiscoveredInstance(e,n){let s=e.toString();if(!this.processedInstances.has(s)&&!this.discoveredInstances.has(s)){let r={ptr:e,discoveryTime:Date.now(),retryCount:0,entityId:n||s};this.discoveredInstances.set(s,r),this.stats.discoveredInstances=this.discoveredInstances.size,this.stats.lastDiscoveryTime=Date.now(),this.config.batchSize===1&&console.log(`[+] REAL-TIME: Added instance ${s} for immediate processing`)}}forceCompleteDiscovery(){this.phaseTracker.currentPhase===1?(console.log("[*] Forcing discovery phase completion..."),this.completeDiscoveryPhase()):console.log("[*] Discovery phase already completed")}forceBatchProcess(){this.phaseTracker.currentPhase===2?(console.log("[*] Forcing immediate batch processing..."),this.processCollectionBatch()):console.log("[*] Cannot force batch processing - still in discovery phase")}resetToDiscovery(){console.log("[*] Resetting to discovery phase..."),this.phaseTracker.currentPhase=1,this.phaseTracker.phaseStartTime=Date.now(),this.phaseTracker.discoveryComplete=!1,this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.batchNumber=0,this.isProcessingBatch=!1,this.stats.lastDiscoveryTime=Date.now(),this.stats.totalDiscoveryScans=0,this.onPhaseChangeCallback&&this.onPhaseChangeCallback(1),console.log("[+] Reset complete - discovery phase restarted")}startDiscoveryPhase(){console.log("[+] ===== PHASE 1: DISCOVERY SCAN STARTED ====="),console.log("[*] Scanning for ALL collectible GoodyHutHelper instances..."),console.log(`[*] Discovery will complete after ${this.config.discoveryTimeout/1e3} seconds with no new findings`),this.phaseTracker.currentPhase=1,this.phaseTracker.phaseStartTime=Date.now(),this.phaseTracker.discoveryComplete=!1,this.stats.currentPhase=1,this.stats.lastDiscoveryTime=Date.now()}completeDiscoveryPhase(){this.phaseTracker.currentPhase=2,this.phaseTracker.phaseEndTime=Date.now(),this.phaseTracker.phaseDuration=this.phaseTracker.phaseEndTime-this.phaseTracker.phaseStartTime,this.phaseTracker.discoveryComplete=!0,this.stats.currentPhase=2,this.stats.discoveryCompleteTime=this.phaseTracker.phaseEndTime,console.log("[+] ===== PHASE 1: DISCOVERY SCAN COMPLETED ====="),console.log(`[+] Total collectible instances discovered: ${this.discoveredInstances.size}`),console.log(`[+] Total discovery scans performed: ${this.stats.totalDiscoveryScans}`),console.log(`[+] Discovery phase duration: ${(this.phaseTracker.phaseDuration/1e3).toFixed(1)} seconds`),this.onPhaseChangeCallback&&this.onPhaseChangeCallback(2),this.discoveredInstances.size===0?console.log("[*] No collectible ruins found. Monitoring will continue for new instances."):(console.log("[+] ===== PHASE 2: BATCH COLLECTION STARTING ====="),console.log(`[*] Beginning batch processing of ${this.discoveredInstances.size} instances`),console.log(`[*] Batch size: ${this.config.batchSize}, Interval: ${this.config.batchInterval/1e3} seconds`),setTimeout(()=>{this.processCollectionBatch()},1e3))}async processCollectionBatch(){if(this.phaseTracker.currentPhase!==2)return;if(this.isProcessingBatch){console.log("[*] PHASE 2: Batch processing already in progress, skipping...");return}this.isProcessingBatch=!0,this.batchNumber++,this.stats.batchNumber=this.batchNumber,this.stats.isProcessingBatch=!0;let e=Date.now(),n=this.prepareBatchInstances();if(n.length===0){this.handleEmptyBatch();return}console.log(`[+] PHASE 2: Starting Batch #${this.batchNumber} - Processing ${n.length} instances`),console.log(`[*] Batch composition: ${n.filter(a=>a.source==="discovered").length} discovered, ${n.filter(a=>a.source==="retry").length} retries`),console.log(`[*] Remaining after this batch: ${this.discoveredInstances.size+this.failedInstances.size-n.length} instances`);let s=await this.processBatchInstances(n),o=Date.now()-e,i={batchNumber:this.batchNumber,totalProcessed:n.length,successCount:s.filter(a=>a.success).length,failureCount:s.filter(a=>!a.success).length,processingTime:o,results:s};console.log(`[+] Batch #${this.batchNumber} COMPLETED - Successes: ${i.successCount}, Failures: ${i.failureCount}`),console.log(`[*] Status - Processed: ${this.processedInstances.size}, Pending: ${this.discoveredInstances.size}, Failed: ${this.failedInstances.size}`),this.onBatchCompleteCallback&&this.onBatchCompleteCallback(i),this.isProcessingBatch=!1,this.stats.isProcessingBatch=!1}prepareBatchInstances(){let e=[],n=Array.from(this.discoveredInstances.entries());for(let i=0;i<Math.min(this.config.batchSize,n.length);i++){let[a,l]=n[i];e.push({id:a,data:l,source:"discovered"}),this.discoveredInstances.delete(a)}let s=Date.now(),r=Array.from(this.failedInstances.entries()),o=this.config.batchSize-e.length;for(let i=0;i<Math.min(o,r.length);i++){let[a,l]=r[i];l.lastFailTime&&s-l.lastFailTime>1e4&&l.retryCount<this.config.retryLimit&&(e.push({id:a,data:l,source:"retry"}),this.failedInstances.delete(a))}return e}handleEmptyBatch(){this.discoveredInstances.size+this.failedInstances.size===0?(console.log("[+] ===== PHASE 2: BATCH COLLECTION COMPLETED ====="),console.log("[+] All discovered instances have been processed!"),console.log("[+] Final Statistics:"),console.log(`    - Total processed: ${this.processedInstances.size}`),console.log(`    - Total failed (abandoned): ${Array.from(this.failedInstances.values()).filter(n=>n.retryCount>=this.config.retryLimit).length}`),console.log(`    - Total batches: ${this.batchNumber}`)):(console.log(`[*] PHASE 2: Batch #${this.batchNumber} - No instances ready to process`),console.log(`[*] Remaining: ${this.discoveredInstances.size} discovered, ${this.failedInstances.size} failed`)),this.isProcessingBatch=!1,this.stats.isProcessingBatch=!1}async processBatchInstances(e){let n=[];for(let s=0;s<e.length;s++){let r=e[s],o=await this.processInstance(r,s+1,e.length);n.push(o),s<e.length-1&&await new Promise(i=>setTimeout(i,200))}return n}async processInstance(e,n,s){let r=Date.now(),o=e.id,a=e.data.ptr;console.log(`[*] Batch #${this.batchNumber} [${n}/${s}] Processing: ${o}`);let l={success:!1,instanceId:o,processingTime:0,cleanupSet:!1};try{if(!a||a.isNull()||a.equals(ptr(0)))return l.errorMessage="Invalid pointer",l;if(!await this.checkCanCollect(a))return l.errorMessage="Instance no longer collectible",l.success=!0,l;let g=await this.setCleanupFlag(a);l.cleanupSet=g,await this.executeFinishCollect(a)?(l.success=!0,this.processedInstances.add(o),this.stats.processedInstances=this.processedInstances.size,console.log(`[+] Batch #${this.batchNumber} [${n}/${s}] SUCCESS: ${o}`),this.onInstanceProcessedCallback&&this.onInstanceProcessedCallback(l)):(l.errorMessage="FinishCollect failed",this.handleFailedInstance(e))}catch(d){let g=d instanceof Error?d.message:String(d);l.errorMessage=g,console.log(`[-] Batch #${this.batchNumber} [${n}/${s}] FAILED: ${o} - ${g}`),this.handleFailedInstance(e)}return l.processingTime=Date.now()-r,l}handleFailedInstance(e){let n=e.data.retryCount+1;if(n<this.config.retryLimit){let s={...e.data,retryCount:n,lastFailTime:Date.now()};this.failedInstances.set(e.id,s),this.stats.failedInstances=this.failedInstances.size,console.log(`[*] Added ${e.id} to retry queue (attempt ${n}/${this.config.retryLimit})`)}else console.log(`[-] Instance ${e.id} exceeded retry limit, abandoning`)}async checkCanCollect(e){try{let n=this.methods.CanCollect?.handle||this.addresses.CanCollect;return!n||n.isNull()?!1:new NativeFunction(n,"int",["pointer"])(e)===1}catch{return!1}}async setCleanupFlag(e){try{let n=this.methods.Config?.handle||this.addresses.Config;if(!n||n.isNull())return!1;let r=new NativeFunction(n,"pointer",["pointer"])(e);return r&&!r.isNull()&&!r.equals(ptr(0))?(r.add(48).writeU8(1),!0):!1}catch{return!1}}async executeFinishCollect(e){try{let n=this.methods.FinishCollect?.handle||this.addresses.FinishCollect;return!n||n.isNull()?!1:(new NativeFunction(n,"void",["pointer"])(e),!0)}catch{return!1}}setupBatchProcessingTimer(){this.batchProcessingInterval=setInterval(()=>{this.phaseTracker.currentPhase===2&&this.processCollectionBatch()},this.config.batchInterval)}setupStatusMonitoring(){this.statusMonitoringInterval=setInterval(()=>{this.printBatchStatus()},3e4)}setupCleanupTimer(){this.cleanupInterval=setInterval(()=>{this.cleanupProcessedInstances()},3e5)}printBatchStatus(){console.log("=== BATCH PROCESSING STATUS ==="),console.log(`Processing Mode: ${this.config.batchSize===1?"REAL-TIME":"BATCH"}`),console.log(`Current Phase: ${this.phaseTracker.currentPhase===1?"DISCOVERY":"COLLECTION"}`),console.log("Progress:"),console.log(`  - Successfully processed: ${this.processedInstances.size}`),console.log(`  - Discovered instances: ${this.discoveredInstances.size}`),console.log(`  - Failed instances: ${this.failedInstances.size}`),console.log(`  - Total scans performed: ${this.stats.totalDiscoveryScans}`),console.log(`  - Batches completed: ${this.batchNumber}`),this.failedInstances.size>0&&(console.log("Failed instances details:"),this.failedInstances.forEach((e,n)=>{console.log(`  ${n} - Retries: ${e.retryCount}/${this.config.retryLimit}`)})),this.processedInstances.size>=this.config.maxWaitTime&&console.log("[+] TARGET ACHIEVED: Processing target reached!"),console.log("====================================")}cleanupProcessedInstances(){if(this.processedInstances.size>1e3){console.log("[*] Cleaning up old processed instances...");let e=Array.from(this.processedInstances);this.processedInstances.clear();for(let n=e.length-500;n<e.length;n++)n>=0&&this.processedInstances.add(e[n]);console.log(`[+] Cleaned up processed instances, kept ${this.processedInstances.size}`)}}onBatchComplete(e){this.onBatchCompleteCallback=e}onPhaseChange(e){this.onPhaseChangeCallback=e}onInstanceProcessed(e){this.onInstanceProcessedCallback=e}getStats(){return{...this.stats}}getPhaseInfo(){return{...this.phaseTracker}}getQueueSizes(){return{discovered:this.discoveredInstances.size,processed:this.processedInstances.size,failed:this.failedInstances.size}}clearAllQueues(){this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.stats.discoveredInstances=0,this.stats.processedInstances=0,this.stats.failedInstances=0,console.log("[+] All processing queues cleared")}};var N=class{config;defaultConfig;constructor(e){this.defaultConfig=this.createDefaultConfig(),this.config=this.mergeConfigs(this.defaultConfig,e||{})}createDefaultConfig(){return{batch:{batchSize:1,batchInterval:3e4,retryLimit:3,discoveryTimeout:1e4,maxWaitTime:6e4},collection:{realTimeEnabled:!0,batchEnabled:!0,cleanupEnabled:!0,maxCollections:1e3},logging:{debugLevel:"normal",logBatchDetails:!0,logErrorDetails:!0},errorHandling:{maxRetries:3,retryDelay:1e3,ignoreAntiDebugErrors:!0,logErrorDetails:!0,continueOnError:!0},module:{name:"libil2cpp.so",maxWaitTime:3e4,checkInterval:500},entityController:{maxRetries:5,retryDelay:1e3,upgradeTimeout:1e4,batchSize:10,batchDelay:2e3,enableLogging:!0,logLevel:"normal"}}}mergeConfigs(e,n){return{batch:{...e.batch,...n.batch},collection:{...e.collection,...n.collection},logging:{...e.logging,...n.logging},errorHandling:{...e.errorHandling,...n.errorHandling},module:{...e.module,...n.module},entityController:{...e.entityController,...n.entityController||{}}}}getConfig(){return{...this.config}}getBatchConfig(){return{...this.config.batch}}getCollectionConfig(){return{...this.config.collection}}getLoggingConfig(){return{...this.config.logging}}getModuleConfig(){return{...this.config.module}}getEntityControllerConfig(){return{...this.config.entityController}}getErrorHandlingConfig(){return{...this.config.errorHandling}}updateBatchConfig(e){this.config.batch={...this.config.batch,...e},console.log("[*] Batch configuration updated:",e)}updateCollectionConfig(e){this.config.collection={...this.config.collection,...e},console.log("[*] Collection configuration updated:",e)}updateLoggingConfig(e){this.config.logging={...this.config.logging,...e},console.log("[*] Logging configuration updated:",e)}updateModuleConfig(e){this.config.module={...this.config.module,...e},console.log("[*] Module configuration updated:",e)}updateEntityControllerConfig(e){this.config.entityController&&(this.config.entityController={...this.config.entityController,...e},console.log("[*] EntityController configuration updated:",e))}updateErrorHandlingConfig(e){this.config.errorHandling={...this.config.errorHandling,...e},console.log("[*] Error handling configuration updated:",e)}resetToDefaults(){this.config={...this.defaultConfig},console.log("[+] Configuration reset to defaults")}validateConfig(){let e=[];return this.config.batch.batchSize<1&&e.push("Batch size must be at least 1"),this.config.batch.batchInterval<1e3&&e.push("Batch interval must be at least 1000ms"),this.config.batch.retryLimit<0&&e.push("Retry limit cannot be negative"),this.config.batch.discoveryTimeout<1e3&&e.push("Discovery timeout must be at least 1000ms"),this.config.collection.maxCollections<1&&e.push("Max collections must be at least 1"),this.config.errorHandling.maxRetries<0&&e.push("Max retries cannot be negative"),this.config.errorHandling.retryDelay<0&&e.push("Retry delay cannot be negative"),{valid:e.length===0,errors:e}}getConfigSummary(){let e=this.config;return["=== CONFIGURATION SUMMARY ===","Batch Processing:",`  - Batch Size: ${e.batch.batchSize} (${e.batch.batchSize===1?"Real-time":"Batch"} mode)`,`  - Batch Interval: ${e.batch.batchInterval/1e3}s`,`  - Retry Limit: ${e.batch.retryLimit}`,`  - Discovery Timeout: ${e.batch.discoveryTimeout/1e3}s`,"Collection:",`  - Real-time: ${e.collection.realTimeEnabled?"ENABLED":"DISABLED"}`,`  - Batch: ${e.collection.batchEnabled?"ENABLED":"DISABLED"}`,`  - Cleanup: ${e.collection.cleanupEnabled?"ENABLED":"DISABLED"}`,`  - Max Collections: ${e.collection.maxCollections}`,"Logging:",`  - Debug Level: ${e.logging.debugLevel}`,`  - Batch Details: ${e.logging.logBatchDetails?"ON":"OFF"}`,`  - Error Details: ${e.logging.logErrorDetails?"ON":"OFF"}`,"Error Handling:",`  - Max Retries: ${e.errorHandling.maxRetries}`,`  - Retry Delay: ${e.errorHandling.retryDelay}ms`,`  - Ignore Anti-Debug: ${e.errorHandling.ignoreAntiDebugErrors?"ON":"OFF"}`,`  - Continue on Error: ${e.errorHandling.continueOnError?"ON":"OFF"}`,"Module:",`  - Name: ${e.module.name}`,`  - Max Wait Time: ${e.module.maxWaitTime/1e3}s`,`  - Check Interval: ${e.module.checkInterval}ms`,"=============================="].join(`
`)}};var C=class{automationStats;phaseTracker;batchStats;discoveredInstances=new Map;processedInstances=new Set;failedInstances=new Map;errorTrackers=new Map;constructor(){this.automationStats=this.createInitialStats(),this.phaseTracker=this.createInitialPhaseTracker(),this.batchStats=this.createInitialBatchStats()}createInitialStats(){return{totalInstances:0,validInstances:0,invalidInstances:0,upgradeableInstances:0,collectionsPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()}}createInitialPhaseTracker(){return{currentPhase:1,phaseStartTime:Date.now(),phaseEndTime:0,phaseDuration:0,discoveryComplete:!1}}createInitialBatchStats(){return{currentPhase:1,batchNumber:0,isProcessingBatch:!1,discoveredInstances:0,processedInstances:0,failedInstances:0,totalDiscoveryScans:0,lastDiscoveryTime:Date.now(),discoveryCompleteTime:0}}getAutomationStats(){return{...this.automationStats}}recordCollection(){this.automationStats.collectionsPerformed++,this.automationStats.lastUpdateTime=Date.now()}recordDiscoveryScan(){this.batchStats.totalDiscoveryScans++,this.batchStats.lastDiscoveryTime=Date.now(),this.automationStats.lastUpdateTime=Date.now()}updateInstanceCounts(e,n,s,r){this.automationStats.totalInstances=e,this.automationStats.validInstances=n,this.automationStats.invalidInstances=s,this.automationStats.upgradeableInstances=r,this.automationStats.lastUpdateTime=Date.now()}resetAutomationStats(){this.automationStats=this.createInitialStats(),console.log("[+] Automation statistics reset")}getPhaseTracker(){return{...this.phaseTracker}}startDiscoveryPhase(){this.phaseTracker.currentPhase=1,this.phaseTracker.phaseStartTime=Date.now(),this.phaseTracker.discoveryComplete=!1,this.batchStats.currentPhase=1,console.log("[+] Discovery phase started")}completeDiscoveryPhase(){this.phaseTracker.currentPhase=2,this.phaseTracker.phaseEndTime=Date.now(),this.phaseTracker.phaseDuration=this.phaseTracker.phaseEndTime-this.phaseTracker.phaseStartTime,this.phaseTracker.discoveryComplete=!0,this.batchStats.currentPhase=2,this.batchStats.discoveryCompleteTime=this.phaseTracker.phaseEndTime,console.log(`[+] Discovery phase completed in ${(this.phaseTracker.phaseDuration/1e3).toFixed(1)}s`)}resetToDiscoveryPhase(){this.phaseTracker=this.createInitialPhaseTracker(),this.batchStats=this.createInitialBatchStats(),this.clearAllInstances(),console.log("[+] Reset to discovery phase")}getBatchStats(){return this.batchStats.discoveredInstances=this.discoveredInstances.size,this.batchStats.processedInstances=this.processedInstances.size,this.batchStats.failedInstances=this.failedInstances.size,{...this.batchStats}}startBatchProcessing(e){this.batchStats.batchNumber=e,this.batchStats.isProcessingBatch=!0,console.log(`[*] Started batch processing #${e}`)}completeBatchProcessing(){this.batchStats.isProcessingBatch=!1,console.log(`[+] Completed batch processing #${this.batchStats.batchNumber}`)}addDiscoveredInstance(e,n){if(!this.discoveredInstances.has(e)&&!this.processedInstances.has(e)){let s={ptr:n,discoveryTime:Date.now(),retryCount:0,entityId:e};this.discoveredInstances.set(e,s)}}markInstanceProcessed(e){this.processedInstances.add(e),this.discoveredInstances.delete(e),this.failedInstances.delete(e)}markInstanceFailed(e,n,s){let r=this.discoveredInstances.get(e)||this.failedInstances.get(e),o=r?r.retryCount+1:1,i={ptr:n,discoveryTime:r?.discoveryTime||Date.now(),retryCount:o,entityId:e,lastFailTime:Date.now()};this.failedInstances.set(e,i),this.discoveredInstances.delete(e)}getDiscoveredInstances(){return new Map(this.discoveredInstances)}getProcessedInstances(){return new Set(this.processedInstances)}getFailedInstances(){return new Map(this.failedInstances)}clearAllInstances(){this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),console.log("[+] All instance tracking cleared")}clearProcessedInstances(){this.processedInstances.clear(),console.log("[+] Processed instances cleared")}clearFailedInstances(){this.failedInstances.clear(),console.log("[+] Failed instances cleared")}recordError(e,n,s){let r=`${e}_${Date.now()}`,o={errorType:"unknown",errorMessage:n instanceof Error?n.message:String(n),occurredAt:Date.now(),instanceId:s?.toString(),methodName:e,retryCount:1};if(this.errorTrackers.set(r,o),this.errorTrackers.size>100){let i=Array.from(this.errorTrackers.keys())[0];this.errorTrackers.delete(i)}}getErrorStats(){let e=Array.from(this.errorTrackers.values()).filter(n=>Date.now()-n.occurredAt<3e5).sort((n,s)=>s.occurredAt-n.occurredAt);return{totalErrors:this.errorTrackers.size,recentErrors:e}}clearErrorTracking(){this.errorTrackers.clear(),console.log("[+] Error tracking cleared")}getStateSummary(){let e=this.getAutomationStats(),n=this.getPhaseTracker(),s=this.getBatchStats(),r=this.getErrorStats(),o=Date.now()-e.startTime;return["=== AUTOMATION STATE SUMMARY ===",`Uptime: ${`${Math.floor(o/6e4)}m ${Math.floor(o%6e4/1e3)}s`}`,`Current Phase: ${n.currentPhase} (${n.currentPhase===1?"Discovery":"Collection"})`,`Collections Performed: ${e.collectionsPerformed}`,`Total Instances: ${e.totalInstances}`,`Valid Instances: ${e.validInstances}`,`Invalid Instances: ${e.invalidInstances}`,"","Instance Counts:",`  - Discovered: ${this.discoveredInstances.size}`,`  - Processed: ${this.processedInstances.size}`,`  - Failed: ${this.failedInstances.size}`,"","Batch Processing:",`  - Current Batch: #${s.batchNumber}`,`  - Processing: ${s.isProcessingBatch?"YES":"NO"}`,"","Errors:",`  - Total: ${r.totalErrors}`,`  - Recent (5min): ${r.recentErrors.length}`,"================================="].join(`
`)}};var G=class{assemblyImage=null;goodyHutHelperClass=null;methods={CanCollect:null,FinishCollect:null,Config:null,SellRuins:null,Update:null,GetRewardType:null,GetRewardAmount:null,GetCollectTime:null};moduleInfo={module:null,name:"",base:ptr(0),size:0,isValid:!1,loadTime:0};methodAddresses={CanCollect:ptr(0),FinishCollect:ptr(0),Update:ptr(0),Config:ptr(0),SellRuins:ptr(0),GetRewardType:ptr(0),GetRewardAmount:ptr(0)};config;stats={totalInstances:0,validInstances:0,invalidInstances:0,upgradeableInstances:0,collectionsPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()};batchStats={totalDiscoveryScans:0,discoveredInstances:0,processedInstances:0,failedInstances:0,batchNumber:0,isProcessingBatch:!1,currentPhase:1,lastDiscoveryTime:0,discoveryCompleteTime:0};phaseTracker={currentPhase:1,phaseStartTime:Date.now(),discoveryComplete:!1};discoveredInstances=new Map;processedInstances=new Set;failedInstances=new Map;errorTrackers=new Map;hookManager=null;batchProcessor=null;configManager;stateManager;moduleCheckInterval=null;batchProcessingInterval=null;statusMonitoringInterval=null;cleanupInterval=null;constructor(e){console.log("\u{1F680} Starting GoodyHutHelper TypeScript Implementation..."),this.configManager=new N(e),this.stateManager=new C,this.config=this.configManager.getConfig(),console.log("\u2705 GoodyHutHelper instance created with configuration:"),console.log(`   - Batch size: ${this.config.batch.batchSize}`),console.log(`   - Real-time collection: ${this.config.collection.realTimeEnabled}`),console.log(`   - Debug level: ${this.config.logging.debugLevel}`)}createDefaultConfig(){return{batch:{batchSize:1,batchInterval:3e4,retryLimit:3,discoveryTimeout:1e4,maxWaitTime:6e4},errorHandling:{maxRetries:3,retryDelay:1e3,ignoreAntiDebugErrors:!0,logErrorDetails:!0,continueOnError:!0},module:{name:"libil2cpp.so",maxWaitTime:6e4,checkInterval:500},collection:{realTimeEnabled:!0,batchEnabled:!0,cleanupEnabled:!0,maxCollections:1e3},logging:{debugLevel:"normal",logBatchDetails:!0,logErrorDetails:!0}}}async initialize(){try{return console.log("\u{1F50D} Initializing GoodyHutHelper automation system..."),await this.detectIL2CPPModule()?(console.log("\u{1F517} IL2CPP bridge ready..."),await this.findGoodyHutHelperClass()?(this.setupMethodReferences(),this.calculateMethodAddresses(),this.setupMethodHooks(),this.initializeBatchProcessing(),this.setupRPCExports(),console.log("\u2705 GoodyHutHelper initialization complete!"),{success:!0,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses}):{success:!1,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses,errorMessage:"Failed to find GoodyHutHelper class"}):{success:!1,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses,errorMessage:"Failed to detect IL2CPP module"}}catch(e){let n=e instanceof Error?e.message:String(e);return console.log(`\u274C Initialization failed: ${n}`),{success:!1,module:this.moduleInfo,methods:this.methods,addresses:this.methodAddresses,errorMessage:n}}}async detectIL2CPPModule(){return new Promise(e=>{console.log("[*] Waiting for libil2cpp.so to load...");let n=Date.now(),s=!1,r=()=>{if(Process.enumerateModules().forEach(i=>{if(i.name===this.config.module.name){this.moduleInfo={module:i,name:i.name,base:i.base,size:i.size,isValid:!0,loadTime:Date.now()},s=!0,console.log(`[+] Found ${i.name} at: ${i.base} (size: ${(i.size/1024/1024).toFixed(1)}MB)`);return}}),s){this.moduleCheckInterval&&(clearInterval(this.moduleCheckInterval),this.moduleCheckInterval=null),console.log("[+] IL2CPP module loaded successfully!"),e(!0);return}let o=Date.now()-n;if(o>this.config.module.maxWaitTime){this.moduleCheckInterval&&(clearInterval(this.moduleCheckInterval),this.moduleCheckInterval=null),console.log(`[-] Timeout waiting for ${this.config.module.name} to load after ${this.config.module.maxWaitTime/1e3} seconds`),this.listAvailableModules(),e(!1);return}o%5e3<this.config.module.checkInterval&&console.log(`[*] Still waiting for ${this.config.module.name}... (${(o/1e3).toFixed(1)}s elapsed)`)};r(),s||(this.moduleCheckInterval=setInterval(r,this.config.module.checkInterval))})}async findGoodyHutHelperClass(){try{return this.assemblyImage=Il2Cpp.domain.assembly("Assembly-CSharp").image,this.assemblyImage?(console.log("\u2705 Assembly-CSharp found"),this.goodyHutHelperClass=this.assemblyImage.class("GoodyHutHelper"),this.goodyHutHelperClass?(console.log("\u2705 GoodyHutHelper class found"),console.log(`\u{1F4CB} Class info: ${this.goodyHutHelperClass.name}`),!0):(console.log("\u274C GoodyHutHelper class not found"),this.listAvailableClasses(),!1)):(console.log("\u274C Failed to get Assembly-CSharp image"),this.listAvailableAssemblies(),!1)}catch(e){return console.log(`\u274C Failed to find GoodyHutHelper class: ${e}`),!1}}setupMethodReferences(){try{if(console.log("\u{1F527} Setting up method references..."),!this.goodyHutHelperClass){console.log("\u274C GoodyHutHelper class not available for method setup");return}this.methods.CanCollect=this.goodyHutHelperClass.method("CanCollect"),this.methods.FinishCollect=this.goodyHutHelperClass.method("FinishCollect"),this.methods.Config=this.goodyHutHelperClass.method("Config"),this.methods.Update=this.goodyHutHelperClass.method("Update");try{this.methods.SellRuins=this.goodyHutHelperClass.method("SellRuins"),this.methods.GetRewardType=this.goodyHutHelperClass.method("GetRewardType"),this.methods.GetRewardAmount=this.goodyHutHelperClass.method("GetRewardAmount"),this.methods.GetCollectTime=this.goodyHutHelperClass.method("GetCollectTime")}catch(e){console.log(`\u26A0\uFE0F Some optional methods not found: ${e}`)}Object.entries(this.methods).forEach(([e,n])=>{console.log(n?`\u2705 Found method: ${e}`:`\u26A0\uFE0F Method not found: ${e}`)})}catch(e){console.log(`\u274C Method setup failed: ${e}`)}}calculateMethodAddresses(){if(!this.moduleInfo.isValid||!this.moduleInfo.base){console.log("\u274C Cannot calculate method addresses - invalid module info");return}console.log("\u{1F9EE} Calculating method addresses...");let e={CanCollect:34136164,FinishCollect:34129736,Update:34135404,Config:34128752,SellRuins:34139196,GetRewardType:34134600,GetRewardAmount:34138044};this.methodAddresses.CanCollect=this.moduleInfo.base.add(e.CanCollect),this.methodAddresses.FinishCollect=this.moduleInfo.base.add(e.FinishCollect),this.methodAddresses.Update=this.moduleInfo.base.add(e.Update),this.methodAddresses.Config=this.moduleInfo.base.add(e.Config),this.methodAddresses.SellRuins=this.moduleInfo.base.add(e.SellRuins),this.methodAddresses.GetRewardType=this.moduleInfo.base.add(e.GetRewardType),this.methodAddresses.GetRewardAmount=this.moduleInfo.base.add(e.GetRewardAmount),console.log("\u{1F4CD} Method addresses calculated:"),Object.entries(this.methodAddresses).forEach(([n,s])=>{console.log(`   ${n}: ${s}`)})}listAvailableModules(){console.log("[-] Available modules:"),Process.enumerateModules().forEach(e=>{e.size>5e6&&console.log(`    ${e.name} (${(e.size/1024/1024).toFixed(1)}MB)`)}),console.log("[-] Use setModule('module_name.so') to manually set the correct module")}listAvailableAssemblies(){try{console.log("\u{1F4A1} Available assemblies:"),Il2Cpp.domain.assemblies.slice(0,10).forEach((n,s)=>{console.log(`   ${s}: ${n.name}`)})}catch{console.log("   Could not enumerate assemblies")}}listAvailableClasses(){if(this.assemblyImage)try{console.log("\u{1F4A1} Available classes (first 10):");let e=this.assemblyImage.classes;for(let s=0;s<Math.min(10,e.length);s++)console.log(`   ${s}: ${e[s].name}`);let n=e.filter(s=>s.name.toLowerCase().includes("goody")||s.name.toLowerCase().includes("hut")||s.name.toLowerCase().includes("collect"));n.length>0&&(console.log("\u{1F50D} Found goody/hut/collect related classes:"),n.forEach((s,r)=>{console.log(`   ${r}: ${s.name}`)}))}catch{console.log("   Could not enumerate classes")}}setupMethodHooks(){console.log("\u{1F3A3} Setting up method hooks...");try{this.hookManager=new M(this.methods,this.methodAddresses,this.moduleInfo),this.hookManager.setupAllHooks(),this.hookManager.onCanCollect((e,n)=>{n&&this.handleCanCollectEvent(e)}),this.hookManager.onFinishCollect((e,n)=>{n&&this.handleFinishCollectEvent(e)}),this.hookManager.onUpdate(e=>{this.handleUpdateEvent(e)}),console.log("\u2705 Method hooks setup complete with callbacks")}catch(e){console.log(`\u274C Failed to setup method hooks: ${e}`)}}handleCanCollectEvent(e){let n=e.toString();console.log(`\u{1F3AF} CanCollect event for instance: ${n}`),this.stats.totalInstances++,this.stats.lastUpdateTime=Date.now()}handleFinishCollectEvent(e){let n=e.toString();console.log(`\u2705 FinishCollect event for instance: ${n}`),this.stats.collectionsPerformed++,this.processedInstances.add(n),this.stats.lastUpdateTime=Date.now()}handleUpdateEvent(e){let n=e.toString();if(this.batchStats.totalDiscoveryScans++,this.batchProcessor)this.batchProcessor.addDiscoveredInstance(e,n);else if(!this.processedInstances.has(n)&&!this.discoveredInstances.has(n)){let s={ptr:e,discoveryTime:Date.now(),retryCount:0,entityId:n};this.discoveredInstances.set(n,s),this.batchStats.discoveredInstances=this.discoveredInstances.size}}initializeBatchProcessing(){console.log("\u{1F4E6} Initializing batch processing system...");try{this.batchProcessor=new D(this.config.batch,this.methods,this.methodAddresses),this.batchProcessor.onBatchComplete(e=>{console.log(`\u2705 Batch ${e.batchNumber} completed: ${e.successCount}/${e.totalProcessed} successful`)}),this.batchProcessor.onPhaseChange(e=>{console.log(`\u{1F504} Phase changed to: ${e===1?"DISCOVERY":"COLLECTION"}`),this.phaseTracker.currentPhase=e}),this.batchProcessor.onInstanceProcessed(e=>{e.success&&this.stats.collectionsPerformed++}),this.batchProcessor.startBatchProcessing(),console.log("\u2705 Batch processing system initialized and started")}catch(e){console.log(`\u274C Failed to initialize batch processing: ${e}`)}}setupRPCExports(){console.log("\u{1F50C} Setting up RPC exports..."),console.log("\u26A0\uFE0F RPC exports setup - placeholder implementation")}getStats(){let e=this.stateManager.getAutomationStats(),n=this.stateManager.getBatchStats();return{...e,...n}}getConfig(){return this.configManager.getConfig()}getBatchStatus(){this.batchProcessor?this.batchProcessor.printBatchStatus():(console.log("=== BATCH PROCESSING STATUS ==="),console.log("Batch processor not initialized"),console.log(`Discovered instances: ${this.discoveredInstances.size}`),console.log(`Processed instances: ${this.processedInstances.size}`),console.log(`Total scans performed: ${this.batchStats.totalDiscoveryScans}`),console.log("===================================="))}clearProcessedInstances(){this.processedInstances.clear(),this.stats.collectionsPerformed=0,this.batchProcessor&&this.batchProcessor.clearAllQueues(),console.log("[+] Cleared all processed instances")}clearFailedInstances(){this.failedInstances.clear(),this.batchStats.failedInstances=0,console.log("[+] Cleared all failed instances")}forceBatchProcess(){this.batchProcessor?this.batchProcessor.getPhaseInfo().currentPhase===2?(console.log("[*] Forcing immediate batch processing..."),this.batchProcessor.processCollectionBatch()):console.log("[*] Cannot force batch processing - still in discovery phase"):console.log("[*] Batch processor not initialized")}forceCompleteDiscovery(){this.batchProcessor?this.batchProcessor.getPhaseInfo().currentPhase===1?(console.log("[*] Forcing discovery phase completion..."),this.batchProcessor.completeDiscoveryPhase()):console.log("[*] Discovery phase already completed"):console.log("[*] Batch processor not initialized")}resetToDiscovery(){console.log("[*] Resetting to discovery phase..."),this.phaseTracker.currentPhase=1,this.phaseTracker.discoveryComplete=!1,this.phaseTracker.phaseStartTime=Date.now(),this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.batchStats={currentPhase:1,batchNumber:0,isProcessingBatch:!1,discoveredInstances:0,processedInstances:0,failedInstances:0,totalDiscoveryScans:0,lastDiscoveryTime:Date.now(),discoveryCompleteTime:0},this.stats.collectionsPerformed=0,this.batchProcessor&&this.batchProcessor.resetToDiscovery(),console.log("[+] Reset complete - discovery phase restarted")}setModule(e){console.log(`[*] Attempting to manually set IL2CPP module to: ${e}`);let n=Process.enumerateModules().find(s=>s.name===e);return n?(this.moduleInfo={name:n.name,base:n.base,size:n.size,module:n,isValid:!0,loadTime:Date.now()},console.log(`[+] Successfully set IL2CPP module to: ${this.moduleInfo.name}`),console.log(`[+] Module base: ${this.moduleInfo.base}`),console.log("[+] Reinitializing hooks with new module..."),this.cleanup(),setTimeout(()=>{this.initialize()},1e3),!0):(console.log(`[-] Module '${e}' not found`),console.log("[-] Available modules:"),Process.enumerateModules().forEach(s=>{s.size>1e6&&console.log(`    ${s.name} (${(s.size/1024/1024).toFixed(1)}MB)`)}),!1)}listLargeModules(){console.log("[*] Large modules (>1MB) in process:");let e=[];return Process.enumerateModules().forEach(n=>{n.size>1e6&&e.push({name:n.name,size:n.size,sizeMB:(n.size/1024/1024).toFixed(1),base:n.base.toString()})}),e.sort((n,s)=>s.size-n.size),e.forEach((n,s)=>{console.log(`  ${s+1}. ${n.name} (${n.sizeMB}MB) at ${n.base}`)}),e}getDetailedStats(){let e=this.batchProcessor?.getStats()||this.batchStats;return{phase:(this.batchProcessor?.getPhaseInfo()||this.phaseTracker).currentPhase,discovered:this.discoveredInstances.size,processed:this.processedInstances.size,failed:this.failedInstances.size,batchNumber:e.batchNumber,isProcessing:e.isProcessingBatch,totalScans:e.totalDiscoveryScans,lastDiscovery:e.lastDiscoveryTime,discoveryComplete:e.discoveryCompleteTime}}updateConfig(e){this.config={...this.config,...e},console.log("\u2705 Configuration updated")}cleanup(){console.log("\u{1F9F9} Cleaning up GoodyHutHelper resources..."),this.batchProcessor&&(this.batchProcessor.stopBatchProcessing(),this.batchProcessor=null),this.hookManager&&(this.hookManager.removeAllHooks(),this.hookManager=null),this.moduleCheckInterval&&(clearInterval(this.moduleCheckInterval),this.moduleCheckInterval=null),this.batchProcessingInterval&&(clearInterval(this.batchProcessingInterval),this.batchProcessingInterval=null),this.statusMonitoringInterval&&(clearInterval(this.statusMonitoringInterval),this.statusMonitoringInterval=null),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.discoveredInstances.clear(),this.processedInstances.clear(),this.failedInstances.clear(),this.errorTrackers.clear(),console.log("\u2705 GoodyHutHelper cleanup complete")}};var U=class{assemblyImage=null;entityControllerClass=null;methods;antiDebugProtection;config;stats;upgradeTrackers=new Map;isInitialized=!1;constructor(e){this.config={maxRetries:5,retryDelay:1e3,upgradeTimeout:1e4,batchSize:10,batchDelay:2e3,enableLogging:!0,logLevel:"normal",...e},this.methods={IsSelected:null,CanUpgrade:null,GetLevel:null,GetMaxLevel:null,GetMaxUpgradeLevel:null,InstantUpgrade:null,Select:null,Unselect:null,GetUniqueId:null,IsUpgrading:null,GetUpgradeTime:null,GetUpgradeCost:null},this.stats={totalInstances:0,selectedInstances:0,upgradeableInstances:0,upgradesPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()},this.antiDebugProtection=new E({maxRetries:this.config.maxRetries,retryDelay:this.config.retryDelay,ignoreKnownErrors:!0,logErrorDetails:this.config.enableLogging}),this.log("\u{1F680} EntityController Manager initialized")}async initialize(){try{return this.log("\u{1F50D} Initializing EntityController manager..."),await Il2Cpp.perform(()=>{}),this.assemblyImage=Il2Cpp.domain.assembly("Assembly-CSharp").image,this.assemblyImage?(this.entityControllerClass=this.assemblyImage.class("EntityController"),this.entityControllerClass?(this.log("\u2705 EntityController class found"),await this.setupMethods(),this.isInitialized=!0,this.log("\u2705 EntityController manager initialized successfully"),!0):(this.log("\u274C EntityController class not found"),!1)):(this.log("\u274C Assembly-CSharp not found"),!1)}catch(e){return this.log(`\u274C Initialization failed: ${e}`),!1}}async setupMethods(){await this.antiDebugProtection.executeWithProtection("setup_methods",()=>{this.log("\u{1F527} Setting up method references..."),this.methods.IsSelected=this.entityControllerClass.method("IsSelected"),this.methods.CanUpgrade=this.entityControllerClass.method("CanUpgrade"),this.methods.GetLevel=this.entityControllerClass.method("GetLevel"),this.methods.GetMaxLevel=this.entityControllerClass.method("GetMaxLevel"),this.methods.GetMaxUpgradeLevel=this.entityControllerClass.method("GetMaxUpgradeLevel"),this.methods.InstantUpgrade=this.entityControllerClass.method("InstantUpgrade");try{this.methods.Select=this.entityControllerClass.method("Select"),this.methods.Unselect=this.entityControllerClass.method("Unselect"),this.methods.GetUniqueId=this.entityControllerClass.method("get_uniqueId"),this.methods.IsUpgrading=this.entityControllerClass.method("IsUpgrading"),this.methods.GetUpgradeTime=this.entityControllerClass.method("GetUpgradeTime"),this.methods.GetUpgradeCost=this.entityControllerClass.method("GetUpgradeCost")}catch(n){this.log(`\u26A0\uFE0F Some optional methods not found: ${n}`)}this.log("\u2705 Method references setup complete")},"EntityController method setup")}getAllInstances(){if(!this.isInitialized||!this.entityControllerClass)return this.log("\u274C EntityController manager not initialized"),[];try{let e=Il2Cpp.gc.choose(this.entityControllerClass);return this.stats.totalInstances=e.length,this.stats.lastUpdateTime=Date.now(),this.log(`\u{1F50D} Found ${e.length} EntityController instances`),e}catch(e){return this.log(`\u274C Failed to get instances: ${e}`),[]}}async autoUpgradeSelected(){return this.isInitialized?await this.antiDebugProtection.executeWithProtection("auto_upgrade_selected",async()=>{this.log("\u{1F680} Starting auto-upgrade for selected entities...");let s=this.getAllInstances();if(s.length===0)return this.log("\u26A0\uFE0F No EntityController instances found"),0;let r=0,o=0,i=0;this.log(`\u{1F50D} Filtering valid instances from ${s.length} total...`);let a=[];for(let l=0;l<s.length;l++)this.validateInstance(s[l])&&a.push(s[l]),(l+1)%500===0&&this.log(`\u{1F4CA} Validated ${l+1}/${s.length} instances, found ${a.length} valid`);if(this.log(`\u2705 Found ${a.length} valid instances out of ${s.length} total`),a.length===0)return this.log("\u26A0\uFE0F No valid EntityController instances found"),0;for(let l=0;l<a.length;l+=this.config.batchSize){let d=a.slice(l,l+this.config.batchSize);for(let g=0;g<d.length;g++){let f=d[g],p=l+g;try{if(!this.safeInvokeInstanceMethod(f,"IsSelected",[],`Instance ${p} IsSelected`))continue;o++;let S=this.validateUpgradeState(f);if(!S.canUpgrade){this.log(`\u{1F4CB} Entity cannot upgrade: ${S.reason}`,"verbose");continue}i++;let w=await this.upgradeEntity(f);w.success&&(r+=w.upgradeCount,this.stats.upgradesPerformed+=w.upgradeCount)}catch(y){this.log(`\u274C Error processing entity ${p}: ${y}`)}}l+this.config.batchSize<a.length&&await new Promise(g=>setTimeout(g,this.config.batchDelay))}return this.stats.selectedInstances=o,this.stats.upgradeableInstances=i,this.stats.lastUpdateTime=Date.now(),this.log(`\u2705 Auto-upgrade complete: ${r} upgrades performed`),this.log(`\u{1F4CA} Selected: ${o}, Upgradeable: ${i}`),r},"EntityController auto-upgrade")||0:(this.log("\u274C EntityController manager not initialized"),0)}validateInstance(e){try{if(!e||!e.handle||e.handle.isNull())return!1;let n=e.class;if(!n||n.name!=="EntityController")return!1;try{let s=e.method("IsSelected");if(!s||!s.handle||s.handle.isNull())return!1;let r=e.method("GetLevel");if(!r||!r.handle||r.handle.isNull())return!1}catch{return!1}return!0}catch{return!1}}safeGetMethod(e,n){try{if(!this.validateInstance(e))return null;let s=e.method(n);return!s||!s.handle||s.handle.isNull()?null:s}catch{return null}}safeInvokeMethod(e,n=[],s="Unknown"){try{return e?e.invoke(...n):(this.log(`\u26A0\uFE0F ${s}: Method is null`,"verbose"),null)}catch(r){let o=r instanceof Error?r.message:String(r);return o.includes("access violation")||o.includes("abort was called")||o.includes("breakpoint")?(this.log(`\u26A0\uFE0F ${s}: Anti-debug protection triggered - ${o}`,"verbose"),null):(this.log(`\u274C ${s}: Method invocation failed - ${o}`,"verbose"),null)}}safeInvokeInstanceMethod(e,n,s=[],r="Unknown"){let o=`invoke_${n}_${e.handle.toString()}`;return this.antiDebugProtection.executeWithProtection(o,()=>{let i=this.safeGetMethod(e,n);if(!i)throw new Error(`Method ${n} not accessible on instance`);return this.safeInvokeMethod(i,s,r)},`EntityController.${n}`)}validateUpgradeState(e){try{let n=this.safeInvokeInstanceMethod(e,"CanUpgrade",[!0],"CanUpgrade validation"),s=this.safeInvokeInstanceMethod(e,"IsUpgrading",[],"IsUpgrading validation")||!1,r=this.safeInvokeInstanceMethod(e,"GetLevel",[],"GetLevel validation")||0,o=this.safeInvokeInstanceMethod(e,"GetMaxUpgradeLevel",[],"GetMaxUpgradeLevel validation")||0;return s?{canUpgrade:!1,isUpgrading:!0,currentLevel:r,maxLevel:o,reason:"Already upgrading"}:n?r>=o-1?{canUpgrade:!1,isUpgrading:!1,currentLevel:r,maxLevel:o,reason:"At max level or max-1 (leaving final upgrade for manual)"}:{canUpgrade:!0,isUpgrading:!1,currentLevel:r,maxLevel:o,reason:"Ready for upgrade"}:{canUpgrade:!1,isUpgrading:!1,currentLevel:r,maxLevel:o,reason:"Cannot upgrade (resources/requirements not met)"}}catch(n){return{canUpgrade:!1,isUpgrading:!1,currentLevel:0,maxLevel:0,reason:`Validation error: ${n}`}}}async upgradeEntity(e){let n=this.getEntityId(e),s=this.safeInvokeInstanceMethod(e,"GetLevel",[],`${n} GetLevel initial`)||0,r=0,o=`upgrade_entity_${n}`;return await this.antiDebugProtection.executeWithProtection(o,async()=>{let a=this.safeInvokeInstanceMethod(e,"GetMaxUpgradeLevel",[],`${n} GetMaxUpgradeLevel`)||0,l=s;for(;l<a-1&&this.safeInvokeInstanceMethod(e,"CanUpgrade",[!0],`${n} CanUpgrade check`);){this.safeInvokeInstanceMethod(e,"InstantUpgrade",[],`${n} InstantUpgrade`),r++,await new Promise(f=>setTimeout(f,500));let g=this.safeInvokeInstanceMethod(e,"GetLevel",[],`${n} GetLevel check`)||l;if(g<=l){await new Promise(p=>setTimeout(p,1e3));let f=this.safeInvokeInstanceMethod(e,"GetLevel",[],`${n} GetLevel recheck`)||l;if(f<=l){this.log(`\u26A0\uFE0F Upgrade may have failed, level didn't increase: ${l}`);break}l=f}else l=g;this.log(`\u{1F4C8} Entity ${n}: Level ${l}/${a}`,"verbose")}return{success:r>0,newLevel:l,waitTime:0,upgradeCount:r}},`EntityController upgrade for ${n}`)||{success:!1,newLevel:s,waitTime:0,upgradeCount:0}}getEntityId(e){try{let n=this.safeInvokeInstanceMethod(e,"GetUniqueId",[],"GetUniqueId");return n?n.toString():e.handle.toString()}catch{return e.handle.toString()}}getStats(){return{...this.stats}}getConfig(){return{...this.config}}updateConfig(e){this.config={...this.config,...e},this.log("\u2699\uFE0F Configuration updated")}cleanup(){this.upgradeTrackers.clear(),this.log("\u{1F9F9} EntityController manager cleaned up")}log(e,n="normal"){if(!this.config.enableLogging)return;let s={minimal:0,normal:1,verbose:2},r=s[this.config.logLevel];s[n]<=r&&console.log(`[EntityController] ${e}`)}};var I=class{goodyHutHelper;entityControllerManager;config;isRunning=!1;automationInterval=null;stats;constructor(e){console.log("\u{1F680} Initializing Unified Automation Agent..."),this.goodyHutHelper=new G(e),this.config=this.goodyHutHelper.getConfig();let n=this.config.entityController||{maxRetries:5,retryDelay:1e3,upgradeTimeout:1e4,batchSize:10,batchDelay:2e3,enableLogging:!0,logLevel:"normal"};this.entityControllerManager=new U(n),this.stats={goodyHut:{totalInstances:0,validInstances:0,invalidInstances:0,upgradeableInstances:0,collectionsPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()},entityController:{totalInstances:0,selectedInstances:0,upgradeableInstances:0,upgradesPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()},startTime:Date.now(),lastUpdateTime:Date.now(),totalOperations:0},console.log("\u2705 Unified Automation Agent initialized")}async initialize(){console.log("\u{1F504} Initializing unified automation systems...");try{let e=await this.goodyHutHelper.initialize();return e.success?(console.log("\u2705 GoodyHutHelper initialized successfully"),await this.entityControllerManager.initialize()?(console.log("\u2705 EntityController initialized successfully"),this.setupGlobalFunctions(),console.log("\u{1F3AF} Unified Automation Agent ready!"),!0):(console.log("\u274C EntityController initialization failed"),!1)):(console.log("\u274C GoodyHutHelper initialization failed:",e.errorMessage),!1)}catch(e){return console.log("\u274C Unified initialization failed:",e),!1}}startAutomation(){return this.isRunning?(console.log("\u26A0\uFE0F Automation is already running"),!1):(console.log("\u{1F680} Starting unified automation..."),console.log("\u2705 GoodyHutHelper automation is running"),this.automationInterval=setInterval(async()=>{try{console.log("\u{1F504} Running EntityController auto-upgrade cycle...");let e=await this.entityControllerManager.autoUpgradeSelected();e>0&&(console.log(`\u2705 EntityController: ${e} upgrades performed`),this.stats.totalOperations+=e),this.updateStats()}catch(e){console.log("\u274C EntityController automation error:",e)}},3e4),this.isRunning=!0,console.log("\u2705 Unified automation started successfully"),setInterval(()=>{this.logStatus()},6e4),!0)}stopAutomation(){console.log("\u{1F6D1} Stopping unified automation..."),this.automationInterval&&(clearInterval(this.automationInterval),this.automationInterval=null),this.isRunning=!1,console.log("\u2705 Unified automation stopped")}getStats(){return this.updateStats(),{...this.stats}}updateStats(){this.stats.goodyHut=this.goodyHutHelper.getStats(),this.stats.entityController=this.entityControllerManager.getStats(),this.stats.lastUpdateTime=Date.now()}getConfig(){return this.goodyHutHelper.getConfig()}updateGoodyHutConfig(e){this.goodyHutHelper.updateConfig(e)}updateEntityControllerConfig(e){this.entityControllerManager.updateConfig(e)}async forceGoodyHutCollection(){return await this.goodyHutHelper.forceBatchProcess()}async forceEntityUpgrade(){return await this.entityControllerManager.autoUpgradeSelected()}resetGoodyHutDiscovery(){this.goodyHutHelper.resetToDiscovery()}getStatusSummary(){let e=this.getStats(),n=Date.now()-e.startTime;return["=== UNIFIED AUTOMATION STATUS ===",`Uptime: ${`${Math.floor(n/6e4)}m ${Math.floor(n%6e4/1e3)}s`}`,`Status: ${this.isRunning?"RUNNING":"STOPPED"}`,"","GoodyHut Automation:",`  - Collections: ${e.goodyHut.collectionsPerformed}`,`  - Total Instances: ${e.goodyHut.totalInstances}`,`  - Valid Instances: ${e.goodyHut.validInstances}`,"","EntityController Automation:",`  - Upgrades: ${e.entityController.upgradesPerformed}`,`  - Selected Entities: ${e.entityController.selectedInstances}`,`  - Upgradeable Entities: ${e.entityController.upgradeableInstances}`,"",`Total Operations: ${e.totalOperations}`,"================================="].join(`
`)}logStatus(){this.isRunning&&console.log(this.getStatusSummary())}setupGlobalFunctions(){console.log("\u{1F310} Setting up unified global functions...");try{globalThis.getUnifiedStats=()=>this.getStats(),globalThis.getUnifiedStatus=()=>this.getStatusSummary(),globalThis.startUnifiedAutomation=()=>this.startAutomation(),globalThis.stopUnifiedAutomation=()=>this.stopAutomation(),globalThis.forceGoodyHutCollection=()=>this.forceGoodyHutCollection(),globalThis.resetGoodyHutDiscovery=()=>this.resetGoodyHutDiscovery(),globalThis.getGoodyHutStats=()=>this.goodyHutHelper.getStats(),globalThis.forceEntityUpgrade=()=>this.forceEntityUpgrade(),globalThis.getEntityControllerStats=()=>this.entityControllerManager.getStats(),globalThis.getAllEntityInstances=()=>this.entityControllerManager.getAllInstances(),console.log("\u2705 Unified global functions setup complete")}catch(e){console.log("\u26A0\uFE0F Global functions setup failed:",e)}}cleanup(){console.log("\u{1F9F9} Cleaning up unified automation..."),this.stopAutomation(),this.goodyHutHelper.cleanup(),this.entityControllerManager.cleanup(),console.log("\u2705 Unified automation cleanup complete")}};Il2Cpp.perform(async()=>{console.log("\u{1F3AE} Starting Unified Dominations Automation...");try{let t=new I({batch:{batchSize:10,batchInterval:5e3,retryLimit:3,discoveryTimeout:3e4,maxWaitTime:6e4},logging:{debugLevel:"normal",logBatchDetails:!0,logErrorDetails:!0},entityController:{maxRetries:3,retryDelay:2e3,upgradeTimeout:15e3,batchSize:5,batchDelay:5e3,enableLogging:!0,logLevel:"verbose"}});globalThis.unifiedAgent=t,await t.initialize()?(console.log("\u2705 Unified automation initialized successfully!"),console.log("\u{1F4A1} Use startUnifiedAutomation() to begin automation"),console.log("\u{1F4A1} Use getUnifiedStatus() to check status"),console.log("\u{1F4A1} Use stopUnifiedAutomation() to stop"),setTimeout(()=>{console.log("\u{1F680} Auto-starting unified automation..."),t.startAutomation()},2e3)):console.log("\u274C Unified automation initialization failed")}catch(t){console.log("\u274C Unified automation setup failed:",t)}});export{I as UnifiedAutomationAgent};
