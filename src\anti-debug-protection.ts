/**
 * Anti-debugging Protection for GoodyHutHelper Automation
 * Handles anti-debugging errors, retry logic, and instance validation
 */

import {
    AntiDebugError,
    ErrorTracker,
    InstanceTracker
} from './interfaces/goody-hut-interfaces';

/**
 * Anti-debugging protection configuration
 */
export interface AntiDebugConfig {
    maxRetries: number;
    retryDelay: number;
    enableRetryBackoff: boolean;
    maxBackoffDelay: number;
    ignoreKnownErrors: boolean;
    logErrorDetails: boolean;
}

/**
 * Retry attempt information
 */
export interface RetryAttempt {
    attemptNumber: number;
    timestamp: number;
    errorType: AntiDebugError;
    success: boolean;
    delay: number;
}

/**
 * Anti-debugging protection manager
 */
export class AntiDebugProtection {
    private config: AntiDebugConfig;
    private errorCounts: Map<string, number> = new Map();
    private retryHistory: Map<string, RetryAttempt[]> = new Map();
    private lastErrorTime: Map<string, number> = new Map();
    
    constructor(config?: Partial<AntiDebugConfig>) {
        this.config = {
            maxRetries: 5,
            retryDelay: 1000,
            enableRetryBackoff: true,
            maxBackoffDelay: 10000,
            ignoreKnownErrors: true,
            logErrorDetails: true,
            ...config
        };
    }
    
    /**
     * Classify error type based on error message
     */
    public classifyError(error: any): AntiDebugError {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const lowerMessage = errorMessage.toLowerCase();
        
        if (lowerMessage.includes('breakpoint triggered') || 
            lowerMessage.includes('breakpoint')) {
            return 'breakpoint_triggered';
        }
        
        if (lowerMessage.includes('access violation') || 
            lowerMessage.includes('segmentation fault') ||
            lowerMessage.includes('sigsegv')) {
            return 'access_violation';
        }
        
        if (lowerMessage.includes('abort was called') || 
            lowerMessage.includes('abort()') ||
            lowerMessage.includes('sigabrt')) {
            return 'abort_called';
        }
        
        if (lowerMessage.includes('invalid pointer') ||
            lowerMessage.includes('null pointer') ||
            lowerMessage.includes('bad pointer')) {
            return 'unknown_error'; // Map to known type
        }

        if (lowerMessage.includes('timeout') ||
            lowerMessage.includes('timed out')) {
            return 'unknown_error'; // Map to known type
        }
        
        return 'unknown_error';
    }
    
    /**
     * Check if error should be ignored based on configuration
     */
    public shouldIgnoreError(errorType: AntiDebugError): boolean {
        if (!this.config.ignoreKnownErrors) {
            return false;
        }
        
        const knownAntiDebugErrors: AntiDebugError[] = [
            'breakpoint_triggered',
            'access_violation',
            'abort_called',
            'illegal_instruction'
        ];
        
        return knownAntiDebugErrors.includes(errorType);
    }
    
    /**
     * Calculate retry delay with optional exponential backoff
     */
    public calculateRetryDelay(attemptNumber: number): number {
        if (!this.config.enableRetryBackoff) {
            return this.config.retryDelay;
        }
        
        // Exponential backoff: delay * (2 ^ attemptNumber)
        const backoffDelay = this.config.retryDelay * Math.pow(2, attemptNumber - 1);
        return Math.min(backoffDelay, this.config.maxBackoffDelay);
    }
    
    /**
     * Check if operation should be retried
     */
    public shouldRetry(operationId: string, errorType: AntiDebugError): boolean {
        const currentRetries = this.errorCounts.get(operationId) || 0;
        
        // Don't retry if max retries exceeded
        if (currentRetries >= this.config.maxRetries) {
            return false;
        }
        
        // Don't retry unknown errors unless configured to do so
        if (errorType === 'unknown_error' && this.config.ignoreKnownErrors) {
            return false;
        }
        
        // Check if we're retrying too frequently
        const lastError = this.lastErrorTime.get(operationId);
        if (lastError && Date.now() - lastError < 100) {
            return false; // Prevent rapid retry loops
        }
        
        return true;
    }
    
    /**
     * Record retry attempt
     */
    public recordRetryAttempt(operationId: string, errorType: AntiDebugError, success: boolean): void {
        const currentRetries = this.errorCounts.get(operationId) || 0;
        const newRetryCount = success ? 0 : currentRetries + 1;
        
        this.errorCounts.set(operationId, newRetryCount);
        this.lastErrorTime.set(operationId, Date.now());
        
        // Record retry history
        const history = this.retryHistory.get(operationId) || [];
        const attempt: RetryAttempt = {
            attemptNumber: currentRetries + 1,
            timestamp: Date.now(),
            errorType,
            success,
            delay: this.calculateRetryDelay(currentRetries + 1)
        };
        
        history.push(attempt);
        
        // Keep only last 10 attempts to prevent memory bloat
        if (history.length > 10) {
            history.shift();
        }
        
        this.retryHistory.set(operationId, history);
    }
    
    /**
     * Execute operation with anti-debugging protection and retry logic
     */
    public async executeWithProtection<T>(
        operationId: string,
        operation: () => T | Promise<T>,
        context?: string
    ): Promise<T | null> {
        let lastError: any = null;
        
        for (let attempt = 1; attempt <= this.config.maxRetries + 1; attempt++) {
            try {
                const result = await operation();
                
                // Record successful attempt
                this.recordRetryAttempt(operationId, 'unknown_error', true);
                
                if (attempt > 1 && this.config.logErrorDetails) {
                    console.log(`[+] ${context || operationId}: Succeeded on attempt ${attempt}`);
                }
                
                return result;
                
            } catch (error) {
                lastError = error;
                const errorType = this.classifyError(error);
                
                // Record failed attempt
                this.recordRetryAttempt(operationId, errorType, false);
                
                if (this.config.logErrorDetails) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    console.log(`[!] ${context || operationId}: Attempt ${attempt} failed - ${errorType}: ${errorMessage}`);
                }
                
                // Check if we should ignore this error
                if (this.shouldIgnoreError(errorType)) {
                    if (this.config.logErrorDetails) {
                        console.log(`[~] ${context || operationId}: Ignoring known anti-debug error (${errorType})`);
                    }
                    return null;
                }
                
                // Check if we should retry
                if (attempt <= this.config.maxRetries && this.shouldRetry(operationId, errorType)) {
                    const delay = this.calculateRetryDelay(attempt);
                    
                    if (this.config.logErrorDetails) {
                        console.log(`[*] ${context || operationId}: Retrying in ${delay}ms (attempt ${attempt + 1}/${this.config.maxRetries + 1})`);
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, delay));
                    continue;
                }
                
                // Max retries exceeded or shouldn't retry
                break;
            }
        }
        
        // All attempts failed
        if (this.config.logErrorDetails) {
            const errorType = this.classifyError(lastError);
            console.log(`[❌] ${context || operationId}: All attempts failed - final error: ${errorType}`);
        }
        
        return null;
    }
    
    /**
     * Validate IL2CPP instance pointer with anti-debugging protection
     */
    public validateInstancePointer(instancePtr: NativePointer, context?: string): boolean {
        const operationId = `validate_${instancePtr.toString()}`;
        
        try {
            // Basic null check
            if (instancePtr.isNull()) {
                return false;
            }
            
            // Try to read a small amount of memory to test accessibility
            const testRead = instancePtr.readU32();
            
            // If we can read, the pointer is likely valid
            return true;
            
        } catch (error) {
            const errorType = this.classifyError(error);
            
            if (this.shouldIgnoreError(errorType)) {
                if (this.config.logErrorDetails) {
                    console.log(`[~] ${context || 'validateInstance'}: Ignoring validation error (${errorType}) for ${instancePtr}`);
                }
                return false;
            }
            
            if (this.config.logErrorDetails) {
                console.log(`[!] ${context || 'validateInstance'}: Validation failed for ${instancePtr} - ${errorType}`);
            }
            
            return false;
        }
    }
    
    /**
     * Get retry statistics for an operation
     */
    public getRetryStats(operationId: string): { retryCount: number; history: RetryAttempt[] } {
        return {
            retryCount: this.errorCounts.get(operationId) || 0,
            history: this.retryHistory.get(operationId) || []
        };
    }
    
    /**
     * Clear retry history for an operation
     */
    public clearRetryHistory(operationId: string): void {
        this.errorCounts.delete(operationId);
        this.retryHistory.delete(operationId);
        this.lastErrorTime.delete(operationId);
    }
    
    /**
     * Get overall protection statistics
     */
    public getProtectionStats(): {
        totalOperations: number;
        totalRetries: number;
        errorTypeDistribution: Map<AntiDebugError, number>;
    } {
        const totalOperations = this.errorCounts.size;
        const totalRetries = Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0);
        
        const errorTypeDistribution = new Map<AntiDebugError, number>();
        
        for (const history of this.retryHistory.values()) {
            for (const attempt of history) {
                if (!attempt.success) {
                    const current = errorTypeDistribution.get(attempt.errorType) || 0;
                    errorTypeDistribution.set(attempt.errorType, current + 1);
                }
            }
        }
        
        return {
            totalOperations,
            totalRetries,
            errorTypeDistribution
        };
    }
}
