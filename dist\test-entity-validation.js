📦
80660 /src/test-entity-validation.js
✄
var u=function(e,t,a,n){var r=arguments.length,s=r<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,a):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(e,t,a,n);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(s=(r<3?o(s):r>3?o(t,a,s):o(t,a))||s);return r>3&&s&&Object.defineProperty(t,a,s),s},d;(function(e){e.application={get dataPath(){return t("get_persistentDataPath")},get identifier(){return t("get_identifier")??t("get_bundleIdentifier")??Process.mainModule.name},get version(){return t("get_version")??V(e.module).toString(16)}},_(e,"unityVersion",()=>{try{let n=e.$config.unityVersion??t("get_unityVersion");if(n!=null)return n}catch{}let a="69 6c 32 63 70 70";for(let n of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:r}of Memory.scanSync(n.base,n.size,a)){for(;r.readU8()!=0;)r=r.sub(1);let s=w.find(r.add(1).readCString());if(s!=null)return s}f("couldn't determine the Unity version, please specify it manually")},c),_(e,"unityVersionIsBelow201830",()=>w.lt(e.unityVersion,"2018.3.0"),c),_(e,"unityVersionIsBelow202120",()=>w.lt(e.unityVersion,"2021.2.0"),c);function t(a){let n=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+a)),r=new NativeFunction(n,"pointer",[]);return r.isNull()?null:new e.String(r()).asNullable()?.content??null}})(d||(d={}));var d;(function(e){function t(a,n){let r={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},s=typeof a=="boolean"?"System.Boolean":typeof a=="number"?r[n??"int32"]:a instanceof Int64?"System.Int64":a instanceof UInt64?"System.UInt64":a instanceof NativePointer?r[n??"intptr"]:f(`Cannot create boxed primitive using value of type '${typeof a}'`),o=e.corlib.class(s??f(`Unknown primitive type name '${n}'`)).alloc();return(o.tryField("m_value")??o.tryField("_pointer")??f(`Could not find primitive field in class '${s}'`)).value=a,o}e.boxed=t})(d||(d={}));var d;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(d||(d={}));var d;(function(e){function t(o,i){o=o??`${e.application.identifier}_${e.application.version}.cs`,i=i??e.application.dataPath??Process.getCurrentDir(),r(i);let l=`${i}/${o}`,h=new File(l,"w");for(let m of e.domain.assemblies){x(`dumping ${m.name}...`);for(let g of m.image.classes)h.write(`${g}

`)}h.flush(),h.close(),L(`dump saved to ${l}`),s()}e.dump=t;function a(o,i=!1){o=o??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!i&&n(o)&&f(`directory ${o} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let l of e.domain.assemblies){x(`dumping ${l.name}...`);let h=`${o}/${l.name.replaceAll(".","/")}.cs`;r(h.substring(0,h.lastIndexOf("/")));let m=new File(h,"w");for(let g of l.image.classes)m.write(`${g}

`);m.flush(),m.close()}L(`dump saved to ${o}`),s()}e.dumpTree=a;function n(o){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(o))}function r(o){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(o))}function s(){$("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(d||(d={}));var d;(function(e){function t(a="current"){let n=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(r){a=="current"&&!e.exports.threadGetCurrent().equals(n)||x(new e.Object(r[0].readPointer()))})}e.installExceptionListener=t})(d||(d={}));var d;(function(e){e.exports={get alloc(){return t("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return t("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return t("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return t("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return t("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return t("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return t("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return t("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return t("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return t("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return t("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return t("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return t("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return t("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return t("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return t("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return t("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return t("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return t("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return t("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return t("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return t("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return t("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return t("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return t("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return t("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return t("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return t("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return t("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return t("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return t("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return t("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return t("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return t("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return t("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return t("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return t("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return t("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return t("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return t("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return t("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return t("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return t("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return t("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return t("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return t("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return t("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return t("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return t("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return t("il2cpp_free","void",["pointer"])},get gcCollect(){return t("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return t("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return t("il2cpp_gc_disable","void",[])},get gcEnable(){return t("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return t("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return t("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return t("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return t("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return t("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return t("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return t("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return t("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return t("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return t("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return t("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return t("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return t("il2cpp_stop_gc_world","void",[])},get getCorlib(){return t("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return t("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return t("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return t("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return t("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return t("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return t("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return t("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return t("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return t("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return t("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return t("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return t("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return t("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return t("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return t("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return t("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return t("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return t("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return t("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return t("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return t("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return t("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return t("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return t("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return t("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return t("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return t("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return t("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return t("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return t("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return t("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return t("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return t("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return t("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return t("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return t("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return t("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return t("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return t("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return t("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return t("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return t("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return t("il2cpp_string_length","int32",["pointer"])},get stringNew(){return t("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return t("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return t("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return t("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return t("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return t("il2cpp_thread_current","pointer",[])},get threadIsVm(){return t("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return t("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return t("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return t("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return t("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return t("il2cpp_type_get_type","int",["pointer"])}},F(e.exports,c),_(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),c);function t(a,n,r){let s=e.$config.exports?.[a]?.()??e.module.findExportByName(a)??e.memorySnapshotExports[a],o=new NativeFunction(s??NULL,n,r);return o.isNull()?new Proxy(o,{get(i,l){let h=i[l];return typeof h=="function"?h.bind(i):h},apply(){s==null?f(`couldn't resolve export ${a}`):s.isNull()&&f(`export ${a} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):o}})(d||(d={}));var d;(function(e){function t(n){return r=>r instanceof e.Class?n.isAssignableFrom(r):n.isAssignableFrom(r.class)}e.is=t;function a(n){return r=>r instanceof e.Class?r.equals(n):r.class.equals(n)}e.isExactly=a})(d||(d={}));var d;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(t){t?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(t){e.exports.gcSetMaxTimeSlice(t)},choose(t){let a=[],n=(s,o)=>{for(let i=0;i<o;i++)a.push(new e.Object(s.add(i*Process.pointerSize).readPointer()))},r=new NativeCallback(n,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let s=new NativeCallback(()=>{},"void",[]),o=e.exports.livenessCalculationBegin(t,0,r,NULL,s,s);e.exports.livenessCalculationFromStatics(o),e.exports.livenessCalculationEnd(o)}else{let s=(l,h)=>!l.isNull()&&h.compare(0)==0?(e.free(l),NULL):e.alloc(h),o=new NativeCallback(s,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let i=e.exports.livenessAllocateStruct(t,0,r,NULL,o);e.exports.livenessCalculationFromStatics(i),e.exports.livenessFinalize(i),this.startWorld(),e.exports.livenessFreeStruct(i)}return a},collect(t){e.exports.gcCollect(t<0?0:t>2?2:t)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(d||(d={}));var M;(function(e){_(e,"apiLevel",()=>{let a=t("ro.build.version.sdk");return a?parseInt(a):null},c);function t(a){let n=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(n){let r=new NativeFunction(n,"void",["pointer","pointer"]),s=Memory.alloc(92).writePointer(NULL);return r(Memory.allocUtf8String(a),s),s.readCString()??void 0}}})(M||(M={}));function f(e){let t=new Error(e);throw t.name="Il2CppError",t.stack=t.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),t}function $(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function L(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function x(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function F(e,t,a=Object.getOwnPropertyDescriptors(e)){for(let n in a)a[n]=t(e,n,a[n]);return Object.defineProperties(e,a),e}function _(e,t,a,n){globalThis.Object.defineProperty(e,t,n?.(e,t,{get:a,configurable:!0})??{get:a,configurable:!0})}function j(e){let t=3735928559,a=1103547991;for(let n=0,r;n<e.length;n++)r=e.charCodeAt(n),t=Math.imul(t^r,2654435761),a=Math.imul(a^r,1597334677);return t=Math.imul(t^t>>>16,2246822507),t^=Math.imul(a^a>>>13,3266489909),a=Math.imul(a^a>>>16,2246822507),a^=Math.imul(t^t>>>13,3266489909),4294967296*(2097151&a)+(t>>>0)}function V(e){return j(e.enumerateExports().sort((t,a)=>t.name.localeCompare(a.name)).map(t=>t.name+t.address.sub(e.base)).join(""))}function c(e,t,a){let n=a.get;if(!n)throw new Error("@lazy can only be applied to getter accessors");return a.get=function(){let r=n.call(this);return Object.defineProperty(this,t,{value:r,configurable:a.configurable,enumerable:a.enumerable,writable:!1}),r},a}var b=class{handle;constructor(t){t instanceof NativePointer?this.handle=t:this.handle=t.handle}equals(t){return this.handle.equals(t.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function D(e){return Object.keys(e).reduce((t,a)=>(t[t[a]]=a,t),e)}NativePointer.prototype.offsetOf=function(e,t){t??=512;for(let a=0;t>0?a<t:a<-t;a++)if(e(t>0?this.add(a):this.sub(a)))return a;return null};function v(e){let t=[],a=Memory.alloc(Process.pointerSize),n=e(a);for(;!n.isNull();)t.push(n),n=e(a);return t}function R(e){let t=Memory.alloc(Process.pointerSize),a=e(t);if(a.isNull())return[];let n=new Array(t.readInt());for(let r=0;r<n.length;r++)n[r]=a.add(r*Process.pointerSize).readPointer();return n}function E(e){return new Proxy(e,{cache:new Map,construct(t,a){let n=a[0].toUInt32();return this.cache.has(n)||this.cache.set(n,new t(a[0])),this.cache.get(n)}})}var w;(function(e){let t=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function a(o){return o?.match(t)?.[0]}e.find=a;function n(o,i){return s(o,i)>=0}e.gte=n;function r(o,i){return s(o,i)<0}e.lt=r;function s(o,i){let l=o.match(t),h=i.match(t);for(let m=1;m<=3;m++){let g=Number(l?.[m]??-1),y=Number(h?.[m]??-1);if(g>y)return 1;if(g<y)return-1}return 0}})(w||(w={}));var d;(function(e){function t(i=Process.pointerSize){return e.exports.alloc(i)}e.alloc=t;function a(i){return e.exports.free(i)}e.free=a;function n(i,l){switch(l.enumValue){case e.Type.Enum.BOOLEAN:return!!i.readS8();case e.Type.Enum.BYTE:return i.readS8();case e.Type.Enum.UBYTE:return i.readU8();case e.Type.Enum.SHORT:return i.readS16();case e.Type.Enum.USHORT:return i.readU16();case e.Type.Enum.INT:return i.readS32();case e.Type.Enum.UINT:return i.readU32();case e.Type.Enum.CHAR:return i.readU16();case e.Type.Enum.LONG:return i.readS64();case e.Type.Enum.ULONG:return i.readU64();case e.Type.Enum.FLOAT:return i.readFloat();case e.Type.Enum.DOUBLE:return i.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return i.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(i.readPointer(),l.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(i,l);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(i.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return l.class.isValueType?new e.ValueType(i,l):new e.Object(i.readPointer());case e.Type.Enum.STRING:return new e.String(i.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i.readPointer())}f(`couldn't read the value from ${i} using an unhandled or unknown type ${l.name} (${l.enumValue}), please file an issue`)}e.read=n;function r(i,l,h){switch(h.enumValue){case e.Type.Enum.BOOLEAN:return i.writeS8(+l);case e.Type.Enum.BYTE:return i.writeS8(l);case e.Type.Enum.UBYTE:return i.writeU8(l);case e.Type.Enum.SHORT:return i.writeS16(l);case e.Type.Enum.USHORT:return i.writeU16(l);case e.Type.Enum.INT:return i.writeS32(l);case e.Type.Enum.UINT:return i.writeU32(l);case e.Type.Enum.CHAR:return i.writeU16(l);case e.Type.Enum.LONG:return i.writeS64(l);case e.Type.Enum.ULONG:return i.writeU64(l);case e.Type.Enum.FLOAT:return i.writeFloat(l);case e.Type.Enum.DOUBLE:return i.writeDouble(l);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return i.writePointer(l);case e.Type.Enum.VALUE_TYPE:return Memory.copy(i,l,h.class.valueTypeSize),i;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return l instanceof e.ValueType?(Memory.copy(i,l,h.class.valueTypeSize),i):i.writePointer(l)}f(`couldn't write value ${l} to ${i} using an unhandled or unknown type ${h.name} (${h.enumValue}), please file an issue`)}e.write=r;function s(i,l){if(globalThis.Array.isArray(i)){let h=Memory.alloc(l.class.valueTypeSize),m=l.class.fields.filter(g=>!g.isStatic);for(let g=0;g<m.length;g++){let y=s(i[g],m[g].type);r(h.add(m[g].offset).sub(e.Object.headerSize),y,m[g].type)}return new e.ValueType(h,l)}else if(i instanceof NativePointer){if(l.isByReference)return new e.Reference(i,l);switch(l.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(i,l.class.baseType);case e.Type.Enum.STRING:return new e.String(i);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(i);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i);default:return i}}else return l.enumValue==e.Type.Enum.BOOLEAN?!!i:l.enumValue==e.Type.Enum.VALUE_TYPE&&l.class.isEnum?s([i],l):i}e.fromFridaValue=s;function o(i){if(typeof i=="boolean")return+i;if(i instanceof e.ValueType){if(i.type.class.isEnum)return i.field("value__").value;{let l=i.type.class.fields.filter(h=>!h.isStatic).map(h=>o(h.bind(i).value));return l.length==0?[0]:l}}else return i}e.toFridaValue=o})(d||(d={}));var d;(function(e){_(e,"module",()=>a()??f("Could not find IL2CPP module"));async function t(r=!1){let s=a()??await new Promise(o=>{let[i,l]=n(),h=setTimeout(()=>{$(`after 10 seconds, IL2CPP module '${i}' has not been loaded yet, is the app running?`)},1e4),m=Process.attachModuleObserver({onAdded(g){(g.name==i||l&&g.name==l)&&(clearTimeout(h),setImmediate(()=>{o(g),m.detach()}))}})});return Reflect.defineProperty(e,"module",{value:s}),e.exports.getCorlib().isNull()?await new Promise(o=>{let i=Interceptor.attach(e.exports.initialize,{onLeave(){i.detach(),r?o(!0):setImmediate(()=>o(!1))}})}):!1}e.initialize=t;function a(){let[r,s]=n();return Process.findModuleByName(r)??Process.findModuleByName(s??r)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function n(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[M.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}f(`${Process.platform} is not supported yet`)}})(d||(d={}));var d;(function(e){async function t(a,n="bind"){let r=null;try{let s=await e.initialize(n=="main");if(n=="main"&&!s)return t(()=>e.mainThread.schedule(a),"free");e.currentThread==null&&(r=e.domain.attach()),n=="bind"&&r!=null&&Script.bindWeak(globalThis,()=>r?.detach());let o=a();return o instanceof Promise?await o:o}catch(s){return Script.nextTick(o=>{throw o},s),Promise.reject(s)}finally{n=="free"&&r!=null&&r.detach()}}e.perform=t})(d||(d={}));var d;(function(e){class t{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let s=`
${this.#e.buffer.join(`
`)}
`;if(this.#d)x(s);else{let o=j(s);this.#e.history.has(o)||(this.#e.history.add(o),x(s))}this.#e.buffer.length=0}}};#u=e.mainThread.id;#d=!1;#h;#l=[];#c;#t;#n;#r;#s;#i;#a;#o;constructor(s){this.#h=s}thread(s){return this.#u=s.id,this}verbose(s){return this.#d=s,this}domain(){return this.#c=e.domain,this}assemblies(...s){return this.#t=s,this}classes(...s){return this.#n=s,this}methods(...s){return this.#r=s,this}filterAssemblies(s){return this.#s=s,this}filterClasses(s){return this.#i=s,this}filterMethods(s){return this.#a=s,this}filterParameters(s){return this.#o=s,this}and(){let s=y=>{if(this.#o==null){this.#l.push(y);return}for(let p of y.parameters)if(this.#o(p)){this.#l.push(y);break}},o=y=>{for(let p of y)s(p)},i=y=>{if(this.#a==null){o(y.methods);return}for(let p of y.methods)this.#a(p)&&s(p)},l=y=>{for(let p of y)i(p)},h=y=>{if(this.#i==null){l(y.image.classes);return}for(let p of y.image.classes)this.#i(p)&&i(p)},m=y=>{for(let p of y)h(p)},g=y=>{if(this.#s==null){m(y.assemblies);return}for(let p of y.assemblies)this.#s(p)&&h(p)};return this.#r?o(this.#r):this.#n?l(this.#n):this.#t?m(this.#t):this.#c&&g(this.#c),this.#t=void 0,this.#n=void 0,this.#r=void 0,this.#s=void 0,this.#i=void 0,this.#a=void 0,this.#o=void 0,this}attach(){for(let s of this.#l)if(!s.virtualAddress.isNull())try{this.#h(s,this.#e,this.#u)}catch(o){switch(o.message){case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:case"already replaced this function":break;default:throw o}}}}e.Tracer=t;function a(r=!1){let s=()=>(i,l,h)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(i.virtualAddress,{onEnter(){this.threadId==h&&l.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(l.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==h&&(l.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--l.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`),l.flush())}})},o=()=>(i,l,h)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0"),g=+!i.isStatic|+e.unityVersionIsBelow201830,y=function(...T){if(this.threadId==h){let k=i.isStatic?void 0:new e.Parameter("this",-1,i.class.type),z=k?[k].concat(i.parameters):i.parameters;l.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(l.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m(${z.map(A=>`\x1B[32m${A.name}\x1B[0m = \x1B[31m${e.fromFridaValue(T[A.position+g],A.type)}\x1B[0m`).join(", ")})`)}let S=i.nativeFunction(...T);return this.threadId==h&&(l.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--l.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m${S==null?"":` = \x1B[36m${e.fromFridaValue(S,i.returnType)}`}\x1B[0m`),l.flush()),S};i.revert();let p=new NativeCallback(y,i.returnType.fridaAlias,i.fridaSignature);Interceptor.replace(i.virtualAddress,p)};return new e.Tracer(r?o():s())}e.trace=a;function n(r){let s=e.domain.assemblies.flatMap(l=>l.image.classes.flatMap(h=>h.methods.filter(m=>!m.virtualAddress.isNull()))).sort((l,h)=>l.virtualAddress.compare(h.virtualAddress)),o=l=>{let h=0,m=s.length-1;for(;h<=m;){let g=Math.floor((h+m)/2),y=s[g].virtualAddress.compare(l);if(y==0)return s[g];y>0?m=g-1:h=g+1}return s[m]},i=()=>(l,h,m)=>{Interceptor.attach(l.virtualAddress,function(){if(this.threadId==m){let g=globalThis.Thread.backtrace(this.context,r);g.unshift(l.virtualAddress);for(let y of g)if(y.compare(e.module.base)>0&&y.compare(e.module.base.add(e.module.size))<0){let p=o(y);if(p){let T=y.sub(p.virtualAddress);T.compare(4095)<0&&h.buffer.push(`\x1B[2m0x${p.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${T.toString(16).padStart(3,"0")}\x1B[0m ${p.class.type.name}::\x1B[1m${p.name}\x1B[0m`)}}h.flush()}})};return new e.Tracer(i())}e.backtrace=n})(d||(d={}));var d;(function(e){class t extends b{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let s=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(o=>o.readS16()==118)??f("couldn't find the elements offset in the native array struct");return _(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(s),this.elementType)},c),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(r){return(r<0||r>=this.length)&&f(`cannot get element at index ${r} as the array length is ${this.length}`),this.elements.get(r)}set(r,s){(r<0||r>=this.length)&&f(`cannot set element at index ${r} as the array length is ${this.length}`),this.elements.set(r,s)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let r=0;r<this.length;r++)yield this.elements.get(r)}}u([c],t.prototype,"elementSize",null),u([c],t.prototype,"elementType",null),u([c],t.prototype,"length",null),u([c],t.prototype,"object",null),u([c],t,"headerSize",null),e.Array=t;function a(n,r){let s=typeof r=="number"?r:r.length,o=new e.Array(e.exports.arrayNew(n,s));return globalThis.Array.isArray(r)&&o.elements.write(r),o}e.array=a})(d||(d={}));var d;(function(e){let t=class extends b{get image(){if(e.exports.assemblyGetImage.isNull()){let n=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??f(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(n.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let n of e.domain.object.method("GetAssemblies",1).invoke(!1))if(n.field("_mono_assembly").value.equals(this))return n;f("couldn't find the object of the native assembly struct")}};u([c],t.prototype,"name",null),u([c],t.prototype,"object",null),t=u([E],t),e.Assembly=t})(d||(d={}));var d;(function(e){let t=class extends b{get actualInstanceSize(){let n=e.corlib.class("System.String"),r=n.handle.offsetOf(s=>s.readInt()==n.instanceSize-2)??f("couldn't find the actual instance size offset in the native class struct");return _(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(r).readS32()},c),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return v(n=>e.exports.classGetFields(this,n)).map(n=>new e.Field(n))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let n=this.image.tryClass(this.fullName)?.asNullable();return n?.equals(this)?null:n??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let n=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(n).map(r=>new e.Class(e.exports.classFromObject(r)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let n=this.tryMethod(".cctor");return n!=null&&!n.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return v(n=>e.exports.classGetInterfaces(this,n)).map(n=>new e.Class(n))}get methods(){return v(n=>e.exports.classGetMethods(this,n)).map(n=>new e.Method(n))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return v(n=>e.exports.classGetNestedClasses(this,n)).map(n=>new e.Class(n))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let n=0,r=this.name;for(let s=this.name.length-1;s>0;s--){let o=r[s];if(o=="]")n++;else{if(o=="["||n==0)break;if(o==",")n++;else break}}return n}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(n){return this.tryField(n)??f(`couldn't find field ${n} in class ${this.type.name}`)}*hierarchy(n){let r=n?.includeCurrent??!0?this:this.parent;for(;r;)yield r,r=r.parent}inflate(...n){this.isGeneric||f(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=n.length&&f(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${n.length}`);let r=n.map(i=>i.type.object),s=e.array(e.corlib.class("System.Type"),r),o=this.type.object.method("MakeGenericType",1).invoke(s);return new e.Class(e.exports.classFromObject(o))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(n){return!!e.exports.classIsAssignableFrom(this,n)}isSubclassOf(n,r){return!!e.exports.classIsSubclassOf(this,n,+r)}method(n,r=-1){return this.tryMethod(n,r)??f(`couldn't find method ${n} in class ${this.type.name}`)}nested(n){return this.tryNested(n)??f(`couldn't find nested class ${n} in class ${this.type.name}`)}new(){let n=this.alloc(),r=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(n,r);let s=r.readPointer();return s.isNull()||f(new e.Object(s).toString()),n}tryField(n){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(n))).asNullable()}tryMethod(n,r=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(n),r)).asNullable()}tryNested(n){return this.nestedClasses.find(r=>r.name==n)}toString(){let n=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${n?` : ${n.map(r=>r?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(n){let r=new NativeCallback(s=>n(new e.Class(s)),"void",["pointer","pointer"]);return e.exports.classForEach(r,NULL)}};u([c],t.prototype,"arrayClass",null),u([c],t.prototype,"arrayElementSize",null),u([c],t.prototype,"assemblyName",null),u([c],t.prototype,"declaringClass",null),u([c],t.prototype,"baseType",null),u([c],t.prototype,"elementClass",null),u([c],t.prototype,"fields",null),u([c],t.prototype,"flags",null),u([c],t.prototype,"fullName",null),u([c],t.prototype,"generics",null),u([c],t.prototype,"hasReferences",null),u([c],t.prototype,"hasStaticConstructor",null),u([c],t.prototype,"image",null),u([c],t.prototype,"instanceSize",null),u([c],t.prototype,"isAbstract",null),u([c],t.prototype,"isBlittable",null),u([c],t.prototype,"isEnum",null),u([c],t.prototype,"isGeneric",null),u([c],t.prototype,"isInflated",null),u([c],t.prototype,"isInterface",null),u([c],t.prototype,"isValueType",null),u([c],t.prototype,"interfaces",null),u([c],t.prototype,"methods",null),u([c],t.prototype,"name",null),u([c],t.prototype,"namespace",null),u([c],t.prototype,"nestedClasses",null),u([c],t.prototype,"parent",null),u([c],t.prototype,"pointerClass",null),u([c],t.prototype,"rank",null),u([c],t.prototype,"staticFieldsData",null),u([c],t.prototype,"valueTypeSize",null),u([c],t.prototype,"type",null),t=u([E],t),e.Class=t})(d||(d={}));var d;(function(e){function t(a,n){let r=e.corlib.class("System.Delegate"),s=e.corlib.class("System.MulticastDelegate");r.isAssignableFrom(a)||f(`cannot create a delegate for ${a.type.name} as it's a non-delegate class`),(a.equals(r)||a.equals(s))&&f(`cannot create a delegate for neither ${r.type.name} nor ${s.type.name}, use a subclass instead`);let o=a.alloc(),i=o.handle.toString(),l=o.tryMethod("Invoke")??f(`cannot create a delegate for ${a.type.name}, there is no Invoke method`);o.method(".ctor").invoke(o,l.handle);let h=l.wrap(n);return o.field("method_ptr").value=h,o.field("invoke_impl").value=h,e._callbacksToKeepAlive[i]=h,o}e.delegate=t,e._callbacksToKeepAlive={}})(d||(d={}));var d;(function(e){let t=class extends b{get assemblies(){let n=R(r=>e.exports.domainGetAssemblies(this,r));if(n.length==0){let r=this.object.method("GetAssemblies").overload().invoke();n=globalThis.Array.from(r).map(s=>s.field("_mono_assembly").value)}return n.map(r=>new e.Assembly(r))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(n){return this.tryAssembly(n)??f(`couldn't find assembly ${n}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(n){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(n))).asNullable()}};u([c],t.prototype,"assemblies",null),u([c],t.prototype,"object",null),t=u([E],t),e.Domain=t,_(e,"domain",()=>new e.Domain(e.exports.domainGet()),c)})(d||(d={}));var d;(function(e){class t extends b{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let n=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return _(e.Field.prototype,"isThreadStatic",function(){return this.offset==n},c),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||f(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let n=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,n),e.read(n,this.type)}set value(n){this.isStatic||f(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&f(`cannot write the value of field ${this.name} as it's thread static or literal`);let r=n instanceof e.Object&&this.type.class.isValueType?n.unbox():n instanceof b?n.handle:n instanceof NativePointer?n:e.write(Memory.alloc(this.type.class.valueTypeSize),n,this.type);e.exports.fieldSetStaticValue(this.handle,r)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(n){this.isStatic&&f(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let r=this.offset-(n instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(s,o){return o=="value"?e.read(n.handle.add(r),s.type):Reflect.get(s,o)},set(s,o,i){return o=="value"?(e.write(n.handle.add(r),i,s.type),!0):Reflect.set(s,o,i)}})}}u([c],t.prototype,"class",null),u([c],t.prototype,"flags",null),u([c],t.prototype,"isLiteral",null),u([c],t.prototype,"isStatic",null),u([c],t.prototype,"isThreadStatic",null),u([c],t.prototype,"modifier",null),u([c],t.prototype,"name",null),u([c],t.prototype,"offset",null),u([c],t.prototype,"type",null),e.Field=t})(d||(d={}));var d;(function(e){class t{handle;constructor(n){this.handle=n}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=t})(d||(d={}));var d;(function(e){let t=class extends b{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let n=this.assembly.object.method("GetTypes").invoke(!1),r=globalThis.Array.from(n,o=>new e.Class(e.exports.classFromObject(o))),s=this.tryClass("<Module>");return s&&r.unshift(s),r}else return globalThis.Array.from(globalThis.Array(this.classCount),(n,r)=>new e.Class(e.exports.imageGetClass(this,r)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(n){return this.tryClass(n)??f(`couldn't find class ${n} in assembly ${this.name}`)}tryClass(n){let r=n.lastIndexOf("."),s=Memory.allocUtf8String(r==-1?"":n.slice(0,r)),o=Memory.allocUtf8String(n.slice(r+1));return new e.Class(e.exports.classFromName(this,s,o)).asNullable()}};u([c],t.prototype,"assembly",null),u([c],t.prototype,"classCount",null),u([c],t.prototype,"classes",null),u([c],t.prototype,"name",null),t=u([E],t),e.Image=t,_(e,"corlib",()=>new e.Image(e.exports.getCorlib()),c)})(d||(d={}));var d;(function(e){class t extends b{static capture(){return new e.MemorySnapshot}constructor(r=e.exports.memorySnapshotCapture()){super(r)}get classes(){return v(r=>e.exports.memorySnapshotGetClasses(this,r)).map(r=>new e.Class(r))}get objects(){return R(r=>e.exports.memorySnapshotGetObjects(this,r)).filter(r=>!r.isNull()).map(r=>new e.Object(r))}free(){e.exports.memorySnapshotFree(this)}}u([c],t.prototype,"classes",null),u([c],t.prototype,"objects",null),e.MemorySnapshot=t;function a(n){let r=e.MemorySnapshot.capture(),s=n(r);return r.free(),s}e.memorySnapshot=a})(d||(d={}));var d;(function(e){class t extends b{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let r=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,r),r.readU32()}get fridaSignature(){let r=[];for(let s of this.parameters)r.push(s.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&r.unshift("pointer"),this.isInflated&&r.push("pointer"),r}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let r=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(r).map(s=>new e.Class(e.exports.classFromObject(s)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(r,s)=>{let o=e.exports.methodGetParameterName(this,s).readUtf8String(),i=e.exports.methodGetParameterType(this,s);return new e.Parameter(o,s,new e.Type(i))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let r=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,s=r.field("method_ptr").value,i=r.field("method").value.offsetOf(l=>l.readPointer().equals(s))??f("couldn't find the virtual address offset in the native method struct");return _(e.Method.prototype,"virtualAddress",function(){return this.handle.add(i).readPointer()},c),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(r){try{Interceptor.replace(this.virtualAddress,this.wrap(r))}catch(s){switch(s.message){case"access violation accessing 0x0":f(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(s.message)?.input:$(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":$(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw s}}}inflate(...r){if(!this.isGeneric||this.generics.length!=r.length){for(let l of this.overloads())if(l.isGeneric&&l.generics.length==r.length)return l.inflate(...r);f(`could not find inflatable signature of method ${this.name} with ${r.length} generic parameter(s)`)}let s=r.map(l=>l.type.object),o=e.array(e.corlib.class("System.Type"),s),i=this.object.method("MakeGenericMethod",1).invoke(o);return new e.Method(i.field("mhandle").value)}invoke(...r){return this.isStatic||f(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...r)}invokeRaw(r,...s){let o=s.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&o.unshift(r),this.isInflated&&o.push(this.handle);try{let i=this.nativeFunction(...o);return e.fromFridaValue(i,this.returnType)}catch(i){switch(i==null&&f("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),i.message){case"bad argument count":f(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${s.length}`);case"expected a pointer":case"expected number":case"expected array with fields":f(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw i}}overload(...r){return this.tryOverload(...r)??f(`couldn't find overloaded method ${this.name}(${r.map(o=>o instanceof e.Class?o.type.name:o)})`)}*overloads(){for(let r of this.class.hierarchy())for(let s of r.methods)this.name==s.name&&(yield s)}parameter(r){return this.tryParameter(r)??f(`couldn't find parameter ${r} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...r){let s=r.length*1,o=r.length*2,i;e:for(let l of this.overloads()){if(l.parameterCount!=r.length)continue;let h=0,m=0;for(let g of l.parameters){let y=r[m];if(y instanceof e.Class)if(g.type.is(y.type))h+=2;else if(g.type.class.isAssignableFrom(y))h+=1;else continue e;else if(g.type.name==y)h+=2;else continue e;m++}if(!(h<s)){if(h==o)return l;if(i==null||h>i[0])i=[h,l];else if(h==i[0]){let g=0;for(let y of i[1].parameters){if(y.type.class.isAssignableFrom(l.parameters[g].type.class)){i=[h,l];continue e}g++}}}}return i?.[1]}tryParameter(r){return this.parameters.find(s=>s.name==r)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(r=>r.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(r){return this.isStatic&&f(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(s,o,i){switch(o){case"invoke":let l=r instanceof e.ValueType?s.class.isValueType?r.handle.sub(a()?e.Object.headerSize:0):f(`cannot invoke method ${s.class.type.name}::${s.name} against a value type, you must box it first`):s.class.isValueType?r.handle.add(a()?0:e.Object.headerSize):r.handle;return s.invokeRaw.bind(s,l);case"overloads":return function*(){for(let m of s[o]())m.isStatic||(yield m)};case"inflate":case"overload":case"tryOverload":let h=Reflect.get(s,o).bind(i);return function(...m){return h(...m)?.bind(r)}}return Reflect.get(s,o)}})}wrap(r){let s=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...o)=>{let i=this.isStatic?this.class:this.class.isValueType?new e.ValueType(o[0].add(a()?e.Object.headerSize:0),this.class.type):new e.Object(o[0]),l=this.parameters.map((m,g)=>e.fromFridaValue(o[g+s],m.type)),h=r.call(i,...l);return e.toFridaValue(h)},this.returnType.fridaAlias,this.fridaSignature)}}u([c],t.prototype,"class",null),u([c],t.prototype,"flags",null),u([c],t.prototype,"implementationFlags",null),u([c],t.prototype,"fridaSignature",null),u([c],t.prototype,"generics",null),u([c],t.prototype,"isExternal",null),u([c],t.prototype,"isGeneric",null),u([c],t.prototype,"isInflated",null),u([c],t.prototype,"isStatic",null),u([c],t.prototype,"isSynchronized",null),u([c],t.prototype,"modifier",null),u([c],t.prototype,"name",null),u([c],t.prototype,"nativeFunction",null),u([c],t.prototype,"object",null),u([c],t.prototype,"parameterCount",null),u([c],t.prototype,"parameters",null),u([c],t.prototype,"relativeVirtualAddress",null),u([c],t.prototype,"returnType",null),e.Method=t;let a=()=>{let n=e.corlib.class("System.Int64").alloc();n.field("m_value").value=3735928559;let r=n.method("Equals",1).overload(n.class).invokeRaw(n,3735928559);return(a=()=>r)()}})(d||(d={}));var d;(function(e){class t extends b{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&f(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(n,r,s){return r=="class"?Reflect.get(n,r).parent:r=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,r).get.bind(s)():Reflect.get(n,r)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(n){return this.tryField(n)??f(`couldn't find non-static field ${n} in hierarchy of class ${this.class.type.name}`)}method(n,r=-1){return this.tryMethod(n,r)??f(`couldn't find non-static method ${n} in hierarchy of class ${this.class.type.name}`)}ref(n){return new e.GCHandle(e.exports.gcHandleNew(this,+n))}virtualMethod(n){return new e.Method(e.exports.objectGetVirtualMethod(this,n)).bind(this)}tryField(n){let r=this.class.tryField(n);if(r?.isStatic){for(let s of this.class.hierarchy({includeCurrent:!1}))for(let o of s.fields)if(o.name==n&&!o.isStatic)return o.bind(this);return}return r?.bind(this)}tryMethod(n,r=-1){let s=this.class.tryMethod(n,r);if(s?.isStatic){for(let o of this.class.hierarchy())for(let i of o.methods)if(i.name==n&&!i.isStatic&&(r<0||i.parameterCount==r))return i.bind(this);return}return s?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):f(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(n){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+n))}}u([c],t.prototype,"class",null),u([c],t.prototype,"size",null),u([c],t,"headerSize",null),e.Object=t,function(a){class n{handle;constructor(s){this.handle=s}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(s){return!!e.exports.monitorTryEnter(this.handle,s)}tryWait(s){return!!e.exports.monitorTryWait(this.handle,s)}wait(){return e.exports.monitorWait(this.handle)}}a.Monitor=n}(t=e.Object||(e.Object={}))})(d||(d={}));var d;(function(e){class t{name;position;type;constructor(n,r,s){this.name=n,this.position=r,this.type=s}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=t})(d||(d={}));var d;(function(e){class t extends b{type;constructor(n,r){super(n),this.type=r}get(n){return e.read(this.handle.add(n*this.type.class.arrayElementSize),this.type)}read(n,r=0){let s=new globalThis.Array(n);for(let o=0;o<n;o++)s[o]=this.get(o+r);return s}set(n,r){e.write(this.handle.add(n*this.type.class.arrayElementSize),r,this.type)}toString(){return this.handle.toString()}write(n,r=0){for(let s=0;s<n.length;s++)this.set(s+r,n[s])}}e.Pointer=t})(d||(d={}));var d;(function(e){class t extends b{type;constructor(r,s){super(r),this.type=s}get value(){return e.read(this.handle,this.type)}set value(r){e.write(this.handle,r,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=t;function a(n,r){let s=Memory.alloc(Process.pointerSize);switch(typeof n){case"boolean":return new e.Reference(s.writeS8(+n),e.corlib.class("System.Boolean").type);case"number":switch(r?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(s.writeU8(n),r);case e.Type.Enum.BYTE:return new e.Reference(s.writeS8(n),r);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(s.writeU16(n),r);case e.Type.Enum.SHORT:return new e.Reference(s.writeS16(n),r);case e.Type.Enum.UINT:return new e.Reference(s.writeU32(n),r);case e.Type.Enum.INT:return new e.Reference(s.writeS32(n),r);case e.Type.Enum.ULONG:return new e.Reference(s.writeU64(n),r);case e.Type.Enum.LONG:return new e.Reference(s.writeS64(n),r);case e.Type.Enum.FLOAT:return new e.Reference(s.writeFloat(n),r);case e.Type.Enum.DOUBLE:return new e.Reference(s.writeDouble(n),r)}case"object":if(n instanceof e.ValueType||n instanceof e.Pointer)return new e.Reference(n.handle,n.type);if(n instanceof e.Object)return new e.Reference(s.writePointer(n),n.class.type);if(n instanceof e.String||n instanceof e.Array)return new e.Reference(s.writePointer(n),n.object.class.type);if(n instanceof NativePointer)switch(r?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(s.writePointer(n),r)}else{if(n instanceof Int64)return new e.Reference(s.writeS64(n),e.corlib.class("System.Int64").type);if(n instanceof UInt64)return new e.Reference(s.writeU64(n),e.corlib.class("System.UInt64").type)}default:f(`couldn't create a reference to ${n} using an unhandled type ${r?.name}`)}}e.reference=a})(d||(d={}));var d;(function(e){class t extends b{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(r){let s=e.string("vfsfitvnm").handle.offsetOf(o=>o.readInt()==9)??f("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(o){e.exports.stringGetChars(this).writeUtf16String(o??""),this.handle.add(s).writeS32(o?.length??0)}}),this.content=r}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=t;function a(n){return new e.String(e.exports.stringNew(Memory.allocUtf8String(n??"")))}e.string=a})(d||(d={}));var d;(function(e){class t extends b{get id(){let n=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let r=Process.getCurrentThreadId(),o=ptr(n.apply(e.currentThread)).offsetOf(l=>l.readS32()==r,1024)??f("couldn't find the offset for determining the kernel id of a posix thread"),i=n;n=function(){return ptr(i.apply(this)).add(o).readS32()}}return _(e.Thread.prototype,"id",n,c),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let r=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(r.tryField("_syncContext")?.value??r.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(n){let r=this.synchronizationContext?.tryMethod("Post");return r==null?Process.runOnThread(this.id,n):new Promise(s=>{let o=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let i=n();setImmediate(()=>s(i))});Script.bindWeak(globalThis,()=>{o.field("method_ptr").value=o.field("invoke_impl").value=e.exports.domainGet}),r.invoke(o,NULL)})}tryLocalValue(n){for(let r=0;r<16;r++){let s=this.staticData.add(r*Process.pointerSize).readPointer();if(!s.isNull()){let o=new e.Object(s.readPointer()).asNullable();if(o?.class?.isSubclassOf(n,!1))return o}}}}u([c],t.prototype,"internal",null),u([c],t.prototype,"isFinalizer",null),u([c],t.prototype,"managedId",null),u([c],t.prototype,"object",null),u([c],t.prototype,"staticData",null),u([c],t.prototype,"synchronizationContext",null),e.Thread=t,_(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let a=e.currentThread?.handle??f("Current thread is not attached to IL2CPP"),n=a.toMatchPattern(),r=[];for(let s of Process.enumerateRanges("rw-"))if(s.file==null){let o=Memory.scanSync(s.base,s.size,n);if(o.length==1){for(;;){let i=o[0].address.sub(o[0].size*r.length).readPointer();if(i.isNull()||!i.readPointer().equals(a.readPointer()))break;r.unshift(new e.Thread(i))}break}}return r}return R(e.exports.threadGetAttachedThreads).map(a=>new e.Thread(a))}),_(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),_(e,"mainThread",()=>e.attachedThreads[0])})(d||(d={}));var d;(function(e){let t=class extends b{static get Enum(){let n=(s,o=i=>i)=>o(e.corlib.class(s)).type.enumValue,r={VOID:n("System.Void"),BOOLEAN:n("System.Boolean"),CHAR:n("System.Char"),BYTE:n("System.SByte"),UBYTE:n("System.Byte"),SHORT:n("System.Int16"),USHORT:n("System.UInt16"),INT:n("System.Int32"),UINT:n("System.UInt32"),LONG:n("System.Int64"),ULONG:n("System.UInt64"),NINT:n("System.IntPtr"),NUINT:n("System.UIntPtr"),FLOAT:n("System.Single"),DOUBLE:n("System.Double"),POINTER:n("System.IntPtr",s=>s.field("m_value")),VALUE_TYPE:n("System.Decimal"),OBJECT:n("System.Object"),STRING:n("System.String"),CLASS:n("System.Array"),ARRAY:n("System.Void",s=>s.arrayClass),NARRAY:n("System.Void",s=>new e.Class(e.exports.classGetArrayClass(s,2))),GENERIC_INSTANCE:n("System.Int32",s=>s.interfaces.find(o=>o.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:r}),D({...r,VAR:n("System.Action`1",s=>s.generics[0]),MVAR:n("System.Array",s=>s.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function n(r){let s=r.class.fields.filter(o=>!o.isStatic);return s.length==0?["char"]:s.map(o=>o.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:n(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?n(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let n=e.exports.typeGetName(this);try{return n.readUtf8String()}finally{e.free(n)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(n){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(n.object):!!e.exports.typeEquals(this,n)}toString(){return this.name}};u([c],t.prototype,"class",null),u([c],t.prototype,"fridaAlias",null),u([c],t.prototype,"isByReference",null),u([c],t.prototype,"isPrimitive",null),u([c],t.prototype,"name",null),u([c],t.prototype,"object",null),u([c],t.prototype,"enumValue",null),u([c],t,"Enum",null),t=u([E],t),e.Type=t})(d||(d={}));var d;(function(e){class t extends b{type;constructor(n,r){super(n),this.type=r}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(n){return this.tryField(n)??f(`couldn't find non-static field ${n} in hierarchy of class ${this.type.name}`)}method(n,r=-1){return this.tryMethod(n,r)??f(`couldn't find non-static method ${n} in hierarchy of class ${this.type.name}`)}tryField(n){let r=this.type.class.tryField(n);if(r?.isStatic){for(let s of this.type.class.hierarchy())for(let o of s.fields)if(o.name==n&&!o.isStatic)return o.bind(this);return}return r?.bind(this)}tryMethod(n,r=-1){let s=this.type.class.tryMethod(n,r);if(s?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.methods)if(i.name==n&&!i.isStatic&&(r<0||i.parameterCount==r))return i.bind(this);return}return s?.bind(this)}toString(){let n=this.method("ToString",0);return this.isNull()?"null":n.class.isValueType?n.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=t})(d||(d={}));globalThis.Il2Cpp=d;var N=class{config;errorCounts=new Map;retryHistory=new Map;lastErrorTime=new Map;constructor(t){this.config={maxRetries:5,retryDelay:1e3,enableRetryBackoff:!0,maxBackoffDelay:1e4,ignoreKnownErrors:!0,logErrorDetails:!0,...t}}classifyError(t){let n=(t instanceof Error?t.message:String(t)).toLowerCase();return n.includes("breakpoint triggered")||n.includes("breakpoint")?"breakpoint_triggered":n.includes("access violation")||n.includes("segmentation fault")||n.includes("sigsegv")?"access_violation":n.includes("abort was called")||n.includes("abort()")||n.includes("sigabrt")?"abort_called":(n.includes("invalid pointer")||n.includes("null pointer")||n.includes("bad pointer")||n.includes("timeout")||n.includes("timed out"),"unknown_error")}shouldIgnoreError(t){return this.config.ignoreKnownErrors?["breakpoint_triggered","access_violation","abort_called","illegal_instruction"].includes(t):!1}calculateRetryDelay(t){if(!this.config.enableRetryBackoff)return this.config.retryDelay;let a=this.config.retryDelay*Math.pow(2,t-1);return Math.min(a,this.config.maxBackoffDelay)}shouldRetry(t,a){if((this.errorCounts.get(t)||0)>=this.config.maxRetries||a==="unknown_error"&&this.config.ignoreKnownErrors)return!1;let r=this.lastErrorTime.get(t);return!(r&&Date.now()-r<100)}recordRetryAttempt(t,a,n){let r=this.errorCounts.get(t)||0,s=n?0:r+1;this.errorCounts.set(t,s),this.lastErrorTime.set(t,Date.now());let o=this.retryHistory.get(t)||[],i={attemptNumber:r+1,timestamp:Date.now(),errorType:a,success:n,delay:this.calculateRetryDelay(r+1)};o.push(i),o.length>10&&o.shift(),this.retryHistory.set(t,o)}async executeWithProtection(t,a,n){let r=null;for(let s=1;s<=this.config.maxRetries+1;s++)try{let o=await a();return this.recordRetryAttempt(t,"unknown_error",!0),s>1&&this.config.logErrorDetails&&console.log(`[+] ${n||t}: Succeeded on attempt ${s}`),o}catch(o){r=o;let i=this.classifyError(o);if(this.recordRetryAttempt(t,i,!1),this.config.logErrorDetails){let l=o instanceof Error?o.message:String(o);console.log(`[!] ${n||t}: Attempt ${s} failed - ${i}: ${l}`)}if(this.shouldIgnoreError(i))return this.config.logErrorDetails&&console.log(`[~] ${n||t}: Ignoring known anti-debug error (${i})`),null;if(s<=this.config.maxRetries&&this.shouldRetry(t,i)){let l=this.calculateRetryDelay(s);this.config.logErrorDetails&&console.log(`[*] ${n||t}: Retrying in ${l}ms (attempt ${s+1}/${this.config.maxRetries+1})`),await new Promise(h=>setTimeout(h,l));continue}break}if(this.config.logErrorDetails){let s=this.classifyError(r);console.log(`[\u274C] ${n||t}: All attempts failed - final error: ${s}`)}return null}validateInstancePointer(t,a){let n=`validate_${t.toString()}`;try{if(t.isNull())return!1;let r=t.readU32();return!0}catch(r){let s=this.classifyError(r);return this.shouldIgnoreError(s)?(this.config.logErrorDetails&&console.log(`[~] ${a||"validateInstance"}: Ignoring validation error (${s}) for ${t}`),!1):(this.config.logErrorDetails&&console.log(`[!] ${a||"validateInstance"}: Validation failed for ${t} - ${s}`),!1)}}getRetryStats(t){return{retryCount:this.errorCounts.get(t)||0,history:this.retryHistory.get(t)||[]}}clearRetryHistory(t){this.errorCounts.delete(t),this.retryHistory.delete(t),this.lastErrorTime.delete(t)}getProtectionStats(){let t=this.errorCounts.size,a=Array.from(this.errorCounts.values()).reduce((r,s)=>r+s,0),n=new Map;for(let r of this.retryHistory.values())for(let s of r)if(!s.success){let o=n.get(s.errorType)||0;n.set(s.errorType,o+1)}return{totalOperations:t,totalRetries:a,errorTypeDistribution:n}}};var G=class{assemblyImage=null;entityControllerClass=null;methods;antiDebugProtection;config;stats;upgradeTrackers=new Map;isInitialized=!1;constructor(t){this.config={maxRetries:5,retryDelay:1e3,upgradeTimeout:1e4,batchSize:10,batchDelay:2e3,enableLogging:!0,logLevel:"normal",...t},this.methods={IsSelected:null,CanUpgrade:null,GetLevel:null,GetMaxLevel:null,GetMaxUpgradeLevel:null,InstantUpgrade:null,Select:null,Unselect:null,GetUniqueId:null,IsUpgrading:null,GetUpgradeTime:null,GetUpgradeCost:null},this.stats={totalInstances:0,selectedInstances:0,upgradeableInstances:0,upgradesPerformed:0,startTime:Date.now(),lastUpdateTime:Date.now()},this.antiDebugProtection=new N({maxRetries:this.config.maxRetries,retryDelay:this.config.retryDelay,ignoreKnownErrors:!0,logErrorDetails:this.config.enableLogging}),this.log("\u{1F680} EntityController Manager initialized")}async initialize(){try{return this.log("\u{1F50D} Initializing EntityController manager..."),await Il2Cpp.perform(()=>{}),this.assemblyImage=Il2Cpp.domain.assembly("Assembly-CSharp").image,this.assemblyImage?(this.entityControllerClass=this.assemblyImage.class("EntityController"),this.entityControllerClass?(this.log("\u2705 EntityController class found"),await this.setupMethods(),this.isInitialized=!0,this.log("\u2705 EntityController manager initialized successfully"),!0):(this.log("\u274C EntityController class not found"),!1)):(this.log("\u274C Assembly-CSharp not found"),!1)}catch(t){return this.log(`\u274C Initialization failed: ${t}`),!1}}async setupMethods(){await this.antiDebugProtection.executeWithProtection("setup_methods",()=>{this.log("\u{1F527} Setting up method references..."),this.methods.IsSelected=this.entityControllerClass.method("IsSelected"),this.methods.CanUpgrade=this.entityControllerClass.method("CanUpgrade"),this.methods.GetLevel=this.entityControllerClass.method("GetLevel"),this.methods.GetMaxLevel=this.entityControllerClass.method("GetMaxLevel"),this.methods.GetMaxUpgradeLevel=this.entityControllerClass.method("GetMaxUpgradeLevel"),this.methods.InstantUpgrade=this.entityControllerClass.method("InstantUpgrade");try{this.methods.Select=this.entityControllerClass.method("Select"),this.methods.Unselect=this.entityControllerClass.method("Unselect"),this.methods.GetUniqueId=this.entityControllerClass.method("get_uniqueId"),this.methods.IsUpgrading=this.entityControllerClass.method("IsUpgrading"),this.methods.GetUpgradeTime=this.entityControllerClass.method("GetUpgradeTime"),this.methods.GetUpgradeCost=this.entityControllerClass.method("GetUpgradeCost")}catch(a){this.log(`\u26A0\uFE0F Some optional methods not found: ${a}`)}this.log("\u2705 Method references setup complete")},"EntityController method setup")}getAllInstances(){if(!this.isInitialized||!this.entityControllerClass)return this.log("\u274C EntityController manager not initialized"),[];try{let t=Il2Cpp.gc.choose(this.entityControllerClass);return this.stats.totalInstances=t.length,this.stats.lastUpdateTime=Date.now(),this.log(`\u{1F50D} Found ${t.length} EntityController instances`),t}catch(t){return this.log(`\u274C Failed to get instances: ${t}`),[]}}async autoUpgradeSelected(){return this.isInitialized?await this.antiDebugProtection.executeWithProtection("auto_upgrade_selected",async()=>{this.log("\u{1F680} Starting auto-upgrade for selected entities...");let n=this.getAllInstances();if(n.length===0)return this.log("\u26A0\uFE0F No EntityController instances found"),0;let r=0,s=0,o=0;this.log(`\u{1F50D} Filtering valid instances from ${n.length} total...`);let i=[];for(let l=0;l<n.length;l++)this.validateInstance(n[l])&&i.push(n[l]),(l+1)%500===0&&this.log(`\u{1F4CA} Validated ${l+1}/${n.length} instances, found ${i.length} valid`);if(this.log(`\u2705 Found ${i.length} valid instances out of ${n.length} total`),i.length===0)return this.log("\u26A0\uFE0F No valid EntityController instances found"),0;for(let l=0;l<i.length;l+=this.config.batchSize){let h=i.slice(l,l+this.config.batchSize);for(let m=0;m<h.length;m++){let g=h[m],y=l+m;try{if(!this.safeInvokeInstanceMethod(g,"IsSelected",[],`Instance ${y} IsSelected`))continue;s++;let T=this.validateUpgradeState(g);if(!T.canUpgrade){this.log(`\u{1F4CB} Entity cannot upgrade: ${T.reason}`,"verbose");continue}o++;let S=await this.upgradeEntity(g);S.success&&(r+=S.upgradeCount,this.stats.upgradesPerformed+=S.upgradeCount)}catch(p){this.log(`\u274C Error processing entity ${y}: ${p}`)}}l+this.config.batchSize<i.length&&await new Promise(m=>setTimeout(m,this.config.batchDelay))}return this.stats.selectedInstances=s,this.stats.upgradeableInstances=o,this.stats.lastUpdateTime=Date.now(),this.log(`\u2705 Auto-upgrade complete: ${r} upgrades performed`),this.log(`\u{1F4CA} Selected: ${s}, Upgradeable: ${o}`),r},"EntityController auto-upgrade")||0:(this.log("\u274C EntityController manager not initialized"),0)}validateInstance(t){try{if(!t||!t.handle||t.handle.isNull())return!1;let a=t.class;if(!a||a.name!=="EntityController")return!1;try{let n=t.method("IsSelected");if(!n||!n.handle||n.handle.isNull())return!1;let r=t.method("GetLevel");if(!r||!r.handle||r.handle.isNull())return!1}catch{return!1}return!0}catch{return!1}}safeGetMethod(t,a){try{if(!this.validateInstance(t))return null;let n=t.method(a);return!n||!n.handle||n.handle.isNull()?null:n}catch{return null}}safeInvokeMethod(t,a=[],n="Unknown"){try{return t?t.invoke(...a):(this.log(`\u26A0\uFE0F ${n}: Method is null`,"verbose"),null)}catch(r){let s=r instanceof Error?r.message:String(r);return s.includes("access violation")||s.includes("abort was called")||s.includes("breakpoint")?(this.log(`\u26A0\uFE0F ${n}: Anti-debug protection triggered - ${s}`,"verbose"),null):(this.log(`\u274C ${n}: Method invocation failed - ${s}`,"verbose"),null)}}safeInvokeInstanceMethod(t,a,n=[],r="Unknown"){let s=`invoke_${a}_${t.handle.toString()}`;return this.antiDebugProtection.executeWithProtection(s,()=>{let o=this.safeGetMethod(t,a);if(!o)throw new Error(`Method ${a} not accessible on instance`);return this.safeInvokeMethod(o,n,r)},`EntityController.${a}`)}validateUpgradeState(t){try{let a=this.safeInvokeInstanceMethod(t,"CanUpgrade",[!0],"CanUpgrade validation"),n=this.safeInvokeInstanceMethod(t,"IsUpgrading",[],"IsUpgrading validation")||!1,r=this.safeInvokeInstanceMethod(t,"GetLevel",[],"GetLevel validation")||0,s=this.safeInvokeInstanceMethod(t,"GetMaxUpgradeLevel",[],"GetMaxUpgradeLevel validation")||0;return n?{canUpgrade:!1,isUpgrading:!0,currentLevel:r,maxLevel:s,reason:"Already upgrading"}:a?r>=s-1?{canUpgrade:!1,isUpgrading:!1,currentLevel:r,maxLevel:s,reason:"At max level or max-1 (leaving final upgrade for manual)"}:{canUpgrade:!0,isUpgrading:!1,currentLevel:r,maxLevel:s,reason:"Ready for upgrade"}:{canUpgrade:!1,isUpgrading:!1,currentLevel:r,maxLevel:s,reason:"Cannot upgrade (resources/requirements not met)"}}catch(a){return{canUpgrade:!1,isUpgrading:!1,currentLevel:0,maxLevel:0,reason:`Validation error: ${a}`}}}async upgradeEntity(t){let a=this.getEntityId(t),n=this.safeInvokeInstanceMethod(t,"GetLevel",[],`${a} GetLevel initial`)||0,r=0,s=`upgrade_entity_${a}`;return await this.antiDebugProtection.executeWithProtection(s,async()=>{let i=this.safeInvokeInstanceMethod(t,"GetMaxUpgradeLevel",[],`${a} GetMaxUpgradeLevel`)||0,l=n;for(;l<i-1&&this.safeInvokeInstanceMethod(t,"CanUpgrade",[!0],`${a} CanUpgrade check`);){this.safeInvokeInstanceMethod(t,"InstantUpgrade",[],`${a} InstantUpgrade`),r++,await new Promise(g=>setTimeout(g,500));let m=this.safeInvokeInstanceMethod(t,"GetLevel",[],`${a} GetLevel check`)||l;if(m<=l){await new Promise(y=>setTimeout(y,1e3));let g=this.safeInvokeInstanceMethod(t,"GetLevel",[],`${a} GetLevel recheck`)||l;if(g<=l){this.log(`\u26A0\uFE0F Upgrade may have failed, level didn't increase: ${l}`);break}l=g}else l=m;this.log(`\u{1F4C8} Entity ${a}: Level ${l}/${i}`,"verbose")}return{success:r>0,newLevel:l,waitTime:0,upgradeCount:r}},`EntityController upgrade for ${a}`)||{success:!1,newLevel:n,waitTime:0,upgradeCount:0}}getEntityId(t){try{let a=this.safeInvokeInstanceMethod(t,"GetUniqueId",[],"GetUniqueId");return a?a.toString():t.handle.toString()}catch{return t.handle.toString()}}getStats(){return{...this.stats}}getConfig(){return{...this.config}}updateConfig(t){this.config={...this.config,...t},this.log("\u2699\uFE0F Configuration updated")}cleanup(){this.upgradeTrackers.clear(),this.log("\u{1F9F9} EntityController manager cleaned up")}log(t,a="normal"){if(!this.config.enableLogging)return;let n={minimal:0,normal:1,verbose:2},r=n[this.config.logLevel];n[a]<=r&&console.log(`[EntityController] ${t}`)}};Il2Cpp.perform(async()=>{console.log("\u{1F9EA} Testing EntityController instance validation...");try{let e=new G({maxRetries:2,retryDelay:3e3,upgradeTimeout:1e4,batchSize:3,batchDelay:8e3,enableLogging:!0,logLevel:"verbose"});if(console.log("\u{1F504} Initializing EntityController manager..."),!await e.initialize()){console.log("\u274C EntityController initialization failed");return}console.log("\u2705 EntityController manager initialized successfully"),console.log("\u{1F50D} Getting EntityController instances...");let a=e.getAllInstances();if(a.length===0){console.log("\u26A0\uFE0F No EntityController instances found");return}console.log(`\u2705 Found ${a.length} EntityController instances`);let n=Math.min(10,a.length);console.log(`
\u{1F9EA} Testing validation on first ${n} instances:`);let r=0,s=0;for(let i=0;i<n;i++){let l=a[i];console.log(`
\u{1F4CB} Testing instance ${i+1}/${n}:`);try{if(await O(l,i)){r++,console.log(`  \u2705 Instance ${i+1}: VALID`);try{let m=l.method("IsSelected");m&&m.handle&&!m.handle.isNull()?console.log(`  \u{1F4CB} Instance ${i+1}: IsSelected method accessible`):console.log(`  \u26A0\uFE0F Instance ${i+1}: IsSelected method not accessible`)}catch(m){console.log(`  \u26A0\uFE0F Instance ${i+1}: Method access failed - ${m}`)}}else s++,console.log(`  \u274C Instance ${i+1}: INVALID`)}catch(h){s++,console.log(`  \u274C Instance ${i+1}: Validation error - ${h}`)}await new Promise(h=>setTimeout(h,1e3))}if(console.log(`
\u{1F4CA} Validation Results:`),console.log(`  Valid instances: ${r}/${n}`),console.log(`  Invalid instances: ${s}/${n}`),console.log(`  Success rate: ${(r/n*100).toFixed(1)}%`),r>0){console.log(`
\u{1F680} Testing conservative auto-upgrade (max 3 instances)...`),e.updateConfig({batchSize:3,batchDelay:1e4,maxRetries:1});let i=await e.autoUpgradeSelected();console.log(`\u2705 Conservative auto-upgrade completed: ${i} upgrades performed`)}let o=e.getStats();console.log(`
\u{1F4CA} Final Statistics:`),console.log(`  Total Instances: ${o.totalInstances}`),console.log(`  Selected Instances: ${o.selectedInstances}`),console.log(`  Upgradeable Instances: ${o.upgradeableInstances}`),console.log(`  Upgrades Performed: ${o.upgradesPerformed}`),console.log(`
\u2705 EntityController validation test completed!`)}catch(e){console.log(`\u274C EntityController validation test failed: ${e}`)}});async function O(e,t){try{if(!e)return console.log(`    \u274C Instance ${t}: Null instance`),!1;if(!e.handle||e.handle.isNull())return console.log(`    \u274C Instance ${t}: Invalid handle`),!1;console.log(`    \u2705 Instance ${t}: Handle valid (${e.handle})`);try{let a=e.class;if(!a)return console.log(`    \u274C Instance ${t}: No class accessible`),!1;if(console.log(`    \u2705 Instance ${t}: Class accessible (${a.name})`),a.name!=="EntityController")return console.log(`    \u274C Instance ${t}: Wrong class (${a.name})`),!1}catch(a){return console.log(`    \u274C Instance ${t}: Class access failed - ${a}`),!1}try{let a=e.method("IsSelected");if(!a)return console.log(`    \u274C Instance ${t}: IsSelected method not found`),!1;if(!a.handle||a.handle.isNull())return console.log(`    \u274C Instance ${t}: IsSelected method handle invalid`),!1;console.log(`    \u2705 Instance ${t}: IsSelected method accessible`)}catch(a){return console.log(`    \u274C Instance ${t}: Method access test failed - ${a}`),!1}return console.log(`    \u2705 Instance ${t}: All validation checks passed`),!0}catch(a){return console.log(`    \u274C Instance ${t}: Validation exception - ${a}`),!1}}globalThis.testEntityValidation=()=>{console.log("\u{1F9EA} Running EntityController validation test...")};globalThis.testInstanceValidation=O;
