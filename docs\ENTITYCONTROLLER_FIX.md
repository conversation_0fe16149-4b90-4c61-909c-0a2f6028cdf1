# EntityController Method Invocation Fix

## Problem Description

The EntityController integration was failing with the error:
```
cannot invoke non-static method GetLevel as it must be invoked through a Il2Cpp.Object, not a Il2Cpp.Class
```

This error occurred because the `safeInvokeMethod` function was incorrectly trying to invoke instance methods using the wrong pattern.

## Root Cause

The original implementation was using:
```typescript
// ❌ INCORRECT - This treats instance methods like static methods
const method = (this.methods as any)[methodName];
return method.invoke(instance, ...args);
```

This pattern is incorrect for instance methods because:
1. `this.methods[methodName]` contains method references obtained from the class
2. When you call `method.invoke(instance, ...args)`, it tries to invoke the method as if it were static
3. IL2CPP instance methods must be invoked **through** the instance, not **on** the instance

## Solution

The fix implements the correct IL2CPP method invocation pattern based on the working `entitycontroller-hook.ts` implementation:

### Correct Pattern: Get Method from Instance, Then Invoke
```typescript
// ✅ CORRECT - Get method from instance, then invoke with args only
const method = instance.method(methodName);
const result = method.invoke(...args);
```

This pattern is implemented in three functions:

1. **`safeGetMethod(instance, methodName)`** - Gets method from instance with validation
2. **`safeInvokeMethod(method, args, context)`** - Invokes method with error handling
3. **`safeInvokeInstanceMethod(instance, methodName, args, context)`** - Combines both with anti-debug protection

## Implementation Details

The fix implements three functions in `src/entity-controller-manager.ts`:

```typescript
// 1. Get method from instance with validation
private safeGetMethod(instance: Il2Cpp.Object, methodName: string): Il2Cpp.Method | null {
    try {
        if (!this.validateInstance(instance)) {
            return null;
        }

        const method = instance.method(methodName);
        if (!method || !method.handle || method.handle.isNull()) {
            return null;
        }

        return method;
    } catch (error) {
        return null;
    }
}

// 2. Invoke method with error handling
private safeInvokeMethod(method: Il2Cpp.Method, args: any[] = [], context: string = "Unknown"): any {
    try {
        if (!method) {
            return null;
        }
        return method.invoke(...args);
    } catch (error) {
        this.log(`❌ ${context}: Method invocation failed - ${error}`);
        return null;
    }
}

// 3. Combined function with anti-debug protection
private safeInvokeInstanceMethod(instance: Il2Cpp.Object, methodName: string, args: any[] = [], context: string = "Unknown"): any {
    const operationId = `invoke_${methodName}_${instance.handle.toString()}`;

    return this.antiDebugProtection.executeWithProtection(
        operationId,
        () => {
            const method = this.safeGetMethod(instance, methodName);
            if (!method) {
                throw new Error(`Method ${methodName} not accessible on instance`);
            }

            return this.safeInvokeMethod(method, args, context);
        },
        `EntityController.${methodName}`
    );
}
```

## Key Differences

| Aspect | Incorrect Pattern | Correct Pattern 1 | Correct Pattern 2 |
|--------|------------------|-------------------|-------------------|
| Method Source | `this.methods[name]` | `instance.method(name)` | `this.methods[name]` |
| Invocation | `method.invoke(instance, ...)` | `method.invoke(...)` | `method.invoke(instance, ...)` |
| Context | Treats as static | Invokes through instance | Uses instance as parameter |

## Testing

A test script `src/test-entity-controller.ts` was created to verify the fix:

```bash
# Build the test
npm run build-test-entity

# Run the test
npm run spawn-test-entity
```

The test verifies:
- EntityController initialization
- Instance discovery
- Method invocation for `IsSelected`, `GetLevel`, `GetMaxUpgradeLevel`, `CanUpgrade`, `IsUpgrading`
- Auto-upgrade functionality

## Reference Implementation

The fix was based on the working patterns found in `src/entitycontroller-hook.ts`:

```typescript
// Pattern 1: Direct method call
const method = instance.method(methodName);
const result = method.invoke(...args);

// Pattern 2: Cached method with instance parameter
const result = this.methods.InstantUpgrade.invoke(instance);
```

## Prevention

To prevent similar issues in the future:

1. **Always use `instance.method(name)` for instance methods** when possible
2. **When using cached methods, pass the instance as the first parameter**
3. **Never treat instance methods like static methods**
4. **Test method invocation patterns with a small test script before full implementation**

## Documentation Updates

- Updated `docs/TYPESCRIPT_IMPLEMENTATION.md` with IL2CPP method invocation best practices
- Added examples of correct and incorrect patterns
- Documented the EntityController manager's dual-pattern approach

## Files Modified

- `src/entity-controller-manager.ts` - Fixed `safeInvokeMethod` function
- `src/test-entity-controller.ts` - Created test script
- `docs/TYPESCRIPT_IMPLEMENTATION.md` - Added best practices section
- `docs/ENTITYCONTROLLER_FIX.md` - This documentation
- `package.json` - Added build and spawn scripts for test

The fix ensures reliable EntityController automation with proper IL2CPP method invocation patterns.
