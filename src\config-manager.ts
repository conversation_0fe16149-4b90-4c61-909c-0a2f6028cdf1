/**
 * Configuration and State Management for GoodyHutHelper
 * Handles cleanup flags, configuration objects, and automation state
 */

import {
    GoodyHutAutomationConfig,
    BatchConfig,
    ErrorHandlingConfig,
    EntityControllerConfig,
    AutomationStats,
    PhaseTracker,
    BatchStats
} from './interfaces/goody-hut-interfaces';

/**
 * Configuration Manager for GoodyHutHelper automation
 */
export class ConfigManager {
    private config: GoodyHutAutomationConfig;
    private defaultConfig: GoodyHutAutomationConfig;

    constructor(customConfig?: Partial<GoodyHutAutomationConfig>) {
        this.defaultConfig = this.createDefaultConfig();
        this.config = this.mergeConfigs(this.defaultConfig, customConfig || {});
    }

    /**
     * Create default configuration
     */
    private createDefaultConfig(): GoodyHutAutomationConfig {
        return {
            batch: {
                batchSize: 1, // Real-time processing by default
                batchInterval: 30000, // 30 seconds
                retryLimit: 3,
                discoveryTimeout: 10000, // 10 seconds
                maxWaitTime: 60000 // 60 seconds
            },
            collection: {
                realTimeEnabled: true,
                batchEnabled: true,
                cleanupEnabled: true,
                maxCollections: 1000
            },
            logging: {
                debugLevel: 'normal',
                logBatchDetails: true,
                logErrorDetails: true
            },
            errorHandling: {
                maxRetries: 3,
                retryDelay: 1000,
                ignoreAntiDebugErrors: true,
                logErrorDetails: true,
                continueOnError: true
            },
            module: {
                name: 'libil2cpp.so',
                maxWaitTime: 30000,
                checkInterval: 500
            },
            entityController: {
                maxRetries: 5,
                retryDelay: 1000,
                upgradeTimeout: 10000,
                batchSize: 10,
                batchDelay: 2000,
                enableLogging: true,
                logLevel: 'normal'
            }
        };
    }

    /**
     * Merge custom configuration with defaults
     */
    private mergeConfigs(
        defaultConfig: GoodyHutAutomationConfig,
        customConfig: Partial<GoodyHutAutomationConfig>
    ): GoodyHutAutomationConfig {
        return {
            batch: { ...defaultConfig.batch, ...customConfig.batch },
            collection: { ...defaultConfig.collection, ...customConfig.collection },
            logging: { ...defaultConfig.logging, ...customConfig.logging },
            errorHandling: { ...defaultConfig.errorHandling, ...customConfig.errorHandling },
            module: { ...defaultConfig.module, ...customConfig.module },
            entityController: { ...defaultConfig.entityController!, ...(customConfig.entityController || {}) }
        };
    }

    /**
     * Get current configuration
     */
    public getConfig(): GoodyHutAutomationConfig {
        return { ...this.config };
    }

    /**
     * Get batch configuration
     */
    public getBatchConfig(): BatchConfig {
        return { ...this.config.batch };
    }

    /**
     * Get collection configuration
     */
    public getCollectionConfig(): GoodyHutAutomationConfig['collection'] {
        return { ...this.config.collection };
    }

    /**
     * Get logging configuration
     */
    public getLoggingConfig(): GoodyHutAutomationConfig['logging'] {
        return { ...this.config.logging };
    }

    /**
     * Get module configuration
     */
    public getModuleConfig(): GoodyHutAutomationConfig['module'] {
        return { ...this.config.module };
    }

    /**
     * Get EntityController configuration
     */
    public getEntityControllerConfig(): EntityControllerConfig {
        return { ...this.config.entityController! };
    }

    /**
     * Get error handling configuration
     */
    public getErrorHandlingConfig(): ErrorHandlingConfig {
        return { ...this.config.errorHandling };
    }

    /**
     * Update batch configuration
     */
    public updateBatchConfig(updates: Partial<BatchConfig>): void {
        this.config.batch = { ...this.config.batch, ...updates };
        console.log("[*] Batch configuration updated:", updates);
    }

    /**
     * Update collection configuration
     */
    public updateCollectionConfig(updates: Partial<GoodyHutAutomationConfig['collection']>): void {
        this.config.collection = { ...this.config.collection, ...updates };
        console.log("[*] Collection configuration updated:", updates);
    }

    /**
     * Update logging configuration
     */
    public updateLoggingConfig(updates: Partial<GoodyHutAutomationConfig['logging']>): void {
        this.config.logging = { ...this.config.logging, ...updates };
        console.log("[*] Logging configuration updated:", updates);
    }

    /**
     * Update module configuration
     */
    public updateModuleConfig(updates: Partial<GoodyHutAutomationConfig['module']>): void {
        this.config.module = { ...this.config.module, ...updates };
        console.log("[*] Module configuration updated:", updates);
    }

    /**
     * Update EntityController configuration
     */
    public updateEntityControllerConfig(updates: Partial<EntityControllerConfig>): void {
        if (this.config.entityController) {
            this.config.entityController = { ...this.config.entityController, ...updates };
            console.log("[*] EntityController configuration updated:", updates);
        }
    }

    /**
     * Update error handling configuration
     */
    public updateErrorHandlingConfig(updates: Partial<ErrorHandlingConfig>): void {
        this.config.errorHandling = { ...this.config.errorHandling, ...updates };
        console.log("[*] Error handling configuration updated:", updates);
    }

    /**
     * Reset configuration to defaults
     */
    public resetToDefaults(): void {
        this.config = { ...this.defaultConfig };
        console.log("[+] Configuration reset to defaults");
    }

    /**
     * Validate configuration values
     */
    public validateConfig(): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        // Validate batch configuration
        if (this.config.batch.batchSize < 1) {
            errors.push("Batch size must be at least 1");
        }
        if (this.config.batch.batchInterval < 1000) {
            errors.push("Batch interval must be at least 1000ms");
        }
        if (this.config.batch.retryLimit < 0) {
            errors.push("Retry limit cannot be negative");
        }
        if (this.config.batch.discoveryTimeout < 1000) {
            errors.push("Discovery timeout must be at least 1000ms");
        }

        // Validate collection configuration
        if (this.config.collection.maxCollections < 1) {
            errors.push("Max collections must be at least 1");
        }

        // Validate error handling configuration
        if (this.config.errorHandling.maxRetries < 0) {
            errors.push("Max retries cannot be negative");
        }
        if (this.config.errorHandling.retryDelay < 0) {
            errors.push("Retry delay cannot be negative");
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Get configuration summary for logging
     */
    public getConfigSummary(): string {
        const config = this.config;
        return [
            "=== CONFIGURATION SUMMARY ===",
            `Batch Processing:`,
            `  - Batch Size: ${config.batch.batchSize} (${config.batch.batchSize === 1 ? 'Real-time' : 'Batch'} mode)`,
            `  - Batch Interval: ${config.batch.batchInterval / 1000}s`,
            `  - Retry Limit: ${config.batch.retryLimit}`,
            `  - Discovery Timeout: ${config.batch.discoveryTimeout / 1000}s`,
            `Collection:`,
            `  - Real-time: ${config.collection.realTimeEnabled ? 'ENABLED' : 'DISABLED'}`,
            `  - Batch: ${config.collection.batchEnabled ? 'ENABLED' : 'DISABLED'}`,
            `  - Cleanup: ${config.collection.cleanupEnabled ? 'ENABLED' : 'DISABLED'}`,
            `  - Max Collections: ${config.collection.maxCollections}`,
            `Logging:`,
            `  - Debug Level: ${config.logging.debugLevel}`,
            `  - Batch Details: ${config.logging.logBatchDetails ? 'ON' : 'OFF'}`,
            `  - Error Details: ${config.logging.logErrorDetails ? 'ON' : 'OFF'}`,
            `Error Handling:`,
            `  - Max Retries: ${config.errorHandling.maxRetries}`,
            `  - Retry Delay: ${config.errorHandling.retryDelay}ms`,
            `  - Ignore Anti-Debug: ${config.errorHandling.ignoreAntiDebugErrors ? 'ON' : 'OFF'}`,
            `  - Continue on Error: ${config.errorHandling.continueOnError ? 'ON' : 'OFF'}`,
            `Module:`,
            `  - Name: ${config.module.name}`,
            `  - Max Wait Time: ${config.module.maxWaitTime / 1000}s`,
            `  - Check Interval: ${config.module.checkInterval}ms`,
            "=============================="
        ].join('\n');
    }
}