/**
 * GoodyHutHelper TypeScript Implementation
 * Frida IL2CPP Bridge Integration for Dominations Game Automation
 * Converts JavaScript automation to TypeScript with enhanced type safety
 */

import "frida-il2cpp-bridge";
import {
    GoodyHutHelperMethods,
    GoodyHutHelperConfig,
    InstanceTracker,
    BatchConfig,
    BatchStats,
    BatchItem,
    AutomationStats,
    PhaseTracker,
    IL2CPPModuleInfo,
    MethodAddresses,
    ErrorHandlingConfig,
    AntiDebugError,
    ErrorTracker,
    RPCExports,
    GoodyHutAutomationConfig,
    InitializationResult,
    MemoryValidationResult,
    InstanceValidationResult,
    CollectionResult,
    BatchProcessingResult
} from './interfaces/goody-hut-interfaces';
import { MethodHookManager } from './method-hooks';
import { BatchProcessor } from './batch-processor';
import { ConfigManager } from './config-manager';
import { StateManager } from './state-manager';

/**
 * Main GoodyHutHelper automation class with TypeScript type safety
 */
export class GoodyHutHelper {
    // ========================================================================
    // Core Properties
    // ========================================================================

    private assemblyImage: Il2Cpp.Image | null = null;
    private goodyHutHelperClass: Il2Cpp.Class | null = null;
    private methods: GoodyHutHelperMethods = {
        CanCollect: null,
        FinishCollect: null,
        Config: null,
        SellRuins: null,
        Update: null,
        GetRewardType: null,
        GetRewardAmount: null,
        GetCollectTime: null
    };

    private moduleInfo: IL2CPPModuleInfo = {
        module: null,
        name: '',
        base: ptr(0),
        size: 0,
        isValid: false,
        loadTime: 0
    };

    private methodAddresses: MethodAddresses = {
        CanCollect: ptr(0),
        FinishCollect: ptr(0),
        Update: ptr(0),
        Config: ptr(0),
        SellRuins: ptr(0),
        GetRewardType: ptr(0),
        GetRewardAmount: ptr(0)
    };

    // ========================================================================
    // Configuration and State
    // ========================================================================

    private config: GoodyHutAutomationConfig;
    private stats: AutomationStats = {
        totalInstances: 0,
        validInstances: 0,
        invalidInstances: 0,
        upgradeableInstances: 0,
        collectionsPerformed: 0,
        startTime: Date.now(),
        lastUpdateTime: Date.now()
    };

    private batchStats: BatchStats = {
        totalDiscoveryScans: 0,
        discoveredInstances: 0,
        processedInstances: 0,
        failedInstances: 0,
        batchNumber: 0,
        isProcessingBatch: false,
        currentPhase: 1,
        lastDiscoveryTime: 0,
        discoveryCompleteTime: 0
    };

    private phaseTracker: PhaseTracker = {
        currentPhase: 1,
        phaseStartTime: Date.now(),
        discoveryComplete: false
    };

    // ========================================================================
    // Data Structures for Batch Processing
    // ========================================================================

    private discoveredInstances: Map<string, InstanceTracker> = new Map();
    private processedInstances: Set<string> = new Set();
    private failedInstances: Map<string, InstanceTracker> = new Map();
    private errorTrackers: Map<string, ErrorTracker> = new Map();

    // Hook manager and batch processor
    private hookManager: MethodHookManager | null = null;
    private batchProcessor: BatchProcessor | null = null;

    // Configuration and state managers
    private configManager: ConfigManager;
    private stateManager: StateManager;

    // ========================================================================
    // Timers and Intervals
    // ========================================================================

    private moduleCheckInterval: NodeJS.Timeout | null = null;
    private batchProcessingInterval: NodeJS.Timeout | null = null;
    private statusMonitoringInterval: NodeJS.Timeout | null = null;
    private cleanupInterval: NodeJS.Timeout | null = null;

    // ========================================================================
    // Constructor
    // ========================================================================

    constructor(customConfig?: Partial<GoodyHutAutomationConfig>) {
        console.log("🚀 Starting GoodyHutHelper TypeScript Implementation...");

        // Initialize managers
        this.configManager = new ConfigManager(customConfig);
        this.stateManager = new StateManager();

        // Initialize configuration from manager
        this.config = this.configManager.getConfig();

        console.log("✅ GoodyHutHelper instance created with configuration:");
        console.log(`   - Batch size: ${this.config.batch.batchSize}`);
        console.log(`   - Real-time collection: ${this.config.collection.realTimeEnabled}`);
        console.log(`   - Debug level: ${this.config.logging.debugLevel}`);
    }

    // ========================================================================
    // Configuration Management
    // ========================================================================

    /**
     * Create default configuration based on original JavaScript implementation
     */
    private createDefaultConfig(): GoodyHutAutomationConfig {
        return {
            batch: {
                batchSize: 1,
                batchInterval: 30000, // 30 seconds
                retryLimit: 3,
                discoveryTimeout: 10000, // 10 seconds
                maxWaitTime: 60000 // 60 seconds
            },
            errorHandling: {
                maxRetries: 3,
                retryDelay: 1000,
                ignoreAntiDebugErrors: true,
                logErrorDetails: true,
                continueOnError: true
            },
            module: {
                name: 'libil2cpp.so',
                maxWaitTime: 60000,
                checkInterval: 500
            },
            collection: {
                realTimeEnabled: true,
                batchEnabled: true,
                cleanupEnabled: true,
                maxCollections: 1000
            },
            logging: {
                debugLevel: 'normal',
                logBatchDetails: true,
                logErrorDetails: true
            }
        };
    }

    // ========================================================================
    // Initialization and Module Detection
    // ========================================================================

    /**
     * Initialize the GoodyHutHelper automation system
     */
    public async initialize(): Promise<InitializationResult> {
        try {
            console.log("🔍 Initializing GoodyHutHelper automation system...");

            // Step 1: Wait for and detect IL2CPP module
            const moduleDetected = await this.detectIL2CPPModule();
            if (!moduleDetected) {
                return {
                    success: false,
                    module: this.moduleInfo,
                    methods: this.methods,
                    addresses: this.methodAddresses,
                    errorMessage: "Failed to detect IL2CPP module"
                };
            }

            // Step 2: IL2CPP bridge is automatically initialized when available
            console.log("🔗 IL2CPP bridge ready...");

            // Step 3: Find GoodyHutHelper class using IL2CPP bridge
            const classFound = await this.findGoodyHutHelperClass();
            if (!classFound) {
                return {
                    success: false,
                    module: this.moduleInfo,
                    methods: this.methods,
                    addresses: this.methodAddresses,
                    errorMessage: "Failed to find GoodyHutHelper class"
                };
            }

            // Step 4: Setup method references
            this.setupMethodReferences();

            // Step 5: Calculate method addresses (fallback for direct hooking)
            this.calculateMethodAddresses();

            // Step 6: Setup hooks
            this.setupMethodHooks();

            // Step 7: Initialize batch processing system
            this.initializeBatchProcessing();

            // Step 8: Setup RPC exports
            this.setupRPCExports();

            console.log("✅ GoodyHutHelper initialization complete!");

            return {
                success: true,
                module: this.moduleInfo,
                methods: this.methods,
                addresses: this.methodAddresses
            };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.log(`❌ Initialization failed: ${errorMessage}`);

            return {
                success: false,
                module: this.moduleInfo,
                methods: this.methods,
                addresses: this.methodAddresses,
                errorMessage
            };
        }
    }

    /**
     * Detect IL2CPP module with enhanced logic from original JavaScript
     */
    private async detectIL2CPPModule(): Promise<boolean> {
        return new Promise((resolve) => {
            console.log("[*] Waiting for libil2cpp.so to load...");

            const startTime = Date.now();
            let moduleFound = false;

            const checkForIL2CPP = () => {
                // Check for libil2cpp.so first
                Process.enumerateModules().forEach((module) => {
                    if (module.name === this.config.module.name) {
                        this.moduleInfo = {
                            module,
                            name: module.name,
                            base: module.base,
                            size: module.size,
                            isValid: true,
                            loadTime: Date.now()
                        };
                        moduleFound = true;
                        console.log(`[+] Found ${module.name} at: ${module.base} (size: ${(module.size/1024/1024).toFixed(1)}MB)`);
                        return;
                    }
                });

                if (moduleFound) {
                    if (this.moduleCheckInterval) {
                        clearInterval(this.moduleCheckInterval);
                        this.moduleCheckInterval = null;
                    }
                    console.log("[+] IL2CPP module loaded successfully!");
                    resolve(true);
                    return;
                }

                const elapsed = Date.now() - startTime;
                if (elapsed > this.config.module.maxWaitTime) {
                    if (this.moduleCheckInterval) {
                        clearInterval(this.moduleCheckInterval);
                        this.moduleCheckInterval = null;
                    }
                    console.log(`[-] Timeout waiting for ${this.config.module.name} to load after ${(this.config.module.maxWaitTime/1000)} seconds`);
                    this.listAvailableModules();
                    resolve(false);
                    return;
                }

                if (elapsed % 5000 < this.config.module.checkInterval) {
                    console.log(`[*] Still waiting for ${this.config.module.name}... (${(elapsed/1000).toFixed(1)}s elapsed)`);
                }
            };

            // Check immediately
            checkForIL2CPP();

            // If not found, check periodically
            if (!moduleFound) {
                this.moduleCheckInterval = setInterval(checkForIL2CPP, this.config.module.checkInterval);
            }
        });
    }

    /**
     * Find GoodyHutHelper class using IL2CPP bridge
     */
    private async findGoodyHutHelperClass(): Promise<boolean> {
        try {
            // Get Assembly-CSharp
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                this.listAvailableAssemblies();
                return false;
            }

            console.log("✅ Assembly-CSharp found");

            // Find GoodyHutHelper class
            this.goodyHutHelperClass = this.assemblyImage.class("GoodyHutHelper");
            if (!this.goodyHutHelperClass) {
                console.log("❌ GoodyHutHelper class not found");
                this.listAvailableClasses();
                return false;
            }

            console.log("✅ GoodyHutHelper class found");
            console.log(`📋 Class info: ${this.goodyHutHelperClass.name}`);

            return true;

        } catch (error) {
            console.log(`❌ Failed to find GoodyHutHelper class: ${error}`);
            return false;
        }
    }

    /**
     * Setup method references using IL2CPP bridge
     */
    private setupMethodReferences(): void {
        try {
            console.log("🔧 Setting up method references...");

            if (!this.goodyHutHelperClass) {
                console.log("❌ GoodyHutHelper class not available for method setup");
                return;
            }

            // Core methods
            this.methods.CanCollect = this.goodyHutHelperClass.method("CanCollect");
            this.methods.FinishCollect = this.goodyHutHelperClass.method("FinishCollect");
            this.methods.Config = this.goodyHutHelperClass.method("Config");
            this.methods.Update = this.goodyHutHelperClass.method("Update");

            // Additional methods
            try {
                this.methods.SellRuins = this.goodyHutHelperClass.method("SellRuins");
                this.methods.GetRewardType = this.goodyHutHelperClass.method("GetRewardType");
                this.methods.GetRewardAmount = this.goodyHutHelperClass.method("GetRewardAmount");
                this.methods.GetCollectTime = this.goodyHutHelperClass.method("GetCollectTime");
            } catch (error) {
                console.log(`⚠️ Some optional methods not found: ${error}`);
            }

            // Verify methods found
            Object.entries(this.methods).forEach(([methodName, method]) => {
                if (method) {
                    console.log(`✅ Found method: ${methodName}`);
                } else {
                    console.log(`⚠️ Method not found: ${methodName}`);
                }
            });

        } catch (error) {
            console.log(`❌ Method setup failed: ${error}`);
        }
    }

    /**
     * Calculate method addresses for fallback direct hooking
     */
    private calculateMethodAddresses(): void {
        if (!this.moduleInfo.isValid || !this.moduleInfo.base) {
            console.log("❌ Cannot calculate method addresses - invalid module info");
            return;
        }

        console.log("🧮 Calculating method addresses...");

        // RVA offsets from original JavaScript (fallback addresses)
        const rvaOffsets = {
            CanCollect: 0x208E064,
            FinishCollect: 0x208C748,
            Update: 0x208DD6C,
            Config: 0x208C370,
            SellRuins: 0x208EC3C,
            GetRewardType: 0x208DA48,
            GetRewardAmount: 0x208E7BC
        };

        // Calculate actual addresses
        this.methodAddresses.CanCollect = this.moduleInfo.base.add(rvaOffsets.CanCollect);
        this.methodAddresses.FinishCollect = this.moduleInfo.base.add(rvaOffsets.FinishCollect);
        this.methodAddresses.Update = this.moduleInfo.base.add(rvaOffsets.Update);
        this.methodAddresses.Config = this.moduleInfo.base.add(rvaOffsets.Config);
        this.methodAddresses.SellRuins = this.moduleInfo.base.add(rvaOffsets.SellRuins);
        this.methodAddresses.GetRewardType = this.moduleInfo.base.add(rvaOffsets.GetRewardType);
        this.methodAddresses.GetRewardAmount = this.moduleInfo.base.add(rvaOffsets.GetRewardAmount);

        console.log("📍 Method addresses calculated:");
        Object.entries(this.methodAddresses).forEach(([methodName, address]) => {
            console.log(`   ${methodName}: ${address}`);
        });
    }

    /**
     * List available modules for debugging
     */
    private listAvailableModules(): void {
        console.log("[-] Available modules:");
        Process.enumerateModules().forEach((module) => {
            if (module.size > 5000000) { // > 5MB
                console.log(`    ${module.name} (${(module.size/1024/1024).toFixed(1)}MB)`);
            }
        });
        console.log("[-] Use setModule('module_name.so') to manually set the correct module");
    }

    /**
     * List available assemblies for debugging
     */
    private listAvailableAssemblies(): void {
        try {
            console.log("💡 Available assemblies:");
            const assemblies = Il2Cpp.domain.assemblies;
            assemblies.slice(0, 10).forEach((assembly, index) => {
                console.log(`   ${index}: ${assembly.name}`);
            });
        } catch (error) {
            console.log("   Could not enumerate assemblies");
        }
    }

    /**
     * List available classes for debugging
     */
    private listAvailableClasses(): void {
        if (!this.assemblyImage) return;

        try {
            console.log("💡 Available classes (first 10):");
            const classes = this.assemblyImage.classes;
            for (let i = 0; i < Math.min(10, classes.length); i++) {
                console.log(`   ${i}: ${classes[i].name}`);
            }

            // Look for classes containing "Goody" or "Hut"
            const goodyClasses = classes.filter(cls =>
                cls.name.toLowerCase().includes('goody') ||
                cls.name.toLowerCase().includes('hut') ||
                cls.name.toLowerCase().includes('collect')
            );

            if (goodyClasses.length > 0) {
                console.log("🔍 Found goody/hut/collect related classes:");
                goodyClasses.forEach((cls, index) => {
                    console.log(`   ${index}: ${cls.name}`);
                });
            }
        } catch (error) {
            console.log("   Could not enumerate classes");
        }
    }

    // ========================================================================
    // Method Hooking System (Placeholder - to be implemented in next task)
    // ========================================================================

    /**
     * Setup method hooks for automation
     */
    private setupMethodHooks(): void {
        console.log("🎣 Setting up method hooks...");

        try {
            // Create hook manager
            this.hookManager = new MethodHookManager(
                this.methods,
                this.methodAddresses,
                this.moduleInfo
            );

            // Setup all hooks
            this.hookManager.setupAllHooks();

            // Register callbacks for hook events
            this.hookManager.onCanCollect((instance, canCollect) => {
                if (canCollect) {
                    this.handleCanCollectEvent(instance);
                }
            });

            this.hookManager.onFinishCollect((instance, success) => {
                if (success) {
                    this.handleFinishCollectEvent(instance);
                }
            });

            this.hookManager.onUpdate((instance) => {
                this.handleUpdateEvent(instance);
            });

            console.log("✅ Method hooks setup complete with callbacks");

        } catch (error) {
            console.log(`❌ Failed to setup method hooks: ${error}`);
        }
    }

    // ========================================================================
    // Hook Event Handlers
    // ========================================================================

    /**
     * Handle CanCollect hook events
     */
    private handleCanCollectEvent(instance: NativePointer): void {
        const instanceId = instance.toString();
        console.log(`🎯 CanCollect event for instance: ${instanceId}`);

        // Track this instance for statistics
        this.stats.totalInstances++;
        this.stats.lastUpdateTime = Date.now();
    }

    /**
     * Handle FinishCollect hook events
     */
    private handleFinishCollectEvent(instance: NativePointer): void {
        const instanceId = instance.toString();
        console.log(`✅ FinishCollect event for instance: ${instanceId}`);

        // Update statistics
        this.stats.collectionsPerformed++;
        this.processedInstances.add(instanceId);
        this.stats.lastUpdateTime = Date.now();
    }

    /**
     * Handle Update hook events for real-time discovery
     */
    private handleUpdateEvent(instance: NativePointer): void {
        const instanceId = instance.toString();

        // Track discovery scans
        this.batchStats.totalDiscoveryScans++;

        // Add to batch processor if available
        if (this.batchProcessor) {
            this.batchProcessor.addDiscoveredInstance(instance, instanceId);
        } else {
            // Fallback: Add to local discovered instances
            if (!this.processedInstances.has(instanceId) && !this.discoveredInstances.has(instanceId)) {
                const tracker: InstanceTracker = {
                    ptr: instance,
                    discoveryTime: Date.now(),
                    retryCount: 0,
                    entityId: instanceId
                };

                this.discoveredInstances.set(instanceId, tracker);
                this.batchStats.discoveredInstances = this.discoveredInstances.size;
            }
        }
    }

    /**
     * Initialize batch processing system
     */
    private initializeBatchProcessing(): void {
        console.log("📦 Initializing batch processing system...");

        try {
            // Create batch processor
            this.batchProcessor = new BatchProcessor(
                this.config.batch,
                this.methods,
                this.methodAddresses
            );

            // Register callbacks
            this.batchProcessor.onBatchComplete((result) => {
                console.log(`✅ Batch ${result.batchNumber} completed: ${result.successCount}/${result.totalProcessed} successful`);
            });

            this.batchProcessor.onPhaseChange((phase) => {
                console.log(`🔄 Phase changed to: ${phase === 1 ? 'DISCOVERY' : 'COLLECTION'}`);
                this.phaseTracker.currentPhase = phase;
            });

            this.batchProcessor.onInstanceProcessed((result) => {
                if (result.success) {
                    this.stats.collectionsPerformed++;
                }
            });

            // Start batch processing
            this.batchProcessor.startBatchProcessing();

            console.log("✅ Batch processing system initialized and started");

        } catch (error) {
            console.log(`❌ Failed to initialize batch processing: ${error}`);
        }
    }

    /**
     * Setup RPC exports for manual control
     */
    private setupRPCExports(): void {
        console.log("🔌 Setting up RPC exports...");
        // Implementation will be added in the RPC exports task
        console.log("⚠️ RPC exports setup - placeholder implementation");
    }

    // ========================================================================
    // Public API Methods
    // ========================================================================

    /**
     * Get current automation statistics
     */
    public getStats(): AutomationStats & BatchStats {
        const automationStats = this.stateManager.getAutomationStats();
        const batchStats = this.stateManager.getBatchStats();
        return { ...automationStats, ...batchStats };
    }

    /**
     * Get current configuration
     */
    public getConfig(): GoodyHutAutomationConfig {
        return this.configManager.getConfig();
    }

    // ========================================================================
    // RPC Export Functions (Manual Control)
    // ========================================================================

    /**
     * Get batch processing status
     */
    public getBatchStatus(): void {
        if (this.batchProcessor) {
            this.batchProcessor.printBatchStatus();
        } else {
            console.log("=== BATCH PROCESSING STATUS ===");
            console.log("Batch processor not initialized");
            console.log(`Discovered instances: ${this.discoveredInstances.size}`);
            console.log(`Processed instances: ${this.processedInstances.size}`);
            console.log(`Total scans performed: ${this.batchStats.totalDiscoveryScans}`);
            console.log("====================================");
        }
    }

    /**
     * Clear all processed instances
     */
    public clearProcessedInstances(): void {
        this.processedInstances.clear();
        this.stats.collectionsPerformed = 0;

        if (this.batchProcessor) {
            this.batchProcessor.clearAllQueues();
        }

        console.log("[+] Cleared all processed instances");
    }

    /**
     * Clear all failed instances
     */
    public clearFailedInstances(): void {
        this.failedInstances.clear();
        this.batchStats.failedInstances = 0;

        console.log("[+] Cleared all failed instances");
    }

    /**
     * Force immediate batch processing
     */
    public forceBatchProcess(): void {
        if (this.batchProcessor) {
            const phaseInfo = this.batchProcessor.getPhaseInfo();
            if (phaseInfo.currentPhase === 2) {
                console.log("[*] Forcing immediate batch processing...");
                // Trigger batch processing by calling the processor directly
                this.batchProcessor.processCollectionBatch();
            } else {
                console.log("[*] Cannot force batch processing - still in discovery phase");
            }
        } else {
            console.log("[*] Batch processor not initialized");
        }
    }

    /**
     * Force completion of discovery phase
     */
    public forceCompleteDiscovery(): void {
        if (this.batchProcessor) {
            const phaseInfo = this.batchProcessor.getPhaseInfo();
            if (phaseInfo.currentPhase === 1) {
                console.log("[*] Forcing discovery phase completion...");
                this.batchProcessor.completeDiscoveryPhase();
            } else {
                console.log("[*] Discovery phase already completed");
            }
        } else {
            console.log("[*] Batch processor not initialized");
        }
    }

    /**
     * Reset to discovery phase
     */
    public resetToDiscovery(): void {
        console.log("[*] Resetting to discovery phase...");

        // Reset local state
        this.phaseTracker.currentPhase = 1;
        this.phaseTracker.discoveryComplete = false;
        this.phaseTracker.phaseStartTime = Date.now();

        this.discoveredInstances.clear();
        this.processedInstances.clear();
        this.failedInstances.clear();

        this.batchStats = {
            currentPhase: 1,
            batchNumber: 0,
            isProcessingBatch: false,
            discoveredInstances: 0,
            processedInstances: 0,
            failedInstances: 0,
            totalDiscoveryScans: 0,
            lastDiscoveryTime: Date.now(),
            discoveryCompleteTime: 0
        };

        this.stats.collectionsPerformed = 0;

        // Reset batch processor if available
        if (this.batchProcessor) {
            this.batchProcessor.resetToDiscovery();
        }

        console.log("[+] Reset complete - discovery phase restarted");
    }

    /**
     * Manually set IL2CPP module
     */
    public setModule(moduleName: string): boolean {
        console.log(`[*] Attempting to manually set IL2CPP module to: ${moduleName}`);

        const foundModule = Process.enumerateModules().find(module => module.name === moduleName);

        if (foundModule) {
            this.moduleInfo = {
                name: foundModule.name,
                base: foundModule.base,
                size: foundModule.size,
                module: foundModule,
                isValid: true,
                loadTime: Date.now()
            };

            console.log(`[+] Successfully set IL2CPP module to: ${this.moduleInfo.name}`);
            console.log(`[+] Module base: ${this.moduleInfo.base}`);
            console.log("[+] Reinitializing hooks with new module...");

            // Reinitialize the system
            this.cleanup();
            setTimeout(() => {
                this.initialize();
            }, 1000);

            return true;
        } else {
            console.log(`[-] Module '${moduleName}' not found`);
            console.log("[-] Available modules:");
            Process.enumerateModules().forEach((module) => {
                if (module.size > 1000000) { // > 1MB
                    console.log(`    ${module.name} (${(module.size/1024/1024).toFixed(1)}MB)`);
                }
            });
            return false;
        }
    }

    /**
     * List large modules in the process
     */
    public listLargeModules(): Array<{name: string, size: number, sizeMB: string, base: string}> {
        console.log("[*] Large modules (>1MB) in process:");
        const modules: Array<{name: string, size: number, sizeMB: string, base: string}> = [];

        Process.enumerateModules().forEach((module) => {
            if (module.size > 1000000) { // > 1MB
                modules.push({
                    name: module.name,
                    size: module.size,
                    sizeMB: (module.size/1024/1024).toFixed(1),
                    base: module.base.toString()
                });
            }
        });

        // Sort by size (largest first)
        modules.sort((a, b) => b.size - a.size);

        modules.forEach((mod, index) => {
            console.log(`  ${index + 1}. ${mod.name} (${mod.sizeMB}MB) at ${mod.base}`);
        });

        return modules;
    }

    /**
     * Get comprehensive statistics
     */
    public getDetailedStats(): {
        phase: number;
        discovered: number;
        processed: number;
        failed: number;
        batchNumber: number;
        isProcessing: boolean;
        totalScans: number;
        lastDiscovery: number;
        discoveryComplete: number | null;
    } {
        const batchStats = this.batchProcessor?.getStats() || this.batchStats;
        const phaseInfo = this.batchProcessor?.getPhaseInfo() || this.phaseTracker;

        return {
            phase: phaseInfo.currentPhase,
            discovered: this.discoveredInstances.size,
            processed: this.processedInstances.size,
            failed: this.failedInstances.size,
            batchNumber: batchStats.batchNumber,
            isProcessing: batchStats.isProcessingBatch,
            totalScans: batchStats.totalDiscoveryScans,
            lastDiscovery: batchStats.lastDiscoveryTime,
            discoveryComplete: batchStats.discoveryCompleteTime
        };
    }

    /**
     * Update configuration
     */
    public updateConfig(newConfig: Partial<GoodyHutAutomationConfig>): void {
        this.config = { ...this.config, ...newConfig };
        console.log("✅ Configuration updated");
    }

    /**
     * Cleanup resources and stop automation
     */
    public cleanup(): void {
        console.log("🧹 Cleaning up GoodyHutHelper resources...");

        // Stop batch processor
        if (this.batchProcessor) {
            this.batchProcessor.stopBatchProcessing();
            this.batchProcessor = null;
        }

        // Remove hooks
        if (this.hookManager) {
            this.hookManager.removeAllHooks();
            this.hookManager = null;
        }

        // Clear intervals
        if (this.moduleCheckInterval) {
            clearInterval(this.moduleCheckInterval);
            this.moduleCheckInterval = null;
        }

        if (this.batchProcessingInterval) {
            clearInterval(this.batchProcessingInterval);
            this.batchProcessingInterval = null;
        }

        if (this.statusMonitoringInterval) {
            clearInterval(this.statusMonitoringInterval);
            this.statusMonitoringInterval = null;
        }

        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }

        // Clear data structures
        this.discoveredInstances.clear();
        this.processedInstances.clear();
        this.failedInstances.clear();
        this.errorTrackers.clear();

        console.log("✅ GoodyHutHelper cleanup complete");
    }
}