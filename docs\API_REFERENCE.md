# GoodyHutHelper TypeScript API Reference

## Table of Contents

- [Core Classes](#core-classes)
- [Interfaces](#interfaces)
- [Type Definitions](#type-definitions)
- [RPC Exports](#rpc-exports)
- [Global Functions](#global-functions)

## Core Classes

### GoodyHutHelper

Main automation class providing comprehensive collection automation.

#### Constructor

```typescript
constructor(customConfig?: Partial<GoodyHutAutomationConfig>)
```

**Parameters:**
- `customConfig` (optional): Partial configuration to override defaults

**Example:**
```typescript
const helper = new GoodyHutHelper({
    batch: { batchSize: 10 },
    logging: { debugLevel: 'verbose' }
});
```

#### Methods

##### initialize(): Promise<InitializationResult>

Initializes the automation system, detects IL2CPP module, and sets up method hooks.

**Returns:** Promise resolving to initialization result with success status and error details.

**Example:**
```typescript
const result = await helper.initialize();
if (result.success) {
    console.log('Initialization successful');
} else {
    console.error('Initialization failed:', result.error);
}
```

##### startAutomation(): boolean

Starts the automation process with real-time collection and batch processing.

**Returns:** `true` if automation started successfully, `false` otherwise.

##### stopAutomation(): void

Stops all automation processes and clears timers.

##### getConfig(): GoodyHutAutomationConfig

Gets the current configuration object.

**Returns:** Complete configuration object with all settings.

##### getStats(): AutomationStats & BatchStats

Gets current automation and batch processing statistics.

**Returns:** Combined statistics object with collection counts, timing, and batch information.

##### updateBatchConfig(updates: Partial<BatchConfig>): void

Updates batch processing configuration at runtime.

**Parameters:**
- `updates`: Partial batch configuration with properties to update

**Example:**
```typescript
helper.updateBatchConfig({
    batchSize: 15,
    batchInterval: 3000
});
```

##### forceBatchProcess(): Promise<BatchProcessingResult>

Manually triggers a batch processing cycle.

**Returns:** Promise resolving to batch processing results.

##### resetToDiscovery(): void

Resets automation to discovery phase, clearing all processed instances.

##### cleanup(): void

Performs cleanup of resources, timers, and hooks.

### ConfigManager

Manages automation configuration with validation and runtime updates.

#### Constructor

```typescript
constructor(customConfig?: Partial<GoodyHutAutomationConfig>)
```

#### Methods

##### getConfig(): GoodyHutAutomationConfig

Returns the complete configuration object.

##### validateConfig(config: any): void

Validates configuration object and throws error if invalid.

**Throws:** Error with validation details if configuration is invalid.

##### updateBatchConfig(updates: Partial<BatchConfig>): void

Updates batch processing configuration.

##### getConfigSummary(): string

Returns formatted configuration summary for logging.

### StateManager

Manages automation state, statistics, and instance tracking.

#### Constructor

```typescript
constructor()
```

#### Methods

##### getAutomationStats(): AutomationStats

Returns current automation statistics.

##### recordCollection(): void

Records a successful collection operation.

##### addDiscoveredInstance(instanceId: string, instancePtr: NativePointer): void

Adds a newly discovered instance to tracking.

##### markInstanceProcessed(instanceId: string): void

Marks an instance as successfully processed.

##### getStateSummary(): string

Returns comprehensive state summary for monitoring.

### AntiDebugProtection

Provides error handling and retry logic for anti-debugging protection.

#### Constructor

```typescript
constructor(config?: Partial<AntiDebugConfig>)
```

#### Methods

##### classifyError(error: any): AntiDebugError

Classifies an error into known anti-debugging error types.

**Returns:** Classified error type.

##### executeWithProtection<T>(operationId: string, operation: () => T | Promise<T>, context?: string): Promise<T | null>

Executes an operation with anti-debugging protection and retry logic.

**Parameters:**
- `operationId`: Unique identifier for the operation
- `operation`: Function to execute with protection
- `context`: Optional context for logging

**Returns:** Operation result or null if all attempts failed.

##### validateInstancePointer(instancePtr: NativePointer, context?: string): boolean

Validates that an instance pointer is accessible and valid.

**Returns:** `true` if pointer is valid, `false` otherwise.

## Interfaces

### GoodyHutAutomationConfig

Main configuration interface for the automation system.

```typescript
interface GoodyHutAutomationConfig {
    batch: BatchConfig;
    errorHandling: ErrorHandlingConfig;
    module: {
        name: string;
        maxWaitTime: number;
        checkInterval: number;
    };
    collection: {
        realTimeEnabled: boolean;
        batchEnabled: boolean;
        cleanupEnabled: boolean;
        maxCollections: number;
    };
    logging: {
        debugLevel: 'minimal' | 'normal' | 'verbose';
        logBatchDetails: boolean;
        logErrorDetails: boolean;
    };
}
```

### BatchConfig

Configuration for batch processing operations.

```typescript
interface BatchConfig {
    batchSize: number;          // Instances per batch
    batchInterval: number;      // Time between batches (ms)
    retryLimit: number;         // Max retry attempts
    discoveryTimeout: number;   // Discovery phase timeout (ms)
    maxWaitTime: number;        // Max operation wait time (ms)
}
```

### AutomationStats

Statistics tracking for automation operations.

```typescript
interface AutomationStats {
    totalInstances: number;
    validInstances: number;
    invalidInstances: number;
    upgradeableInstances: number;
    collectionsPerformed: number;
    startTime: number;
    lastUpdateTime: number;
}
```

### BatchStats

Statistics for batch processing operations.

```typescript
interface BatchStats {
    currentPhase: number;
    batchNumber: number;
    isProcessingBatch: boolean;
    discoveredInstances: number;
    processedInstances: number;
    failedInstances: number;
    totalDiscoveryScans: number;
    lastDiscoveryTime: number;
    discoveryCompleteTime: number;
}
```

## Type Definitions

### AntiDebugError

Union type for anti-debugging error classification.

```typescript
type AntiDebugError =
    | 'breakpoint_triggered'
    | 'access_violation'
    | 'illegal_instruction'
    | 'abort_called'
    | 'unknown_error';
```

### InitializationResult

Result object for initialization operations.

```typescript
interface InitializationResult {
    success: boolean;
    error?: string;
    moduleInfo?: IL2CPPModuleInfo;
    methods?: GoodyHutHelperMethods;
}
```

## RPC Exports

The following functions are exported via Frida RPC for external control:

### getBatchStatus(): BatchStats

Returns current batch processing status.

### forceBatchProcess(): Promise<BatchProcessingResult>

Manually triggers batch processing.

### getGoodyHutStats(): AutomationStats & BatchStats

Returns comprehensive automation statistics.

### getGoodyHutConfig(): GoodyHutAutomationConfig

Returns current configuration.

### startGoodyHutAutomation(): boolean

Starts automation if not already running.

### stopGoodyHutAutomation(): void

Stops automation and clears all timers.

### resetToDiscovery(): void

Resets to discovery phase.

### clearProcessedInstances(): void

Clears processed instance tracking.

### updateBatchSize(size: number): void

Updates batch size configuration.

## Global Functions

### getGoodyHutHelper(): GoodyHutHelper | null

Returns the global GoodyHutHelper instance if available.

### getGoodyHutStats(): AutomationStats & BatchStats

Convenience function to get automation statistics.

### getGoodyHutConfig(): GoodyHutAutomationConfig

Convenience function to get current configuration.

## Error Handling

### Error Types

All methods that can fail return appropriate error information:

- **Configuration Errors**: Invalid configuration parameters
- **Initialization Errors**: Module detection or method discovery failures
- **Runtime Errors**: Hook setup failures or processing errors
- **Anti-debugging Errors**: Known game protection mechanisms

### Error Recovery

The system implements automatic error recovery through:

1. **Retry Logic**: Failed operations are retried with exponential backoff
2. **Error Classification**: Errors are classified and handled appropriately
3. **Graceful Degradation**: Known errors are ignored to maintain operation
4. **State Recovery**: Failed instances can be retried in subsequent cycles

## Usage Patterns

### Basic Automation

```typescript
const helper = new GoodyHutHelper();
await helper.initialize();
helper.startAutomation();
```

### Custom Configuration

```typescript
const helper = new GoodyHutHelper({
    batch: { batchSize: 20, batchInterval: 5000 },
    logging: { debugLevel: 'verbose' }
});
```

### Manual Control

```typescript
// Force immediate batch processing
const result = await helper.forceBatchProcess();
console.log(`Processed ${result.processedCount} instances`);

// Reset and restart
helper.resetToDiscovery();
helper.startAutomation();
```

### Monitoring

```typescript
// Get current statistics
const stats = helper.getStats();
console.log(`Collections: ${stats.collectionsPerformed}`);

// Get detailed summary
const summary = helper.getStateSummary();
console.log(summary);
```

## Best Practices

1. **Always initialize before starting automation**
2. **Use appropriate batch sizes for your device performance**
3. **Monitor statistics regularly for optimal performance**
4. **Handle initialization failures gracefully**
5. **Clean up resources when done**
6. **Use verbose logging during development**
7. **Test configuration changes in development environment**
