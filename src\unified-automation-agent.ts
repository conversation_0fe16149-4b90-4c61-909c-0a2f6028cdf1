/**
 * Unified Automation Agent for Dominations
 * Combines GoodyHutHelper and EntityController automation systems
 */

import "frida-il2cpp-bridge";
import { GoodyHutHelper } from './goody-hut-helper';
import { EntityControllerManager } from './entity-controller-manager';
import { 
    GoodyHutAutomationConfig,
    EntityControllerConfig,
    AutomationStats,
    EntityControllerStats
} from './interfaces/goody-hut-interfaces';

/**
 * Unified automation statistics
 */
interface UnifiedAutomationStats {
    goodyHut: AutomationStats;
    entityController: EntityControllerStats;
    startTime: number;
    lastUpdateTime: number;
    totalOperations: number;
}

/**
 * Unified automation agent
 */
export class UnifiedAutomationAgent {
    private goodyHutHelper: GoodyHutHelper;
    private entityControllerManager: EntityControllerManager;
    private config: GoodyHutAutomationConfig;
    private isRunning: boolean = false;
    private automationInterval: NodeJS.Timeout | null = null;
    private stats: UnifiedAutomationStats;
    
    constructor(config?: Partial<GoodyHutAutomationConfig>) {
        console.log("🚀 Initializing Unified Automation Agent...");
        
        // Initialize GoodyHutHelper
        this.goodyHutHelper = new GoodyHutHelper(config);
        this.config = this.goodyHutHelper.getConfig();
        
        // Initialize EntityController with config
        const entityConfig = this.config.entityController || {
            maxRetries: 5,
            retryDelay: 1000,
            upgradeTimeout: 10000,
            batchSize: 10,
            batchDelay: 2000,
            enableLogging: true,
            logLevel: 'normal'
        };
        
        this.entityControllerManager = new EntityControllerManager(entityConfig);
        
        this.stats = {
            goodyHut: {
                totalInstances: 0,
                validInstances: 0,
                invalidInstances: 0,
                upgradeableInstances: 0,
                collectionsPerformed: 0,
                startTime: Date.now(),
                lastUpdateTime: Date.now()
            },
            entityController: {
                totalInstances: 0,
                selectedInstances: 0,
                upgradeableInstances: 0,
                upgradesPerformed: 0,
                startTime: Date.now(),
                lastUpdateTime: Date.now()
            },
            startTime: Date.now(),
            lastUpdateTime: Date.now(),
            totalOperations: 0
        };
        
        console.log("✅ Unified Automation Agent initialized");
    }
    
    /**
     * Initialize both automation systems
     */
    public async initialize(): Promise<boolean> {
        console.log("🔄 Initializing unified automation systems...");
        
        try {
            // Initialize GoodyHutHelper
            const goodyHutResult = await this.goodyHutHelper.initialize();
            if (!goodyHutResult.success) {
                console.log("❌ GoodyHutHelper initialization failed:", goodyHutResult.errorMessage);
                return false;
            }
            
            console.log("✅ GoodyHutHelper initialized successfully");
            
            // Initialize EntityController
            const entityControllerResult = await this.entityControllerManager.initialize();
            if (!entityControllerResult) {
                console.log("❌ EntityController initialization failed");
                return false;
            }
            
            console.log("✅ EntityController initialized successfully");
            
            // Setup global functions
            this.setupGlobalFunctions();
            
            console.log("🎯 Unified Automation Agent ready!");
            return true;
            
        } catch (error) {
            console.log("❌ Unified initialization failed:", error);
            return false;
        }
    }
    
    /**
     * Start unified automation
     */
    public startAutomation(): boolean {
        if (this.isRunning) {
            console.log("⚠️ Automation is already running");
            return false;
        }
        
        console.log("🚀 Starting unified automation...");

        // GoodyHutHelper runs automatically after initialization
        console.log("✅ GoodyHutHelper automation is running");
        
        // Start periodic EntityController automation
        this.automationInterval = setInterval(async () => {
            try {
                console.log("🔄 Running EntityController auto-upgrade cycle...");
                const upgrades = await this.entityControllerManager.autoUpgradeSelected();
                
                if (upgrades > 0) {
                    console.log(`✅ EntityController: ${upgrades} upgrades performed`);
                    this.stats.totalOperations += upgrades;
                }
                
                this.updateStats();
                
            } catch (error) {
                console.log("❌ EntityController automation error:", error);
            }
        }, 30000); // Run every 30 seconds
        
        this.isRunning = true;
        console.log("✅ Unified automation started successfully");
        
        // Log status every minute
        setInterval(() => {
            this.logStatus();
        }, 60000);
        
        return true;
    }
    
    /**
     * Stop unified automation
     */
    public stopAutomation(): void {
        console.log("🛑 Stopping unified automation...");

        // Stop EntityController interval
        if (this.automationInterval) {
            clearInterval(this.automationInterval);
            this.automationInterval = null;
        }

        this.isRunning = false;
        console.log("✅ Unified automation stopped");
    }
    
    /**
     * Get unified statistics
     */
    public getStats(): UnifiedAutomationStats {
        this.updateStats();
        return { ...this.stats };
    }
    
    /**
     * Update statistics from both systems
     */
    private updateStats(): void {
        this.stats.goodyHut = this.goodyHutHelper.getStats();
        this.stats.entityController = this.entityControllerManager.getStats();
        this.stats.lastUpdateTime = Date.now();
    }
    
    /**
     * Get configuration
     */
    public getConfig(): GoodyHutAutomationConfig {
        return this.goodyHutHelper.getConfig();
    }
    
    /**
     * Update GoodyHut configuration
     */
    public updateGoodyHutConfig(updates: Partial<GoodyHutAutomationConfig>): void {
        // Update through GoodyHutHelper's updateConfig method
        this.goodyHutHelper.updateConfig(updates);
    }
    
    /**
     * Update EntityController configuration
     */
    public updateEntityControllerConfig(updates: Partial<EntityControllerConfig>): void {
        this.entityControllerManager.updateConfig(updates);
    }
    
    /**
     * Manual GoodyHut operations
     */
    public async forceGoodyHutCollection(): Promise<any> {
        return await this.goodyHutHelper.forceBatchProcess();
    }
    
    /**
     * Manual EntityController operations
     */
    public async forceEntityUpgrade(): Promise<number> {
        return await this.entityControllerManager.autoUpgradeSelected();
    }
    
    /**
     * Reset GoodyHut to discovery phase
     */
    public resetGoodyHutDiscovery(): void {
        this.goodyHutHelper.resetToDiscovery();
    }
    
    /**
     * Get status summary
     */
    public getStatusSummary(): string {
        const stats = this.getStats();
        const uptime = Date.now() - stats.startTime;
        const uptimeStr = `${Math.floor(uptime / 60000)}m ${Math.floor((uptime % 60000) / 1000)}s`;
        
        return [
            "=== UNIFIED AUTOMATION STATUS ===",
            `Uptime: ${uptimeStr}`,
            `Status: ${this.isRunning ? 'RUNNING' : 'STOPPED'}`,
            "",
            "GoodyHut Automation:",
            `  - Collections: ${stats.goodyHut.collectionsPerformed}`,
            `  - Total Instances: ${stats.goodyHut.totalInstances}`,
            `  - Valid Instances: ${stats.goodyHut.validInstances}`,
            "",
            "EntityController Automation:",
            `  - Upgrades: ${stats.entityController.upgradesPerformed}`,
            `  - Selected Entities: ${stats.entityController.selectedInstances}`,
            `  - Upgradeable Entities: ${stats.entityController.upgradeableInstances}`,
            "",
            `Total Operations: ${stats.totalOperations}`,
            "================================="
        ].join('\n');
    }
    
    /**
     * Log current status
     */
    private logStatus(): void {
        if (this.isRunning) {
            console.log(this.getStatusSummary());
        }
    }
    
    /**
     * Setup global functions for external control
     */
    private setupGlobalFunctions(): void {
        console.log("🌐 Setting up unified global functions...");
        
        try {
            // Unified control functions
            (globalThis as any).getUnifiedStats = () => this.getStats();
            (globalThis as any).getUnifiedStatus = () => this.getStatusSummary();
            (globalThis as any).startUnifiedAutomation = () => this.startAutomation();
            (globalThis as any).stopUnifiedAutomation = () => this.stopAutomation();
            
            // GoodyHut specific functions
            (globalThis as any).forceGoodyHutCollection = () => this.forceGoodyHutCollection();
            (globalThis as any).resetGoodyHutDiscovery = () => this.resetGoodyHutDiscovery();
            (globalThis as any).getGoodyHutStats = () => this.goodyHutHelper.getStats();
            
            // EntityController specific functions
            (globalThis as any).forceEntityUpgrade = () => this.forceEntityUpgrade();
            (globalThis as any).getEntityControllerStats = () => this.entityControllerManager.getStats();
            (globalThis as any).getAllEntityInstances = () => this.entityControllerManager.getAllInstances();
            
            console.log("✅ Unified global functions setup complete");
            
        } catch (error) {
            console.log("⚠️ Global functions setup failed:", error);
        }
    }
    
    /**
     * Cleanup resources
     */
    public cleanup(): void {
        console.log("🧹 Cleaning up unified automation...");
        
        this.stopAutomation();
        this.goodyHutHelper.cleanup();
        this.entityControllerManager.cleanup();
        
        console.log("✅ Unified automation cleanup complete");
    }
}

// ============================================================================
// Main Execution
// ============================================================================

Il2Cpp.perform(async () => {
    console.log("🎮 Starting Unified Dominations Automation...");
    
    try {
        // Create unified automation agent
        const unifiedAgent = new UnifiedAutomationAgent({
            batch: {
                batchSize: 10,
                batchInterval: 5000,
                retryLimit: 3,
                discoveryTimeout: 30000,
                maxWaitTime: 60000
            },
            logging: {
                debugLevel: 'normal',
                logBatchDetails: true,
                logErrorDetails: true
            },
            entityController: {
                maxRetries: 3,
                retryDelay: 2000,
                upgradeTimeout: 15000,
                batchSize: 5,
                batchDelay: 5000,
                enableLogging: true,
                logLevel: 'verbose'
            }
        });
        
        // Make agent available globally
        (globalThis as any).unifiedAgent = unifiedAgent;
        
        // Initialize
        const initialized = await unifiedAgent.initialize();
        
        if (initialized) {
            console.log("✅ Unified automation initialized successfully!");
            console.log("💡 Use startUnifiedAutomation() to begin automation");
            console.log("💡 Use getUnifiedStatus() to check status");
            console.log("💡 Use stopUnifiedAutomation() to stop");
            
            // Auto-start automation
            setTimeout(() => {
                console.log("🚀 Auto-starting unified automation...");
                unifiedAgent.startAutomation();
            }, 2000);
            
        } else {
            console.log("❌ Unified automation initialization failed");
        }
        
    } catch (error) {
        console.log("❌ Unified automation setup failed:", error);
    }
});
