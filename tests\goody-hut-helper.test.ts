/**
 * Unit Tests for GoodyHutHelper TypeScript Implementation
 * Tests method discovery, batch processing, error handling, and automation logic
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock Frida globals and IL2CPP bridge
const mockModule = {
    name: 'libil2cpp.so',
    base: ptr('0x12345000'),
    size: 0x1000000,
    path: '/data/app/com.nexonm.dominations.adk/lib/arm64/libil2cpp.so'
};

const mockIl2Cpp = {
    perform: jest.fn((callback: () => void) => callback()),
    domain: {
        assembly: jest.fn(() => ({
            image: {
                class: jest.fn(() => ({
                    method: jest.fn(() => ({
                        implementation: jest.fn(),
                        invoke: jest.fn()
                    }))
                }))
            }
        }))
    }
};

const mockInterceptor = {
    attach: jest.fn(() => ({
        detach: jest.fn()
    }))
};

// Mock global Frida objects
(global as any).Module = {
    enumerateExports: jest.fn(() => []),
    findExportByName: jest.fn(() => ptr('0x12345678')),
    getBaseAddress: jest.fn(() => mockModule.base),
    load: jest.fn(() => mockModule)
};

(global as any).Process = {
    enumerateModules: jest.fn(() => [mockModule]),
    arch: 'arm64',
    platform: 'linux'
};

(global as any).ptr = jest.fn((address: string) => ({
    toString: () => address,
    add: jest.fn((offset: number) => ptr((parseInt(address, 16) + offset).toString(16))),
    readU32: jest.fn(() => 0x12345678),
    writeU32: jest.fn(),
    isNull: jest.fn(() => address === '0x0')
}));

(global as any).Il2Cpp = mockIl2Cpp;
(global as any).Interceptor = mockInterceptor;

// Import the classes to test
import { GoodyHutHelper } from '../src/goody-hut-helper';
import { ConfigManager } from '../src/config-manager';
import { StateManager } from '../src/state-manager';
import { AntiDebugProtection } from '../src/anti-debug-protection';
import { MethodHookManager } from '../src/method-hooks';
import { BatchProcessor } from '../src/batch-processor';

describe('GoodyHutHelper TypeScript Implementation', () => {
    let goodyHutHelper: GoodyHutHelper;
    
    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();
        
        // Create new instance for each test
        goodyHutHelper = new GoodyHutHelper({
            batch: {
                batchSize: 5,
                batchInterval: 1000,
                retryLimit: 3,
                discoveryTimeout: 5000,
                maxWaitTime: 30000
            }
        });
    });
    
    afterEach(() => {
        // Cleanup after each test
        if (goodyHutHelper) {
            goodyHutHelper.cleanup();
        }
    });

    describe('Initialization', () => {
        it('should initialize with default configuration', () => {
            const helper = new GoodyHutHelper();
            const config = helper.getConfig();
            
            expect(config).toBeDefined();
            expect(config.batch.batchSize).toBeGreaterThan(0);
            expect(config.errorHandling.maxRetries).toBeGreaterThan(0);
            expect(config.module.name).toBe('libil2cpp.so');
        });
        
        it('should merge custom configuration with defaults', () => {
            const customConfig = {
                batch: { batchSize: 10 },
                logging: { debugLevel: 'verbose' as const }
            };
            
            const helper = new GoodyHutHelper(customConfig);
            const config = helper.getConfig();
            
            expect(config.batch.batchSize).toBe(10);
            expect(config.logging.debugLevel).toBe('verbose');
            expect(config.errorHandling.maxRetries).toBeGreaterThan(0); // Should have default
        });
    });

    describe('Module Detection', () => {
        it('should detect IL2CPP module successfully', async () => {
            const result = await goodyHutHelper.initialize();
            
            expect(result).toBeDefined();
            expect(result.success).toBe(true);
            expect(Process.enumerateModules).toHaveBeenCalled();
        });
        
        it('should handle module detection timeout', async () => {
            // Mock module enumeration to return empty array
            (Process.enumerateModules as jest.Mock).mockReturnValue([]);
            
            const helper = new GoodyHutHelper({
                module: { maxWaitTime: 100 } // Very short timeout
            });
            
            const result = await helper.initialize();
            
            expect(result.success).toBe(false);
            expect(result.error).toContain('timeout');
        });
    });

    describe('Method Discovery', () => {
        beforeEach(async () => {
            await goodyHutHelper.initialize();
        });
        
        it('should discover IL2CPP methods using bridge', () => {
            // Mock successful method discovery
            mockIl2Cpp.domain.assembly.mockReturnValue({
                image: {
                    class: jest.fn(() => ({
                        method: jest.fn(() => ({
                            implementation: jest.fn(),
                            invoke: jest.fn()
                        }))
                    }))
                }
            });
            
            const methods = goodyHutHelper.getMethods();
            
            expect(methods).toBeDefined();
            expect(mockIl2Cpp.domain.assembly).toHaveBeenCalled();
        });
        
        it('should fallback to RVA addresses when bridge fails', () => {
            // Mock bridge failure
            mockIl2Cpp.domain.assembly.mockImplementation(() => {
                throw new Error('Bridge method failed');
            });
            
            const methods = goodyHutHelper.getMethods();
            
            expect(Module.findExportByName).toHaveBeenCalled();
        });
    });

    describe('Statistics and State Management', () => {
        it('should track automation statistics', () => {
            const stats = goodyHutHelper.getStats();
            
            expect(stats).toBeDefined();
            expect(stats.collectionsPerformed).toBe(0);
            expect(stats.startTime).toBeGreaterThan(0);
        });
        
        it('should update statistics when collections are performed', () => {
            const initialStats = goodyHutHelper.getStats();
            
            // Simulate collection
            goodyHutHelper.recordCollection();
            
            const updatedStats = goodyHutHelper.getStats();
            expect(updatedStats.collectionsPerformed).toBe(initialStats.collectionsPerformed + 1);
        });
    });

    describe('Configuration Management', () => {
        it('should allow configuration updates', () => {
            const newBatchSize = 15;
            
            goodyHutHelper.updateBatchConfig({ batchSize: newBatchSize });
            
            const config = goodyHutHelper.getConfig();
            expect(config.batch.batchSize).toBe(newBatchSize);
        });
        
        it('should validate configuration changes', () => {
            expect(() => {
                goodyHutHelper.updateBatchConfig({ batchSize: -1 });
            }).toThrow();
        });
    });
});

describe('ConfigManager', () => {
    let configManager: ConfigManager;
    
    beforeEach(() => {
        configManager = new ConfigManager();
    });
    
    it('should create default configuration', () => {
        const config = configManager.getConfig();
        
        expect(config.batch.batchSize).toBeGreaterThan(0);
        expect(config.errorHandling.maxRetries).toBeGreaterThan(0);
        expect(config.module.name).toBe('libil2cpp.so');
    });
    
    it('should merge custom configuration', () => {
        const customConfig = {
            batch: { batchSize: 20 },
            logging: { debugLevel: 'minimal' as const }
        };
        
        const manager = new ConfigManager(customConfig);
        const config = manager.getConfig();
        
        expect(config.batch.batchSize).toBe(20);
        expect(config.logging.debugLevel).toBe('minimal');
    });
    
    it('should validate configuration', () => {
        const validConfig = { batch: { batchSize: 5 } };
        const invalidConfig = { batch: { batchSize: -1 } };
        
        expect(() => configManager.validateConfig(validConfig)).not.toThrow();
        expect(() => configManager.validateConfig(invalidConfig)).toThrow();
    });
});

describe('StateManager', () => {
    let stateManager: StateManager;
    
    beforeEach(() => {
        stateManager = new StateManager();
    });
    
    it('should initialize with default state', () => {
        const stats = stateManager.getAutomationStats();
        
        expect(stats.collectionsPerformed).toBe(0);
        expect(stats.totalInstances).toBe(0);
        expect(stats.startTime).toBeGreaterThan(0);
    });
    
    it('should track instance discovery', () => {
        const instancePtr = ptr('0x12345678');
        const instanceId = 'test-instance';
        
        stateManager.addDiscoveredInstance(instanceId, instancePtr);
        
        const discovered = stateManager.getDiscoveredInstances();
        expect(discovered.has(instanceId)).toBe(true);
    });
    
    it('should track processed instances', () => {
        const instanceId = 'test-instance';
        
        stateManager.markInstanceProcessed(instanceId);
        
        const processed = stateManager.getProcessedInstances();
        expect(processed.has(instanceId)).toBe(true);
    });
    
    it('should generate state summary', () => {
        const summary = stateManager.getStateSummary();
        
        expect(summary).toContain('AUTOMATION STATE SUMMARY');
        expect(summary).toContain('Collections Performed');
        expect(summary).toContain('Instance Counts');
    });
});

describe('AntiDebugProtection', () => {
    let antiDebugProtection: AntiDebugProtection;
    
    beforeEach(() => {
        antiDebugProtection = new AntiDebugProtection();
    });
    
    it('should classify error types correctly', () => {
        const breakpointError = new Error('breakpoint triggered');
        const accessError = new Error('access violation occurred');
        const unknownError = new Error('some random error');
        
        expect(antiDebugProtection.classifyError(breakpointError)).toBe('breakpoint_triggered');
        expect(antiDebugProtection.classifyError(accessError)).toBe('access_violation');
        expect(antiDebugProtection.classifyError(unknownError)).toBe('unknown_error');
    });
    
    it('should determine if errors should be ignored', () => {
        expect(antiDebugProtection.shouldIgnoreError('breakpoint_triggered')).toBe(true);
        expect(antiDebugProtection.shouldIgnoreError('access_violation')).toBe(true);
        expect(antiDebugProtection.shouldIgnoreError('unknown_error')).toBe(false);
    });
    
    it('should calculate retry delays with backoff', () => {
        const protection = new AntiDebugProtection({
            retryDelay: 1000,
            enableRetryBackoff: true
        });
        
        expect(protection.calculateRetryDelay(1)).toBe(1000);
        expect(protection.calculateRetryDelay(2)).toBe(2000);
        expect(protection.calculateRetryDelay(3)).toBe(4000);
    });
    
    it('should validate instance pointers', () => {
        const validPtr = ptr('0x12345678');
        const nullPtr = ptr('0x0');

        expect(antiDebugProtection.validateInstancePointer(validPtr)).toBe(true);
        expect(antiDebugProtection.validateInstancePointer(nullPtr)).toBe(false);
    });

    it('should execute operations with protection', async () => {
        let callCount = 0;
        const operation = () => {
            callCount++;
            if (callCount === 1) {
                throw new Error('breakpoint triggered');
            }
            return 'success';
        };

        const result = await antiDebugProtection.executeWithProtection('test-op', operation);

        expect(result).toBe(null); // Should ignore breakpoint error
        expect(callCount).toBe(1);
    });
});

describe('Integration Tests', () => {
    it('should handle complete automation workflow', async () => {
        const helper = new GoodyHutHelper({
            batch: { batchSize: 2, batchInterval: 100 }
        });

        // Initialize
        const initResult = await helper.initialize();
        expect(initResult.success).toBe(true);

        // Start automation
        const startResult = helper.startAutomation();
        expect(startResult).toBe(true);

        // Check initial stats
        const stats = helper.getStats();
        expect(stats.collectionsPerformed).toBe(0);

        // Stop automation
        helper.stopAutomation();

        // Cleanup
        helper.cleanup();
    });

    it('should handle error scenarios gracefully', async () => {
        // Mock module enumeration failure
        (Process.enumerateModules as jest.Mock).mockImplementation(() => {
            throw new Error('Process enumeration failed');
        });

        const helper = new GoodyHutHelper();
        const result = await helper.initialize();

        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
    });
});
