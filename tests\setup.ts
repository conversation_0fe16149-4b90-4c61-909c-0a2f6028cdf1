/**
 * Jest Test Setup for GoodyHutHelper TypeScript Tests
 * Configures global mocks and test environment
 */

// Mock console methods to reduce test noise
const originalConsole = global.console;

beforeAll(() => {
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    debug: jest.fn()
  };
});

afterAll(() => {
  global.console = originalConsole;
});

// Mock setTimeout and setInterval for faster tests
jest.useFakeTimers();

// Global test utilities
(global as any).createMockPointer = (address: string = '0x12345678') => ({
  toString: () => address,
  add: jest.fn((offset: number) => createMockPointer((parseInt(address, 16) + offset).toString(16))),
  readU32: jest.fn(() => 0x12345678),
  writeU32: jest.fn(),
  isNull: jest.fn(() => address === '0x0'),
  equals: jest.fn((other: any) => other.toString() === address)
});

(global as any).createMockModule = (name: string = 'libil2cpp.so') => ({
  name,
  base: createMockPointer('0x12345000'),
  size: 0x1000000,
  path: `/data/app/com.nexonm.dominations.adk/lib/arm64/${name}`
});

// Mock Frida globals that are commonly used
(global as any).rpc = {
  exports: {}
};

(global as any).send = jest.fn();
(global as any).recv = jest.fn();

// Extend Jest matchers for better assertions
expect.extend({
  toBeValidPointer(received: any) {
    const pass = received && typeof received.toString === 'function' && !received.isNull();
    return {
      message: () => `expected ${received} to be a valid pointer`,
      pass
    };
  },
  
  toHaveBeenCalledWithPointer(received: jest.Mock, expectedAddress?: string) {
    const calls = received.mock.calls;
    const pass = calls.some(call => 
      call.some(arg => 
        arg && typeof arg.toString === 'function' && 
        (!expectedAddress || arg.toString() === expectedAddress)
      )
    );
    
    return {
      message: () => `expected function to have been called with a pointer${expectedAddress ? ` (${expectedAddress})` : ''}`,
      pass
    };
  }
});

// Declare custom matchers for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidPointer(): R;
      toHaveBeenCalledWithPointer(expectedAddress?: string): R;
    }
  }
}
