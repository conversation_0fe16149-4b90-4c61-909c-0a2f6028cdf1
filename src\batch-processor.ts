/**
 * Batch Processing System for GoodyHutHelper
 * Two-phase batch processing: Discovery and Collection
 * TypeScript implementation with proper async handling and state management
 */

import "frida-il2cpp-bridge";
import {
    InstanceTracker,
    BatchConfig,
    BatchStats,
    BatchItem,
    PhaseTracker,
    BatchProcessingResult,
    CollectionResult,
    GoodyHutHelperMethods,
    MethodAddresses
} from './interfaces/goody-hut-interfaces';
import { AntiDebugProtection } from './anti-debug-protection';

/**
 * Batch processor for two-phase GoodyHutHelper automation
 */
export class BatchProcessor {
    private config: BatchConfig;
    private stats: BatchStats;
    private phaseTracker: PhaseTracker;
    private methods: GoodyHutHelperMethods;
    private addresses: MethodAddresses;

    // Data structures
    private discoveredInstances: Map<string, InstanceTracker> = new Map();
    private processedInstances: Set<string> = new Set();
    private failedInstances: Map<string, InstanceTracker> = new Map();

    // Processing state
    private isProcessingBatch: boolean = false;
    private batchNumber: number = 0;
    private batchProcessingInterval: NodeJS.Timeout | null = null;
    private statusMonitoringInterval: NodeJS.Timeout | null = null;
    private cleanupInterval: NodeJS.Timeout | null = null;

    // Callbacks
    private onBatchCompleteCallback?: (result: BatchProcessingResult) => void;
    private onPhaseChangeCallback?: (phase: 1 | 2) => void;
    private onInstanceProcessedCallback?: (result: CollectionResult) => void;

    constructor(
        config: BatchConfig,
        methods: GoodyHutHelperMethods,
        addresses: MethodAddresses
    ) {
        this.config = config;
        this.methods = methods;
        this.addresses = addresses;

        // Initialize stats
        this.stats = {
            totalDiscoveryScans: 0,
            discoveredInstances: 0,
            processedInstances: 0,
            failedInstances: 0,
            batchNumber: 0,
            isProcessingBatch: false,
            currentPhase: 1,
            lastDiscoveryTime: Date.now(),
            discoveryCompleteTime: 0
        };

        // Initialize phase tracker
        this.phaseTracker = {
            currentPhase: 1,
            phaseStartTime: Date.now(),
            discoveryComplete: false
        };

        console.log("📦 BatchProcessor initialized with configuration:");
        console.log(`   - Batch size: ${this.config.batchSize}`);
        console.log(`   - Batch interval: ${this.config.batchInterval}ms`);
        console.log(`   - Retry limit: ${this.config.retryLimit}`);
        console.log(`   - Discovery timeout: ${this.config.discoveryTimeout}ms`);
    }

    // ========================================================================
    // Public API
    // ========================================================================

    /**
     * Start the two-phase batch processing system
     */
    public async startBatchProcessing(): Promise<void> {
        console.log("🚀 Starting two-phase batch processing system...");

        // Phase 1: Discovery
        this.startDiscoveryPhase();

        // Phase 2: Collection (will start automatically after discovery)
        this.setupBatchProcessingTimer();

        // Setup monitoring and cleanup
        this.setupStatusMonitoring();
        this.setupCleanupTimer();

        console.log("✅ Batch processing system started");
    }

    /**
     * Stop batch processing and cleanup
     */
    public stopBatchProcessing(): void {
        console.log("🛑 Stopping batch processing system...");

        // Clear intervals
        if (this.batchProcessingInterval) {
            clearInterval(this.batchProcessingInterval);
            this.batchProcessingInterval = null;
        }

        if (this.statusMonitoringInterval) {
            clearInterval(this.statusMonitoringInterval);
            this.statusMonitoringInterval = null;
        }

        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }

        // Reset state
        this.isProcessingBatch = false;

        console.log("✅ Batch processing system stopped");
    }

    /**
     * Add discovered instance to processing queue
     */
    public addDiscoveredInstance(instancePtr: NativePointer, entityId?: string): void {
        const instanceId = instancePtr.toString();

        if (!this.processedInstances.has(instanceId) && !this.discoveredInstances.has(instanceId)) {
            const tracker: InstanceTracker = {
                ptr: instancePtr,
                discoveryTime: Date.now(),
                retryCount: 0,
                entityId: entityId || instanceId
            };

            this.discoveredInstances.set(instanceId, tracker);
            this.stats.discoveredInstances = this.discoveredInstances.size;
            this.stats.lastDiscoveryTime = Date.now();

            if (this.config.batchSize === 1) {
                console.log(`[+] REAL-TIME: Added instance ${instanceId} for immediate processing`);
            }
        }
    }

    /**
     * Force completion of discovery phase
     */
    public forceCompleteDiscovery(): void {
        if (this.phaseTracker.currentPhase === 1) {
            console.log("[*] Forcing discovery phase completion...");
            this.completeDiscoveryPhase();
        } else {
            console.log("[*] Discovery phase already completed");
        }
    }

    /**
     * Force immediate batch processing
     */
    public forceBatchProcess(): void {
        if (this.phaseTracker.currentPhase === 2) {
            console.log("[*] Forcing immediate batch processing...");
            this.processCollectionBatch();
        } else {
            console.log("[*] Cannot force batch processing - still in discovery phase");
        }
    }

    /**
     * Reset to discovery phase
     */
    public resetToDiscovery(): void {
        console.log("[*] Resetting to discovery phase...");

        this.phaseTracker.currentPhase = 1;
        this.phaseTracker.phaseStartTime = Date.now();
        this.phaseTracker.discoveryComplete = false;

        this.discoveredInstances.clear();
        this.processedInstances.clear();
        this.failedInstances.clear();

        this.batchNumber = 0;
        this.isProcessingBatch = false;
        this.stats.lastDiscoveryTime = Date.now();
        this.stats.totalDiscoveryScans = 0;

        // Notify callback
        if (this.onPhaseChangeCallback) {
            this.onPhaseChangeCallback(1);
        }

        console.log("[+] Reset complete - discovery phase restarted");
    }

    // ========================================================================
    // Phase Management
    // ========================================================================

    /**
     * Start Phase 1: Discovery
     */
    private startDiscoveryPhase(): void {
        console.log("[+] ===== PHASE 1: DISCOVERY SCAN STARTED =====");
        console.log("[*] Scanning for ALL collectible GoodyHutHelper instances...");
        console.log(`[*] Discovery will complete after ${this.config.discoveryTimeout/1000} seconds with no new findings`);

        this.phaseTracker.currentPhase = 1;
        this.phaseTracker.phaseStartTime = Date.now();
        this.phaseTracker.discoveryComplete = false;

        this.stats.currentPhase = 1;
        this.stats.lastDiscoveryTime = Date.now();

        // Discovery will be handled by the Update hook in MethodHookManager
        // This method just sets up the phase tracking
    }

    /**
     * Complete discovery phase and start collection
     */
    public completeDiscoveryPhase(): void {
        this.phaseTracker.currentPhase = 2;
        this.phaseTracker.phaseEndTime = Date.now();
        this.phaseTracker.phaseDuration = this.phaseTracker.phaseEndTime - this.phaseTracker.phaseStartTime;
        this.phaseTracker.discoveryComplete = true;

        this.stats.currentPhase = 2;
        this.stats.discoveryCompleteTime = this.phaseTracker.phaseEndTime;

        console.log("[+] ===== PHASE 1: DISCOVERY SCAN COMPLETED =====");
        console.log(`[+] Total collectible instances discovered: ${this.discoveredInstances.size}`);
        console.log(`[+] Total discovery scans performed: ${this.stats.totalDiscoveryScans}`);
        console.log(`[+] Discovery phase duration: ${(this.phaseTracker.phaseDuration / 1000).toFixed(1)} seconds`);

        // Notify callback
        if (this.onPhaseChangeCallback) {
            this.onPhaseChangeCallback(2);
        }

        if (this.discoveredInstances.size === 0) {
            console.log("[*] No collectible ruins found. Monitoring will continue for new instances.");
        } else {
            console.log("[+] ===== PHASE 2: BATCH COLLECTION STARTING =====");
            console.log(`[*] Beginning batch processing of ${this.discoveredInstances.size} instances`);
            console.log(`[*] Batch size: ${this.config.batchSize}, Interval: ${(this.config.batchInterval/1000)} seconds`);

            // Start batch processing immediately
            setTimeout(() => {
                this.processCollectionBatch();
            }, 1000);
        }
    }

    // ========================================================================
    // Batch Processing Core
    // ========================================================================

    /**
     * Process a batch of instances for collection
     */
    public async processCollectionBatch(): Promise<void> {
        // Only process batches in Phase 2
        if (this.phaseTracker.currentPhase !== 2) {
            return;
        }

        if (this.isProcessingBatch) {
            console.log("[*] PHASE 2: Batch processing already in progress, skipping...");
            return;
        }

        this.isProcessingBatch = true;
        this.batchNumber++;
        this.stats.batchNumber = this.batchNumber;
        this.stats.isProcessingBatch = true;

        const batchStartTime = Date.now();

        // Prepare current batch from discovered instances and failed retries
        const batchInstances = this.prepareBatchInstances();

        if (batchInstances.length === 0) {
            this.handleEmptyBatch();
            return;
        }

        console.log(`[+] PHASE 2: Starting Batch #${this.batchNumber} - Processing ${batchInstances.length} instances`);
        console.log(`[*] Batch composition: ${batchInstances.filter(b => b.source === 'discovered').length} discovered, ${batchInstances.filter(b => b.source === 'retry').length} retries`);
        console.log(`[*] Remaining after this batch: ${this.discoveredInstances.size + this.failedInstances.size - batchInstances.length} instances`);

        // Process batch
        const results = await this.processBatchInstances(batchInstances);

        const batchEndTime = Date.now();
        const processingTime = batchEndTime - batchStartTime;

        // Create batch result
        const batchResult: BatchProcessingResult = {
            batchNumber: this.batchNumber,
            totalProcessed: batchInstances.length,
            successCount: results.filter(r => r.success).length,
            failureCount: results.filter(r => !r.success).length,
            processingTime,
            results
        };

        console.log(`[+] Batch #${this.batchNumber} COMPLETED - Successes: ${batchResult.successCount}, Failures: ${batchResult.failureCount}`);
        console.log(`[*] Status - Processed: ${this.processedInstances.size}, Pending: ${this.discoveredInstances.size}, Failed: ${this.failedInstances.size}`);

        // Notify callback
        if (this.onBatchCompleteCallback) {
            this.onBatchCompleteCallback(batchResult);
        }

        this.isProcessingBatch = false;
        this.stats.isProcessingBatch = false;
    }

    /**
     * Prepare batch instances from discovered and failed queues
     */
    private prepareBatchInstances(): BatchItem[] {
        const batchInstances: BatchItem[] = [];

        // Add discovered instances to batch
        const discoveredArray = Array.from(this.discoveredInstances.entries());
        for (let i = 0; i < Math.min(this.config.batchSize, discoveredArray.length); i++) {
            const [instanceId, data] = discoveredArray[i];
            batchInstances.push({id: instanceId, data: data, source: 'discovered'});
            this.discoveredInstances.delete(instanceId);
        }

        // Add failed instances ready for retry
        const now = Date.now();
        const failedArray = Array.from(this.failedInstances.entries());
        const remainingBatchSlots = this.config.batchSize - batchInstances.length;

        for (let i = 0; i < Math.min(remainingBatchSlots, failedArray.length); i++) {
            const [instanceId, failData] = failedArray[i];
            // Retry after 10 seconds and if retry count is under limit
            if (failData.lastFailTime &&
                now - failData.lastFailTime > 10000 &&
                failData.retryCount < this.config.retryLimit) {
                batchInstances.push({id: instanceId, data: failData, source: 'retry'});
                this.failedInstances.delete(instanceId);
            }
        }

        return batchInstances;
    }

    /**
     * Handle empty batch scenario
     */
    private handleEmptyBatch(): void {
        // Check if all processing is complete
        const totalRemaining = this.discoveredInstances.size + this.failedInstances.size;
        if (totalRemaining === 0) {
            console.log("[+] ===== PHASE 2: BATCH COLLECTION COMPLETED =====");
            console.log("[+] All discovered instances have been processed!");
            console.log("[+] Final Statistics:");
            console.log(`    - Total processed: ${this.processedInstances.size}`);
            console.log(`    - Total failed (abandoned): ${Array.from(this.failedInstances.values()).filter(f => f.retryCount >= this.config.retryLimit).length}`);
            console.log(`    - Total batches: ${this.batchNumber}`);
        } else {
            console.log(`[*] PHASE 2: Batch #${this.batchNumber} - No instances ready to process`);
            console.log(`[*] Remaining: ${this.discoveredInstances.size} discovered, ${this.failedInstances.size} failed`);
        }

        this.isProcessingBatch = false;
        this.stats.isProcessingBatch = false;
    }

    /**
     * Process individual batch instances
     */
    private async processBatchInstances(batchInstances: BatchItem[]): Promise<CollectionResult[]> {
        const results: CollectionResult[] = [];

        // Process each instance in the batch sequentially
        for (let index = 0; index < batchInstances.length; index++) {
            const batchItem = batchInstances[index];
            const result = await this.processInstance(batchItem, index + 1, batchInstances.length);
            results.push(result);

            // Small delay between instances to reduce detection
            if (index < batchInstances.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        return results;
    }

    /**
     * Process a single instance
     */
    private async processInstance(batchItem: BatchItem, itemIndex: number, totalItems: number): Promise<CollectionResult> {
        const startTime = Date.now();
        const instanceId = batchItem.id;
        const instanceData = batchItem.data;
        const thisPtr = instanceData.ptr;

        console.log(`[*] Batch #${this.batchNumber} [${itemIndex}/${totalItems}] Processing: ${instanceId}`);

        const result: CollectionResult = {
            success: false,
            instanceId,
            processingTime: 0,
            cleanupSet: false
        };

        try {
            // Validate pointer is still valid
            if (!thisPtr || thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                result.errorMessage = "Invalid pointer";
                return result;
            }

            // Check if instance can still be collected
            const canCollect = await this.checkCanCollect(thisPtr);
            if (!canCollect) {
                result.errorMessage = "Instance no longer collectible";
                result.success = true; // Count as success since it's no longer needed
                return result;
            }

            // Set cleanup flag
            const cleanupSet = await this.setCleanupFlag(thisPtr);
            result.cleanupSet = cleanupSet;

            // Execute collection
            const collectionSuccess = await this.executeFinishCollect(thisPtr);

            if (collectionSuccess) {
                result.success = true;
                this.processedInstances.add(instanceId);
                this.stats.processedInstances = this.processedInstances.size;

                console.log(`[+] Batch #${this.batchNumber} [${itemIndex}/${totalItems}] SUCCESS: ${instanceId}`);

                // Notify callback
                if (this.onInstanceProcessedCallback) {
                    this.onInstanceProcessedCallback(result);
                }
            } else {
                result.errorMessage = "FinishCollect failed";
                this.handleFailedInstance(batchItem);
            }

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            result.errorMessage = errorMessage;

            console.log(`[-] Batch #${this.batchNumber} [${itemIndex}/${totalItems}] FAILED: ${instanceId} - ${errorMessage}`);
            this.handleFailedInstance(batchItem);
        }

        result.processingTime = Date.now() - startTime;
        return result;
    }

    /**
     * Handle failed instance processing
     */
    private handleFailedInstance(batchItem: BatchItem): void {
        const retryCount = batchItem.data.retryCount + 1;

        if (retryCount < this.config.retryLimit) {
            const failedTracker: InstanceTracker = {
                ...batchItem.data,
                retryCount,
                lastFailTime: Date.now()
            };

            this.failedInstances.set(batchItem.id, failedTracker);
            this.stats.failedInstances = this.failedInstances.size;

            console.log(`[*] Added ${batchItem.id} to retry queue (attempt ${retryCount}/${this.config.retryLimit})`);
        } else {
            console.log(`[-] Instance ${batchItem.id} exceeded retry limit, abandoning`);
        }
    }

    // ========================================================================
    // Collection Operations
    // ========================================================================

    /**
     * Check if instance can be collected
     */
    private async checkCanCollect(instancePtr: NativePointer): Promise<boolean> {
        try {
            const canCollectTarget = this.methods.CanCollect?.handle || this.addresses.CanCollect;

            if (!canCollectTarget || canCollectTarget.isNull()) {
                return false;
            }

            const canCollectFunc = new NativeFunction(canCollectTarget, 'int', ['pointer']);
            const result = canCollectFunc(instancePtr);

            return result === 1;

        } catch (error) {
            return false;
        }
    }

    /**
     * Set cleanup flag on instance config
     */
    private async setCleanupFlag(instancePtr: NativePointer): Promise<boolean> {
        try {
            const configTarget = this.methods.Config?.handle || this.addresses.Config;

            if (!configTarget || configTarget.isNull()) {
                return false;
            }

            const configFunc = new NativeFunction(configTarget, 'pointer', ['pointer']);
            const configPtr = configFunc(instancePtr);

            if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                const cleanUpOffset = 0x30;
                configPtr.add(cleanUpOffset).writeU8(1);
                return true;
            }

            return false;

        } catch (error) {
            return false;
        }
    }

    /**
     * Execute FinishCollect on instance
     */
    private async executeFinishCollect(instancePtr: NativePointer): Promise<boolean> {
        try {
            const finishCollectTarget = this.methods.FinishCollect?.handle || this.addresses.FinishCollect;

            if (!finishCollectTarget || finishCollectTarget.isNull()) {
                return false;
            }

            const finishCollectFunc = new NativeFunction(finishCollectTarget, 'void', ['pointer']);
            finishCollectFunc(instancePtr);

            return true;

        } catch (error) {
            return false;
        }
    }

    // ========================================================================
    // Timer Setup and Monitoring
    // ========================================================================

    /**
     * Setup batch processing timer
     */
    private setupBatchProcessingTimer(): void {
        this.batchProcessingInterval = setInterval(() => {
            if (this.phaseTracker.currentPhase === 2) {
                this.processCollectionBatch();
            }
        }, this.config.batchInterval);
    }

    /**
     * Setup status monitoring
     */
    private setupStatusMonitoring(): void {
        this.statusMonitoringInterval = setInterval(() => {
            this.printBatchStatus();
        }, 30000); // Every 30 seconds
    }

    /**
     * Setup cleanup timer
     */
    private setupCleanupTimer(): void {
        this.cleanupInterval = setInterval(() => {
            this.cleanupProcessedInstances();
        }, 300000); // Every 5 minutes
    }

    /**
     * Print batch processing status
     */
    public printBatchStatus(): void {
        console.log("=== BATCH PROCESSING STATUS ===");
        console.log(`Processing Mode: ${this.config.batchSize === 1 ? 'REAL-TIME' : 'BATCH'}`);
        console.log(`Current Phase: ${this.phaseTracker.currentPhase === 1 ? 'DISCOVERY' : 'COLLECTION'}`);
        console.log("Progress:");
        console.log(`  - Successfully processed: ${this.processedInstances.size}`);
        console.log(`  - Discovered instances: ${this.discoveredInstances.size}`);
        console.log(`  - Failed instances: ${this.failedInstances.size}`);
        console.log(`  - Total scans performed: ${this.stats.totalDiscoveryScans}`);
        console.log(`  - Batches completed: ${this.batchNumber}`);

        if (this.failedInstances.size > 0) {
            console.log("Failed instances details:");
            this.failedInstances.forEach((data, instanceId) => {
                console.log(`  ${instanceId} - Retries: ${data.retryCount}/${this.config.retryLimit}`);
            });
        }

        if (this.processedInstances.size >= this.config.maxWaitTime) {
            console.log("[+] TARGET ACHIEVED: Processing target reached!");
        }
        console.log("====================================");
    }

    /**
     * Cleanup old processed instances to prevent memory bloat
     */
    private cleanupProcessedInstances(): void {
        if (this.processedInstances.size > 1000) {
            console.log("[*] Cleaning up old processed instances...");
            const processedArray = Array.from(this.processedInstances);
            // Keep only the most recent 500
            this.processedInstances.clear();
            for (let i = processedArray.length - 500; i < processedArray.length; i++) {
                if (i >= 0) {
                    this.processedInstances.add(processedArray[i]);
                }
            }
            console.log(`[+] Cleaned up processed instances, kept ${this.processedInstances.size}`);
        }
    }

    // ========================================================================
    // Callback Registration
    // ========================================================================

    /**
     * Register callback for batch completion events
     */
    public onBatchComplete(callback: (result: BatchProcessingResult) => void): void {
        this.onBatchCompleteCallback = callback;
    }

    /**
     * Register callback for phase change events
     */
    public onPhaseChange(callback: (phase: 1 | 2) => void): void {
        this.onPhaseChangeCallback = callback;
    }

    /**
     * Register callback for instance processing events
     */
    public onInstanceProcessed(callback: (result: CollectionResult) => void): void {
        this.onInstanceProcessedCallback = callback;
    }

    // ========================================================================
    // Getters
    // ========================================================================

    /**
     * Get current batch statistics
     */
    public getStats(): BatchStats {
        return { ...this.stats };
    }

    /**
     * Get current phase information
     */
    public getPhaseInfo(): PhaseTracker {
        return { ...this.phaseTracker };
    }

    /**
     * Get processing queue sizes
     */
    public getQueueSizes(): { discovered: number, processed: number, failed: number } {
        return {
            discovered: this.discoveredInstances.size,
            processed: this.processedInstances.size,
            failed: this.failedInstances.size
        };
    }

    /**
     * Clear all processing queues
     */
    public clearAllQueues(): void {
        this.discoveredInstances.clear();
        this.processedInstances.clear();
        this.failedInstances.clear();

        this.stats.discoveredInstances = 0;
        this.stats.processedInstances = 0;
        this.stats.failedInstances = 0;

        console.log("[+] All processing queues cleared");
    }
}